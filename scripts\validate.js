#!/usr/bin/env node

/**
 * Validation Script
 * Validates the trading bot application setup and configuration
 */

const fs = require('fs');
const path = require('path');

class ApplicationValidator {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.info = [];
        this.rootDir = path.join(__dirname, '..');
    }

    /**
     * Run all validations
     */
    async validate() {
        console.log('🔍 Validating Trading Bot Application...\n');

        this.validateFileStructure();
        this.validatePackageJson();
        this.validateDependencies();
        this.validateConfiguration();
        this.validateSourceFiles();
        this.validateTests();
        this.validateBuildConfiguration();

        this.printResults();
        return this.errors.length === 0;
    }

    /**
     * Validate file structure
     */
    validateFileStructure() {
        console.log('📁 Validating file structure...');

        const requiredFiles = [
            'package.json',
            'README.md',
            '.gitignore',
            '.eslintrc.js',
            '.prettierrc.js',
            'jest.config.js',
            'src/main/main.js',
            'src/main/preload.js',
            'src/renderer/index.html',
            'src/renderer/js/app.js',
            'src/renderer/styles/main.css'
        ];

        const requiredDirectories = [
            'src',
            'src/main',
            'src/renderer',
            'src/renderer/js',
            'src/renderer/js/components',
            'src/renderer/js/services',
            'src/renderer/js/utils',
            'src/renderer/styles',
            'src/services',
            'test',
            'scripts'
        ];

        // Check required files
        requiredFiles.forEach(file => {
            const filePath = path.join(this.rootDir, file);
            if (!fs.existsSync(filePath)) {
                this.errors.push(`Missing required file: ${file}`);
            } else {
                this.info.push(`✓ Found: ${file}`);
            }
        });

        // Check required directories
        requiredDirectories.forEach(dir => {
            const dirPath = path.join(this.rootDir, dir);
            if (!fs.existsSync(dirPath)) {
                this.errors.push(`Missing required directory: ${dir}`);
            } else {
                this.info.push(`✓ Found directory: ${dir}`);
            }
        });
    }

    /**
     * Validate package.json
     */
    validatePackageJson() {
        console.log('📦 Validating package.json...');

        const packagePath = path.join(this.rootDir, 'package.json');
        if (!fs.existsSync(packagePath)) {
            this.errors.push('package.json not found');
            return;
        }

        try {
            const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

            // Check required fields
            const requiredFields = ['name', 'version', 'description', 'main', 'scripts'];
            requiredFields.forEach(field => {
                if (!packageJson[field]) {
                    this.errors.push(`Missing required field in package.json: ${field}`);
                } else {
                    this.info.push(`✓ Found package.json field: ${field}`);
                }
            });

            // Check required scripts
            const requiredScripts = ['start', 'dev', 'build', 'test'];
            requiredScripts.forEach(script => {
                if (!packageJson.scripts || !packageJson.scripts[script]) {
                    this.warnings.push(`Missing recommended script: ${script}`);
                } else {
                    this.info.push(`✓ Found script: ${script}`);
                }
            });

            // Check main entry point
            if (packageJson.main && !fs.existsSync(path.join(this.rootDir, packageJson.main))) {
                this.errors.push(`Main entry point not found: ${packageJson.main}`);
            }

        } catch (error) {
            this.errors.push(`Invalid package.json: ${error.message}`);
        }
    }

    /**
     * Validate dependencies
     */
    validateDependencies() {
        console.log('📚 Validating dependencies...');

        const packagePath = path.join(this.rootDir, 'package.json');
        if (!fs.existsSync(packagePath)) {
            return;
        }

        try {
            const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            const dependencies = packageJson.dependencies || {};
            const devDependencies = packageJson.devDependencies || {};

            // Check for required dependencies
            const requiredDeps = ['electron', 'axios', 'electron-store', 'node-cron', 'moment'];
            requiredDeps.forEach(dep => {
                if (!dependencies[dep] && !devDependencies[dep]) {
                    this.warnings.push(`Missing recommended dependency: ${dep}`);
                } else {
                    this.info.push(`✓ Found dependency: ${dep}`);
                }
            });

            // Check for development dependencies
            const requiredDevDeps = ['jest', 'eslint', 'prettier', 'electron-builder'];
            requiredDevDeps.forEach(dep => {
                if (!devDependencies[dep]) {
                    this.warnings.push(`Missing recommended dev dependency: ${dep}`);
                } else {
                    this.info.push(`✓ Found dev dependency: ${dep}`);
                }
            });

            // Check if node_modules exists
            const nodeModulesPath = path.join(this.rootDir, 'node_modules');
            if (!fs.existsSync(nodeModulesPath)) {
                this.warnings.push('node_modules directory not found. Run "npm install" to install dependencies.');
            } else {
                this.info.push('✓ node_modules directory found');
            }

        } catch (error) {
            this.errors.push(`Error reading dependencies: ${error.message}`);
        }
    }

    /**
     * Validate configuration files
     */
    validateConfiguration() {
        console.log('⚙️ Validating configuration files...');

        // Check ESLint config
        const eslintPath = path.join(this.rootDir, '.eslintrc.js');
        if (fs.existsSync(eslintPath)) {
            try {
                require(eslintPath);
                this.info.push('✓ ESLint configuration is valid');
            } catch (error) {
                this.errors.push(`Invalid ESLint configuration: ${error.message}`);
            }
        }

        // Check Prettier config
        const prettierPath = path.join(this.rootDir, '.prettierrc.js');
        if (fs.existsSync(prettierPath)) {
            try {
                require(prettierPath);
                this.info.push('✓ Prettier configuration is valid');
            } catch (error) {
                this.errors.push(`Invalid Prettier configuration: ${error.message}`);
            }
        }

        // Check Jest config
        const jestPath = path.join(this.rootDir, 'jest.config.js');
        if (fs.existsSync(jestPath)) {
            try {
                require(jestPath);
                this.info.push('✓ Jest configuration is valid');
            } catch (error) {
                this.errors.push(`Invalid Jest configuration: ${error.message}`);
            }
        }
    }

    /**
     * Validate source files
     */
    validateSourceFiles() {
        console.log('📝 Validating source files...');

        const sourceFiles = [
            'src/main/main.js',
            'src/main/preload.js',
            'src/renderer/js/app.js',
            'src/services/authService.js',
            'src/services/marketDataService.js',
            'src/services/orderService.js',
            'src/services/portfolioService.js',
            'src/services/tradingEngine.js',
            'src/services/websocketService.js',
            'src/services/optionChainService.js'
        ];

        sourceFiles.forEach(file => {
            const filePath = path.join(this.rootDir, file);
            if (fs.existsSync(filePath)) {
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    
                    // Basic syntax check
                    if (content.trim().length === 0) {
                        this.warnings.push(`Empty source file: ${file}`);
                    } else {
                        this.info.push(`✓ Source file exists and has content: ${file}`);
                    }

                    // Check for common patterns
                    if (file.includes('Service.js') && !content.includes('class ')) {
                        this.warnings.push(`Service file should contain a class: ${file}`);
                    }

                } catch (error) {
                    this.errors.push(`Error reading source file ${file}: ${error.message}`);
                }
            } else {
                this.errors.push(`Missing source file: ${file}`);
            }
        });
    }

    /**
     * Validate test files
     */
    validateTests() {
        console.log('🧪 Validating test files...');

        const testDir = path.join(this.rootDir, 'test');
        if (fs.existsSync(testDir)) {
            const testFiles = fs.readdirSync(testDir).filter(file => file.endsWith('.test.js'));
            
            if (testFiles.length === 0) {
                this.warnings.push('No test files found in test directory');
            } else {
                this.info.push(`✓ Found ${testFiles.length} test file(s)`);
                
                testFiles.forEach(file => {
                    const filePath = path.join(testDir, file);
                    try {
                        const content = fs.readFileSync(filePath, 'utf8');
                        if (content.includes('describe(') && content.includes('it(')) {
                            this.info.push(`✓ Test file has proper structure: ${file}`);
                        } else {
                            this.warnings.push(`Test file may not have proper structure: ${file}`);
                        }
                    } catch (error) {
                        this.errors.push(`Error reading test file ${file}: ${error.message}`);
                    }
                });
            }
        } else {
            this.warnings.push('Test directory not found');
        }
    }

    /**
     * Validate build configuration
     */
    validateBuildConfiguration() {
        console.log('🔨 Validating build configuration...');

        const packagePath = path.join(this.rootDir, 'package.json');
        if (fs.existsSync(packagePath)) {
            try {
                const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
                
                if (packageJson.build) {
                    this.info.push('✓ Electron builder configuration found');
                    
                    // Check for required build fields
                    const buildConfig = packageJson.build;
                    if (buildConfig.appId) {
                        this.info.push('✓ App ID configured');
                    } else {
                        this.warnings.push('App ID not configured in build settings');
                    }
                    
                    if (buildConfig.directories) {
                        this.info.push('✓ Build directories configured');
                    }
                    
                } else {
                    this.warnings.push('Electron builder configuration not found');
                }
                
            } catch (error) {
                this.errors.push(`Error reading build configuration: ${error.message}`);
            }
        }
    }

    /**
     * Print validation results
     */
    printResults() {
        console.log('\n📊 Validation Results:');
        console.log('='.repeat(50));

        if (this.errors.length > 0) {
            console.log('\n❌ Errors:');
            this.errors.forEach(error => console.log(`  • ${error}`));
        }

        if (this.warnings.length > 0) {
            console.log('\n⚠️  Warnings:');
            this.warnings.forEach(warning => console.log(`  • ${warning}`));
        }

        if (this.info.length > 0 && process.env.VERBOSE) {
            console.log('\n✅ Info:');
            this.info.forEach(info => console.log(`  • ${info}`));
        }

        console.log('\n📈 Summary:');
        console.log(`  Errors: ${this.errors.length}`);
        console.log(`  Warnings: ${this.warnings.length}`);
        console.log(`  Info: ${this.info.length}`);

        if (this.errors.length === 0) {
            console.log('\n🎉 Validation passed! The application appears to be properly configured.');
        } else {
            console.log('\n💥 Validation failed! Please fix the errors above before proceeding.');
        }

        console.log('\n💡 Next steps:');
        if (this.errors.length === 0) {
            console.log('  1. Run "npm install" to install dependencies');
            console.log('  2. Run "npm test" to execute tests');
            console.log('  3. Run "npm start" to launch the application');
            console.log('  4. Configure your API credentials in the settings');
        } else {
            console.log('  1. Fix the errors listed above');
            console.log('  2. Run this validation script again');
            console.log('  3. Proceed with installation and testing');
        }
    }
}

// Run validation if this script is executed directly
if (require.main === module) {
    const validator = new ApplicationValidator();
    validator.validate().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('Validation failed with error:', error);
        process.exit(1);
    });
}

module.exports = ApplicationValidator;
