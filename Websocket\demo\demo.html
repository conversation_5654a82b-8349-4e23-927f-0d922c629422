<!DOCTYPE html>
<html lang="en">
<head>
	<title>Bootstrap Example</title>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
	
	<script src="../hslib.js" type="text/javascript"></script>
	<script src="./demo.js" type="text/javascript"></script>
	<script src="./neo.js" type="text/javascript"></script>
	
	<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
	<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
	
</head>
<body>

<div class="container-fluid">
  <h1>Subscribing to HSM demo</h1>
  <p>You can use this file to get Live Broadcast of a scrip, Pause, Unpause, Resume etc</p>
  
  
  <div class="row">
    
	
    <div class="col-sm-12" style="background-color:MediumSeaGreen;">
		Token <input type="text" id="token_id" value=""  /> <br />
		SID   <input type="text" id="sid" value="" /> <br />
		<!-- HandshakeServerId <input type="text" id="serverid" value="" /> <i>eg. server32</i><br />  -->
		Data Center(select Your Data Center)  
		<select name="DataCenter" id="datacenter_id">
			<option value="gdc">gdc</option>
			<option value="gdcd">gdcd</option>
			<option value="adc">adc</option>
			<option value="e21">E21</option>
			<option value="e22">E22</option>
			<option value="e41">E41</option>
			<option value="e43">E43</option>
		</select>

		<br />
	</div>
    
	
	
	</div>
	
	<div class="row" class="text-primary">
	
	<div class="col-sm-3">
	
		Do Action on Channel #<br /> 
		<input type="text" id="channel_number" value="1" />
		<br />
		<br/>

		<br />
	
		<input type="button" id="connect_hsm" class="bg-info" value="Connect HSM" onclick="wconnect('Hsm')" /> 
		<input type="button" id="connect_hsi" value="Connect HSI" onclick="wconnect('Hsi')" /> <br />
		<br />
		
		Channels <br />
		<input type="button" id="pause_channel" class="bg-danger" value="Pause " onclick="resumeandpause('cp',document.getElementById('channel_number').value)" />
	    
		<input type="button" id="resume_channel" class="bg-success" value="Resume" onclick="resumeandpause('cr',document.getElementById('channel_number').value)" /> <br />
		   
	</div>
	<div class="col-sm-3">
		   Stream for Scrips(input=exchange_identifier)  <br /> 
		   <textarea id="sub_scrips" value="">nse_cm|11536&nse_cm|1594&nse_cm|3456</textarea> <br />
		     
		   <input type="button" value="Subscribe Scrip" rows="5" cols="100" onclick="wsub('mws','sub_scrips','');" /><br />
		   
	</div>
	<div class="col-sm-3">
		   Stream Indices <br /> 
		   <textarea id="sub_indices">nse_cm|Nifty 50&nse_cm|Nifty Realty</textarea><br />
		   <input type="button" value="Subscribe Index" rows="5" cols="100" onclick="wsub('ifs','sub_indices','');" /><br />
        
		   
	</div>
	<div class="col-sm-3">
		   Stream MD  <br /> 
		   <textarea id="sub_depth">nse_cm|11536&nse_cm|11000&nse_cm|11001</textarea><br />
		   <input type="button" value="Subscribe Depth" rows="5" cols="100" onclick="wsub('dps','sub_depth','');" /><br />
		
		   
	</div>
	
    </div>
	
	<div class="row">
	
		<div class="col-sm-3">
			
			   
		</div>
		<div class="col-sm-9">
			Streaming ... Scrips <br />
			<textarea id="stream_scrips" rows="10" cols="100"></textarea>
		<br />
		Streaming ... Orders <br />
			<textarea id="stream_scrips1" rows="10" cols="100"></textarea>
			
			<br />
		</div>
	
    </div>
	
	<hr />
	
	

	</div>
	
  </div>
</div>
<p>
For HandshakeServerId - Call POST https://lhsi.kotaksecurities.com/quick/user/handshake?sId=server34

</p>
</body>
</html>
