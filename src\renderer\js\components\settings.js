/**
 * Settings Component
 * Handles the settings modal and configuration management
 */
class Settings {
    constructor(app) {
        this.app = app;
        this.currentSettings = {};
        this.isVisible = false;
        
        this.init();
    }

    init() {
        this.createSettingsUI();
        this.setupEventListeners();
    }

    createSettingsUI() {
        const modalBody = document.querySelector('#settings-modal .modal-body');
        if (!modalBody) return;

        modalBody.innerHTML = `
            <div class="settings-container">
                <div class="settings-tabs">
                    <button class="settings-tab active" data-tab="credentials">
                        <i class="fas fa-key"></i>
                        API Credentials
                    </button>
                    <button class="settings-tab" data-tab="trading">
                        <i class="fas fa-chart-line"></i>
                        Trading Settings
                    </button>
                    <button class="settings-tab" data-tab="risk">
                        <i class="fas fa-shield-alt"></i>
                        Risk Management
                    </button>
                    <button class="settings-tab" data-tab="notifications">
                        <i class="fas fa-bell"></i>
                        Notifications
                    </button>
                </div>

                <div class="settings-content">
                    <!-- API Credentials Tab -->
                    <div class="settings-tab-content active" id="credentials-tab">
                        <h4>Kotak Neo Trade API Credentials</h4>
                        <p class="settings-description">
                            Enter your Kotak Neo Trade API credentials. These will be stored securely on your device.
                        </p>
                        <div class="credential-help">
                            <h5>📋 How to get your credentials:</h5>
                            <ol>
                                <li>Log in to your <strong>Kotak Neo Trade</strong> account</li>
                                <li>Go to <strong>Settings → API Management</strong></li>
                                <li>Apply for API access (if not done already)</li>
                                <li>Copy the <strong>Consumer Key</strong> and <strong>Consumer Secret</strong></li>
                                <li>Your <strong>UCC</strong> is your Unique Client Code (e.g., YOJE0)</li>
                                <li>Use your registered <strong>mobile number</strong> (10 digits only)</li>
                            </ol>
                            <p><strong>Note:</strong> You'll need TOTP (6-digit code) for login and MPIN (4-6 digits) for trading.</p>
                        </div>
                        
                        <form id="credentials-form" class="settings-form">
                            <div class="form-group">
                                <label for="ucc">UCC (Unique Client Code) *</label>
                                <input type="text" id="ucc" name="ucc" required
                                       placeholder="Enter your UCC (e.g., YOJE0)">
                                <small class="form-help">Your unique client code from Kotak Securities</small>
                            </div>

                            <div class="form-group">
                                <label for="mobile-number">Mobile Number *</label>
                                <input type="tel" id="mobile-number" name="mobileNumber" required
                                       placeholder="10-digit mobile number" pattern="[6-9][0-9]{9}">
                                <small class="form-help">Registered mobile number (without +91)</small>
                            </div>
                            
                            <div class="form-group">
                                <label for="consumer-key">Consumer Key</label>
                                <input type="text" id="consumer-key" name="consumerKey" required 
                                       placeholder="Enter your API Consumer Key">
                            </div>
                            
                            <div class="form-group">
                                <label for="consumer-secret">Consumer Secret</label>
                                <input type="password" id="consumer-secret" name="consumerSecret" required 
                                       placeholder="Enter your API Consumer Secret">
                            </div>
                            
                            <div class="form-group">
                                <label for="environment">Environment</label>
                                <select id="environment" name="environment">
                                    <option value="prod">Production</option>
                                    <option value="uat">UAT (Testing)</option>
                                </select>
                            </div>
                            
                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" id="test-credentials">
                                    <i class="fas fa-vial"></i>
                                    Test Connection
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Save Credentials
                                </button>
                            </div>
                        </form>
                        
                        <div class="connection-status" id="connection-status" style="display: none;">
                            <div class="status-message"></div>
                        </div>
                    </div>

                    <!-- Trading Settings Tab -->
                    <div class="settings-tab-content" id="trading-tab">
                        <h4>Trading Configuration</h4>
                        
                        <form id="trading-form" class="settings-form">
                            <div class="form-group">
                                <label for="default-symbol">Default Symbol</label>
                                <select id="default-symbol" name="defaultSymbol">
                                    <option value="NIFTY">Nifty 50</option>
                                    <option value="BANKNIFTY">Bank Nifty</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="default-lots">Default Lot Size</label>
                                <input type="number" id="default-lots" name="defaultLots" min="1" max="100" value="1">
                            </div>
                            
                            <div class="form-group">
                                <label for="auto-square-off">Auto Square Off Time</label>
                                <input type="time" id="auto-square-off" name="autoSquareOffTime" value="15:20">
                                <small>Automatically close all positions before market close</small>
                            </div>
                            
                            <div class="form-group">
                                <label for="order-type">Default Order Type</label>
                                <select id="order-type" name="defaultOrderType">
                                    <option value="MKT">Market Order</option>
                                    <option value="LMT">Limit Order</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="product-type">Default Product Type</label>
                                <select id="product-type" name="defaultProductType">
                                    <option value="NRML">Normal</option>
                                    <option value="MIS">Intraday</option>
                                </select>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Save Trading Settings
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Risk Management Tab -->
                    <div class="settings-tab-content" id="risk-tab">
                        <h4>Risk Management</h4>
                        
                        <form id="risk-form" class="settings-form">
                            <div class="form-group">
                                <label for="max-loss-per-day">Maximum Loss Per Day (₹)</label>
                                <input type="number" id="max-loss-per-day" name="maxLossPerDay" min="0" step="100" value="5000">
                                <small>Trading will stop if daily loss exceeds this amount</small>
                            </div>
                            
                            <div class="form-group">
                                <label for="max-profit-per-day">Maximum Profit Per Day (₹)</label>
                                <input type="number" id="max-profit-per-day" name="maxProfitPerDay" min="0" step="100" value="10000">
                                <small>Trading will stop if daily profit exceeds this amount</small>
                            </div>
                            
                            <div class="form-group">
                                <label for="max-trades-per-day">Maximum Trades Per Day</label>
                                <input type="number" id="max-trades-per-day" name="maxTradesPerDay" min="1" max="100" value="10">
                            </div>
                            
                            <div class="form-group">
                                <label for="default-stop-loss">Default Stop Loss (%)</label>
                                <input type="number" id="default-stop-loss" name="defaultStopLoss" min="0.1" max="50" step="0.1" value="2">
                            </div>
                            
                            <div class="form-group">
                                <label for="default-target">Default Target (%)</label>
                                <input type="number" id="default-target" name="defaultTarget" min="0.1" max="100" step="0.1" value="5">
                            </div>
                            
                            <div class="form-group">
                                <label for="position-sizing">Position Sizing Method</label>
                                <select id="position-sizing" name="positionSizing">
                                    <option value="fixed">Fixed Lot Size</option>
                                    <option value="percentage">Percentage of Capital</option>
                                    <option value="risk">Risk-based Sizing</option>
                                </select>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Save Risk Settings
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Notifications Tab -->
                    <div class="settings-tab-content" id="notifications-tab">
                        <h4>Notification Settings</h4>
                        
                        <form id="notifications-form" class="settings-form">
                            <div class="form-group checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="notify-orders" name="notifyOrders" checked>
                                    <span class="checkmark"></span>
                                    Order Notifications
                                </label>
                                <small>Get notified when orders are placed, executed, or cancelled</small>
                            </div>
                            
                            <div class="form-group checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="notify-pnl" name="notifyPnL" checked>
                                    <span class="checkmark"></span>
                                    P&L Notifications
                                </label>
                                <small>Get notified about significant profit or loss events</small>
                            </div>
                            
                            <div class="form-group checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="notify-risk" name="notifyRisk" checked>
                                    <span class="checkmark"></span>
                                    Risk Alerts
                                </label>
                                <small>Get notified when risk limits are approached or breached</small>
                            </div>
                            
                            <div class="form-group checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="notify-system" name="notifySystem" checked>
                                    <span class="checkmark"></span>
                                    System Notifications
                                </label>
                                <small>Get notified about system events and errors</small>
                            </div>
                            
                            <div class="form-group">
                                <label for="notification-sound">Notification Sound</label>
                                <select id="notification-sound" name="notificationSound">
                                    <option value="default">Default</option>
                                    <option value="chime">Chime</option>
                                    <option value="bell">Bell</option>
                                    <option value="none">None</option>
                                </select>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Save Notification Settings
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // Tab switching
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('settings-tab')) {
                this.switchTab(e.target.dataset.tab);
            }
        });

        // Form submissions
        document.getElementById('credentials-form')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCredentials();
        });

        document.getElementById('trading-form')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveTradingSettings();
        });

        document.getElementById('risk-form')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveRiskSettings();
        });

        document.getElementById('notifications-form')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveNotificationSettings();
        });

        // Test credentials
        document.getElementById('test-credentials')?.addEventListener('click', () => {
            this.testCredentials();
        });
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.settings-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.settings-tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');
    }

    async show() {
        this.isVisible = true;
        await this.loadSettings();
    }

    hide() {
        this.isVisible = false;
    }

    async loadSettings() {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.getSettings();
                if (result.success) {
                    this.currentSettings = result.settings;
                    this.populateForm();
                }
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }

    populateForm() {
        // Populate credentials form (excluding sensitive data)
        if (this.currentSettings.credentials) {
            const creds = this.currentSettings.credentials;
            document.getElementById('user-id').value = creds.userId || '';
            document.getElementById('mobile-number').value = creds.mobileNumber || '';
            document.getElementById('consumer-key').value = creds.consumerKey || '';
            document.getElementById('environment').value = creds.environment || 'prod';
        }

        // Populate trading settings
        if (this.currentSettings.trading) {
            const trading = this.currentSettings.trading;
            document.getElementById('default-symbol').value = trading.defaultSymbol || 'NIFTY';
            document.getElementById('default-lots').value = trading.defaultLots || 1;
            document.getElementById('auto-square-off').value = trading.autoSquareOffTime || '15:20';
            document.getElementById('order-type').value = trading.defaultOrderType || 'MKT';
            document.getElementById('product-type').value = trading.defaultProductType || 'MIS';
        }

        // Populate risk settings
        if (this.currentSettings.risk) {
            const risk = this.currentSettings.risk;
            document.getElementById('max-loss-per-day').value = risk.maxLossPerDay || 5000;
            document.getElementById('max-profit-per-day').value = risk.maxProfitPerDay || 10000;
            document.getElementById('max-trades-per-day').value = risk.maxTradesPerDay || 10;
            document.getElementById('default-stop-loss').value = risk.defaultStopLoss || 2;
            document.getElementById('default-target').value = risk.defaultTarget || 5;
            document.getElementById('position-sizing').value = risk.positionSizing || 'fixed';
        }

        // Populate notification settings
        if (this.currentSettings.notifications) {
            const notif = this.currentSettings.notifications;
            document.getElementById('notify-orders').checked = notif.notifyOrders !== false;
            document.getElementById('notify-pnl').checked = notif.notifyPnL !== false;
            document.getElementById('notify-risk').checked = notif.notifyRisk !== false;
            document.getElementById('notify-system').checked = notif.notifySystem !== false;
            document.getElementById('notification-sound').value = notif.notificationSound || 'default';
        }
    }

    async saveCredentials() {
        try {
            const formData = new FormData(document.getElementById('credentials-form'));
            const credentials = Object.fromEntries(formData);

            if (window.electronAPI) {
                const result = await window.electronAPI.saveSettings({
                    ...this.currentSettings,
                    credentials
                });

                if (result.success) {
                    this.showNotification('Credentials saved successfully', 'success');
                    this.currentSettings.credentials = credentials;
                } else {
                    this.showNotification('Failed to save credentials', 'error');
                }
            }
        } catch (error) {
            console.error('Failed to save credentials:', error);
            this.showNotification('Failed to save credentials', 'error');
        }
    }

    async saveTradingSettings() {
        try {
            const formData = new FormData(document.getElementById('trading-form'));
            const trading = Object.fromEntries(formData);

            if (window.electronAPI) {
                const result = await window.electronAPI.saveSettings({
                    ...this.currentSettings,
                    trading
                });

                if (result.success) {
                    this.showNotification('Trading settings saved successfully', 'success');
                    this.currentSettings.trading = trading;
                } else {
                    this.showNotification('Failed to save trading settings', 'error');
                }
            }
        } catch (error) {
            console.error('Failed to save trading settings:', error);
            this.showNotification('Failed to save trading settings', 'error');
        }
    }

    async saveRiskSettings() {
        try {
            const formData = new FormData(document.getElementById('risk-form'));
            const risk = Object.fromEntries(formData);

            if (window.electronAPI) {
                const result = await window.electronAPI.saveSettings({
                    ...this.currentSettings,
                    risk
                });

                if (result.success) {
                    this.showNotification('Risk settings saved successfully', 'success');
                    this.currentSettings.risk = risk;
                } else {
                    this.showNotification('Failed to save risk settings', 'error');
                }
            }
        } catch (error) {
            console.error('Failed to save risk settings:', error);
            this.showNotification('Failed to save risk settings', 'error');
        }
    }

    async saveNotificationSettings() {
        try {
            const form = document.getElementById('notifications-form');
            const formData = new FormData(form);
            const notifications = Object.fromEntries(formData);
            
            // Handle checkboxes
            notifications.notifyOrders = document.getElementById('notify-orders').checked;
            notifications.notifyPnL = document.getElementById('notify-pnl').checked;
            notifications.notifyRisk = document.getElementById('notify-risk').checked;
            notifications.notifySystem = document.getElementById('notify-system').checked;

            if (window.electronAPI) {
                const result = await window.electronAPI.saveSettings({
                    ...this.currentSettings,
                    notifications
                });

                if (result.success) {
                    this.showNotification('Notification settings saved successfully', 'success');
                    this.currentSettings.notifications = notifications;
                } else {
                    this.showNotification('Failed to save notification settings', 'error');
                }
            }
        } catch (error) {
            console.error('Failed to save notification settings:', error);
            this.showNotification('Failed to save notification settings', 'error');
        }
    }

    async authenticate() {
        try {
            const totpCode = document.getElementById('totp-code').value;
            if (!totpCode || totpCode.length !== 6) {
                this.showNotification('Please enter a valid 6-digit TOTP code', 'error');
                return;
            }

            const authBtn = document.getElementById('authenticate-btn');
            authBtn.disabled = true;
            authBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Authenticating...';

            if (window.electronAPI) {
                const result = await window.electronAPI.authenticate({ totp: totpCode });
                
                if (result.success) {
                    this.updateAuthStatus(true);
                    this.showNotification('Authentication successful', 'success');
                    this.app.isAuthenticated = true;
                    document.getElementById('totp-code').value = '';
                } else {
                    this.updateAuthStatus(false);
                    this.showNotification(result.message || 'Authentication failed', 'error');
                }
            }
        } catch (error) {
            console.error('Authentication failed:', error);
            this.updateAuthStatus(false);
            this.showNotification('Authentication failed', 'error');
        } finally {
            const authBtn = document.getElementById('authenticate-btn');
            authBtn.disabled = false;
            authBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Authenticate';
        }
    }

    async testCredentials() {
        try {
            const testBtn = document.getElementById('test-credentials');
            testBtn.disabled = true;
            testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';

            // Save credentials first
            await this.saveCredentials();

            // Test connection
            if (window.electronAPI) {
                const result = await window.electronAPI.testConnection();
                
                if (result.success) {
                    this.showNotification('Connection test successful', 'success');
                } else {
                    this.showNotification(result.message || 'Connection test failed', 'error');
                }
            }
        } catch (error) {
            console.error('Connection test failed:', error);
            this.showNotification('Connection test failed', 'error');
        } finally {
            const testBtn = document.getElementById('test-credentials');
            testBtn.disabled = false;
            testBtn.innerHTML = '<i class="fas fa-vial"></i> Test Connection';
        }
    }

    updateAuthStatus(isAuthenticated) {
        const statusElement = document.getElementById('auth-status');
        const indicator = statusElement.querySelector('.status-indicator');
        const text = statusElement.querySelector('.status-text');

        if (isAuthenticated) {
            indicator.style.backgroundColor = 'var(--success-color)';
            text.textContent = 'Authenticated';
        } else {
            indicator.style.backgroundColor = 'var(--danger-color)';
            text.textContent = 'Not authenticated';
        }
    }

    showNotification(message, type) {
        // This will be implemented in the notification system
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}

// Export for use in main app
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Settings;
}
