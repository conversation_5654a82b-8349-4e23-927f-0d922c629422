module.exports = {
  // Print width
  printWidth: 120,
  
  // Tab width
  tabWidth: 4,
  
  // Use tabs instead of spaces
  useTabs: false,
  
  // Semicolons
  semi: true,
  
  // Single quotes
  singleQuote: true,
  
  // Quote properties
  quoteProps: 'as-needed',
  
  // JSX quotes
  jsxSingleQuote: true,
  
  // Trailing commas
  trailingComma: 'none',
  
  // Bracket spacing
  bracketSpacing: true,
  
  // Bracket same line
  bracketSameLine: false,
  
  // Arrow function parentheses
  arrowParens: 'avoid',
  
  // Range formatting
  rangeStart: 0,
  rangeEnd: Infinity,
  
  // Parser
  parser: undefined,
  
  // File path
  filepath: undefined,
  
  // Require pragma
  requirePragma: false,
  
  // Insert pragma
  insertPragma: false,
  
  // Prose wrap
  proseWrap: 'preserve',
  
  // HTML whitespace sensitivity
  htmlWhitespaceSensitivity: 'css',
  
  // Vue files script and style tags indentation
  vueIndentScriptAndStyle: false,
  
  // End of line
  endOfLine: 'lf',
  
  // Embedded language formatting
  embeddedLanguageFormatting: 'auto',
  
  // Single attribute per line
  singleAttributePerLine: false,
  
  // Override for specific file types
  overrides: [
    {
      files: '*.json',
      options: {
        tabWidth: 2
      }
    },
    {
      files: '*.md',
      options: {
        proseWrap: 'always',
        printWidth: 80
      }
    },
    {
      files: '*.html',
      options: {
        tabWidth: 2,
        printWidth: 100
      }
    },
    {
      files: '*.css',
      options: {
        tabWidth: 2,
        singleQuote: false
      }
    },
    {
      files: '*.yml',
      options: {
        tabWidth: 2
      }
    },
    {
      files: '*.yaml',
      options: {
        tabWidth: 2
      }
    }
  ]
};
