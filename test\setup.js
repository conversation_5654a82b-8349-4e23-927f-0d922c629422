/**
 * Jest Test Setup
 * Global setup and mocks for testing
 */

// Mock Electron APIs
const mockElectronAPI = {
  // Authentication
  authenticate: jest.fn(),
  logout: jest.fn(),
  getUserProfile: jest.fn(),
  
  // Market Data
  getMarketData: jest.fn(),
  getOptionChain: jest.fn(),
  getAvailableExpiries: jest.fn(),
  getMarketDepth: jest.fn(),
  getHistoricalData: jest.fn(),
  searchInstruments: jest.fn(),
  
  // Orders
  placeOrder: jest.fn(),
  modifyOrder: jest.fn(),
  cancelOrder: jest.fn(),
  getOrderBook: jest.fn(),
  getOrderHistory: jest.fn(),
  getScheduledOrders: jest.fn(),
  cancelScheduledOrder: jest.fn(),
  
  // Portfolio
  getPortfolio: jest.fn(),
  getPositions: jest.fn(),
  getHoldings: jest.fn(),
  getPortfolioSummary: jest.fn(),
  
  // Trading Engine
  startTradingEngine: jest.fn(),
  stopTradingEngine: jest.fn(),
  getTradingStatus: jest.fn(),
  updateRiskLimits: jest.fn(),
  emergencyStop: jest.fn(),
  
  // WebSocket
  subscribeMarketData: jest.fn(),
  unsubscribeMarketData: jest.fn(),
  initializeWebSocket: jest.fn(),
  reconnectWebSocket: jest.fn(),
  disconnectWebSocket: jest.fn(),
  
  // Settings
  saveSettings: jest.fn(),
  getSettings: jest.fn(),
  testConnection: jest.fn(),
  
  // Utilities
  showNotification: jest.fn(),
  showErrorDialog: jest.fn(),
  showConfirmDialog: jest.fn(),
  exportData: jest.fn(),
  importData: jest.fn(),
  logInfo: jest.fn(),
  logError: jest.fn(),
  logWarning: jest.fn(),
  
  // Event listeners
  onMarketDataUpdate: jest.fn(),
  onOrderUpdate: jest.fn(),
  onPortfolioUpdate: jest.fn(),
  onTradingEngineUpdate: jest.fn(),
  onNotification: jest.fn(),
  onOpenSettings: jest.fn(),
  onStartTrading: jest.fn(),
  onStopTrading: jest.fn(),
  onViewPortfolio: jest.fn()
};

// Mock window object
global.window = {
  electronAPI: mockElectronAPI,
  location: {
    href: 'http://localhost:3000'
  },
  document: {
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    createElement: jest.fn(() => ({
      classList: {
        add: jest.fn(),
        remove: jest.fn(),
        contains: jest.fn()
      },
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      setAttribute: jest.fn(),
      getAttribute: jest.fn(),
      appendChild: jest.fn(),
      removeChild: jest.fn(),
      querySelector: jest.fn(),
      querySelectorAll: jest.fn()
    })),
    getElementById: jest.fn(),
    querySelector: jest.fn(),
    querySelectorAll: jest.fn(),
    body: {
      appendChild: jest.fn(),
      removeChild: jest.fn()
    }
  },
  localStorage: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
  },
  sessionStorage: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
  },
  console: {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn()
  }
};

// Mock document object
global.document = global.window.document;

// Mock console
global.console = {
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn()
};

// Mock Date for consistent testing
const mockDate = new Date('2023-10-25T10:00:00.000Z');
global.Date = jest.fn(() => mockDate);
global.Date.now = jest.fn(() => mockDate.getTime());

// Mock setTimeout and setInterval
global.setTimeout = jest.fn((fn, delay) => {
  return setTimeout(fn, 0); // Execute immediately in tests
});

global.setInterval = jest.fn((fn, delay) => {
  return setInterval(fn, 0); // Execute immediately in tests
});

global.clearTimeout = jest.fn();
global.clearInterval = jest.fn();

// Mock crypto for Node.js environment
const crypto = require('crypto');
global.crypto = {
  randomBytes: crypto.randomBytes,
  createHash: crypto.createHash,
  createCipher: crypto.createCipher,
  createDecipher: crypto.createDecipher,
  scryptSync: crypto.scryptSync
};

// Mock axios for HTTP requests
jest.mock('axios', () => ({
  get: jest.fn(() => Promise.resolve({ data: {} })),
  post: jest.fn(() => Promise.resolve({ data: {} })),
  put: jest.fn(() => Promise.resolve({ data: {} })),
  delete: jest.fn(() => Promise.resolve({ data: {} })),
  create: jest.fn(() => ({
    get: jest.fn(() => Promise.resolve({ data: {} })),
    post: jest.fn(() => Promise.resolve({ data: {} })),
    put: jest.fn(() => Promise.resolve({ data: {} })),
    delete: jest.fn(() => Promise.resolve({ data: {} }))
  }))
}));

// Mock WebSocket
global.WebSocket = jest.fn(() => ({
  send: jest.fn(),
  close: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  readyState: 1, // OPEN
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3
}));

// Mock EventEmitter for Node.js
const EventEmitter = require('events');
global.EventEmitter = EventEmitter;

// Mock electron-store
jest.mock('electron-store', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn(),
    clear: jest.fn(),
    has: jest.fn(),
    size: 0
  }));
});

// Mock node-cron
jest.mock('node-cron', () => ({
  schedule: jest.fn(() => ({
    start: jest.fn(),
    stop: jest.fn(),
    destroy: jest.fn()
  })),
  validate: jest.fn(() => true)
}));

// Mock moment
jest.mock('moment', () => {
  const moment = jest.requireActual('moment');
  return jest.fn(() => moment('2023-10-25T10:00:00.000Z'));
});

// Helper functions for tests
global.testHelpers = {
  // Create mock market data
  createMockMarketData: (symbol = 'NIFTY', price = 18500) => ({
    symbol: symbol,
    ltp: price,
    change: 25.50,
    changePercent: 0.14,
    volume: 1000000,
    open: price - 50,
    high: price + 25,
    low: price - 75,
    close: price - 25.50,
    timestamp: new Date()
  }),
  
  // Create mock order
  createMockOrder: (symbol = 'NIFTY21OCT18500CE') => ({
    orderId: 'ORDER123',
    symbol: symbol,
    orderType: 'BUY',
    quantity: 50,
    price: 100.50,
    priceType: 'LMT',
    status: 'pending',
    timestamp: new Date()
  }),
  
  // Create mock position
  createMockPosition: (symbol = 'NIFTY21OCT18500CE') => ({
    symbol: symbol,
    quantity: 50,
    buyPrice: 100.50,
    currentPrice: 105.75,
    pnl: 262.5,
    pnlPercent: 5.22,
    side: 'LONG',
    timestamp: new Date()
  }),
  
  // Create mock option chain
  createMockOptionChain: (symbol = 'NIFTY', atmStrike = 18500) => ({
    symbol: symbol,
    underlyingPrice: atmStrike + 25,
    atmStrike: atmStrike,
    strikes: [
      {
        strike: atmStrike - 100,
        call: { ltp: 150, volume: 1000, oi: 5000 },
        put: { ltp: 25, volume: 800, oi: 3000 }
      },
      {
        strike: atmStrike,
        call: { ltp: 100, volume: 2000, oi: 8000 },
        put: { ltp: 75, volume: 1500, oi: 6000 }
      },
      {
        strike: atmStrike + 100,
        call: { ltp: 50, volume: 1200, oi: 4000 },
        put: { ltp: 125, volume: 900, oi: 7000 }
      }
    ],
    timestamp: new Date()
  }),
  
  // Wait for async operations
  waitFor: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Flush all promises
  flushPromises: () => new Promise(resolve => setImmediate(resolve))
};

// Setup and teardown
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks();
  
  // Reset mock implementations
  mockElectronAPI.authenticate.mockResolvedValue({ success: true });
  mockElectronAPI.getMarketData.mockResolvedValue({ 
    success: true, 
    data: [testHelpers.createMockMarketData()] 
  });
  mockElectronAPI.placeOrder.mockResolvedValue({ 
    success: true, 
    orderId: 'ORDER123' 
  });
  mockElectronAPI.getPortfolio.mockResolvedValue({ 
    success: true, 
    portfolio: { positions: [], dayTradingStats: {} } 
  });
});

afterEach(() => {
  // Clean up after each test
  jest.clearAllTimers();
  jest.restoreAllMocks();
});

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

console.log('Test setup completed successfully');
