const EventEmitter = require('events');
const cron = require('node-cron');

/**
 * Automated Trading Engine
 * Core engine for automated trading with real-time monitoring and execution
 */
class TradingEngine extends EventEmitter {
    constructor(orderService, portfolioService, marketDataService, wsService) {
        super();
        this.orderService = orderService;
        this.portfolioService = portfolioService;
        this.marketDataService = marketDataService;
        this.wsService = wsService;
        
        this.isActive = false;
        this.monitoredPositions = new Map();
        this.scheduledTasks = new Map();
        this.riskLimits = {
            maxLossPerDay: 5000,
            maxProfitPerDay: 10000,
            maxTradesPerDay: 10,
            maxPositionSize: 100000
        };
        
        this.tradingStats = {
            tradesExecuted: 0,
            stopLossHits: 0,
            targetHits: 0,
            currentPnL: 0,
            dailyPnL: 0
        };

        this.setupEventListeners();
    }

    /**
     * Start the trading engine
     */
    async start() {
        try {
            if (this.isActive) {
                return { success: false, message: 'Trading engine is already active' };
            }

            // Check if market is open
            if (!this.isMarketOpen()) {
                return { success: false, message: 'Market is closed' };
            }

            // Initialize WebSocket connection for real-time data
            await this.initializeRealTimeData();

            // Start monitoring existing positions
            await this.startPositionMonitoring();

            // Start scheduled task monitoring
            this.startScheduledTaskMonitoring();

            // Start risk monitoring
            this.startRiskMonitoring();

            this.isActive = true;
            this.emit('engineStarted');

            console.log('Trading engine started successfully');
            return { success: true, message: 'Trading engine started successfully' };
        } catch (error) {
            console.error('Failed to start trading engine:', error);
            return { success: false, message: error.message || 'Failed to start trading engine' };
        }
    }

    /**
     * Stop the trading engine
     */
    async stop() {
        try {
            this.isActive = false;

            // Stop all scheduled tasks
            this.scheduledTasks.forEach((task, id) => {
                if (task.destroy) {
                    task.destroy();
                }
            });
            this.scheduledTasks.clear();

            // Clear monitored positions
            this.monitoredPositions.clear();

            // Disconnect WebSocket if needed
            // this.wsService.disconnect();

            this.emit('engineStopped');

            console.log('Trading engine stopped');
            return { success: true, message: 'Trading engine stopped successfully' };
        } catch (error) {
            console.error('Failed to stop trading engine:', error);
            return { success: false, message: error.message || 'Failed to stop trading engine' };
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for order events
        this.orderService.on('orderPlaced', (order) => {
            this.handleOrderPlaced(order);
        });

        this.orderService.on('orderExecuted', (order) => {
            this.handleOrderExecuted(order);
        });

        // Listen for portfolio updates
        this.portfolioService.on('positionUpdated', (position, oldPnL) => {
            this.handlePositionUpdate(position, oldPnL);
        });

        // Listen for WebSocket market data
        if (this.wsService) {
            this.wsService.on('quote', (data) => {
                this.handleMarketDataUpdate(data);
            });
        }
    }

    /**
     * Initialize real-time data connections
     */
    async initializeRealTimeData() {
        try {
            // Get current positions to monitor
            const positionsResult = await this.portfolioService.getPositions();
            if (positionsResult.success) {
                const symbols = positionsResult.data.map(pos => pos.symbol);
                if (symbols.length > 0) {
                    // Subscribe to real-time data for all position symbols
                    await this.wsService.subscribeQuote(symbols);
                }
            }
        } catch (error) {
            console.error('Failed to initialize real-time data:', error);
        }
    }

    /**
     * Start monitoring existing positions for stop-loss and target
     */
    async startPositionMonitoring() {
        try {
            const positionsResult = await this.portfolioService.getPositions();
            if (positionsResult.success) {
                positionsResult.data.forEach(position => {
                    this.addPositionToMonitoring(position);
                });
            }
        } catch (error) {
            console.error('Failed to start position monitoring:', error);
        }
    }

    /**
     * Add position to monitoring
     */
    addPositionToMonitoring(position) {
        const monitoringData = {
            symbol: position.symbol,
            quantity: position.quantity,
            entryPrice: position.buyPrice,
            currentPrice: position.currentPrice,
            side: position.side,
            stopLoss: null,
            target: null,
            stopLossPrice: null,
            targetPrice: null
        };

        // Calculate stop-loss and target prices based on default settings
        if (this.riskLimits.defaultStopLoss) {
            const stopLossPercent = this.riskLimits.defaultStopLoss;
            monitoringData.stopLoss = stopLossPercent;
            
            if (position.side === 'LONG') {
                monitoringData.stopLossPrice = position.buyPrice * (1 - stopLossPercent / 100);
            } else {
                monitoringData.stopLossPrice = position.buyPrice * (1 + stopLossPercent / 100);
            }
        }

        if (this.riskLimits.defaultTarget) {
            const targetPercent = this.riskLimits.defaultTarget;
            monitoringData.target = targetPercent;
            
            if (position.side === 'LONG') {
                monitoringData.targetPrice = position.buyPrice * (1 + targetPercent / 100);
            } else {
                monitoringData.targetPrice = position.buyPrice * (1 - targetPercent / 100);
            }
        }

        this.monitoredPositions.set(position.symbol, monitoringData);
        console.log(`Added position to monitoring: ${position.symbol}`);
    }

    /**
     * Start scheduled task monitoring
     */
    startScheduledTaskMonitoring() {
        // Monitor scheduled orders every minute
        const scheduledOrderTask = cron.schedule('* * * * *', async () => {
            if (this.isActive) {
                await this.checkScheduledOrders();
            }
        }, { scheduled: false });

        // Auto square-off before market close (3:20 PM)
        const squareOffTask = cron.schedule('20 15 * * 1-5', async () => {
            if (this.isActive) {
                await this.autoSquareOff();
            }
        }, { scheduled: false });

        // Daily reset at market open (9:15 AM)
        const dailyResetTask = cron.schedule('15 9 * * 1-5', async () => {
            if (this.isActive) {
                this.resetDailyStats();
            }
        }, { scheduled: false });

        scheduledOrderTask.start();
        squareOffTask.start();
        dailyResetTask.start();

        this.scheduledTasks.set('scheduledOrders', scheduledOrderTask);
        this.scheduledTasks.set('squareOff', squareOffTask);
        this.scheduledTasks.set('dailyReset', dailyResetTask);
    }

    /**
     * Start risk monitoring
     */
    startRiskMonitoring() {
        // Check risk limits every 30 seconds
        const riskMonitorTask = cron.schedule('*/30 * * * * *', async () => {
            if (this.isActive) {
                await this.checkRiskLimits();
            }
        }, { scheduled: false });

        riskMonitorTask.start();
        this.scheduledTasks.set('riskMonitor', riskMonitorTask);
    }

    /**
     * Handle market data updates
     */
    handleMarketDataUpdate(data) {
        if (!this.isActive) return;

        const symbol = data.symbol;
        const currentPrice = data.ltp;

        // Update monitored position
        const monitoredPosition = this.monitoredPositions.get(symbol);
        if (monitoredPosition) {
            monitoredPosition.currentPrice = currentPrice;
            this.checkStopLossAndTarget(monitoredPosition);
        }

        // Update portfolio position price
        this.portfolioService.updatePositionPrice(symbol, currentPrice);
    }

    /**
     * Check stop-loss and target for a position
     */
    async checkStopLossAndTarget(monitoredPosition) {
        try {
            const { symbol, side, currentPrice, stopLossPrice, targetPrice, quantity } = monitoredPosition;

            // Check stop-loss
            if (stopLossPrice) {
                let shouldTriggerStopLoss = false;
                
                if (side === 'LONG' && currentPrice <= stopLossPrice) {
                    shouldTriggerStopLoss = true;
                } else if (side === 'SHORT' && currentPrice >= stopLossPrice) {
                    shouldTriggerStopLoss = true;
                }

                if (shouldTriggerStopLoss) {
                    await this.executeStopLoss(monitoredPosition);
                    return;
                }
            }

            // Check target
            if (targetPrice) {
                let shouldTriggerTarget = false;
                
                if (side === 'LONG' && currentPrice >= targetPrice) {
                    shouldTriggerTarget = true;
                } else if (side === 'SHORT' && currentPrice <= targetPrice) {
                    shouldTriggerTarget = true;
                }

                if (shouldTriggerTarget) {
                    await this.executeTarget(monitoredPosition);
                    return;
                }
            }
        } catch (error) {
            console.error('Error checking stop-loss and target:', error);
        }
    }

    /**
     * Execute stop-loss order
     */
    async executeStopLoss(monitoredPosition) {
        try {
            const { symbol, quantity, side } = monitoredPosition;
            
            const orderData = {
                symbol: symbol,
                orderType: side === 'LONG' ? 'SELL' : 'BUY',
                quantity: Math.abs(quantity),
                priceType: 'MKT'
            };

            const result = await this.orderService.placeOrder(orderData);
            
            if (result.success) {
                this.tradingStats.stopLossHits++;
                this.monitoredPositions.delete(symbol);
                
                this.emit('stopLossExecuted', {
                    symbol: symbol,
                    price: monitoredPosition.currentPrice,
                    orderId: result.orderId
                });

                console.log(`Stop-loss executed for ${symbol} at ${monitoredPosition.currentPrice}`);
            } else {
                console.error(`Failed to execute stop-loss for ${symbol}:`, result.message);
            }
        } catch (error) {
            console.error('Failed to execute stop-loss:', error);
        }
    }

    /**
     * Execute target order
     */
    async executeTarget(monitoredPosition) {
        try {
            const { symbol, quantity, side } = monitoredPosition;
            
            const orderData = {
                symbol: symbol,
                orderType: side === 'LONG' ? 'SELL' : 'BUY',
                quantity: Math.abs(quantity),
                priceType: 'MKT'
            };

            const result = await this.orderService.placeOrder(orderData);
            
            if (result.success) {
                this.tradingStats.targetHits++;
                this.monitoredPositions.delete(symbol);
                
                this.emit('targetExecuted', {
                    symbol: symbol,
                    price: monitoredPosition.currentPrice,
                    orderId: result.orderId
                });

                console.log(`Target executed for ${symbol} at ${monitoredPosition.currentPrice}`);
            } else {
                console.error(`Failed to execute target for ${symbol}:`, result.message);
            }
        } catch (error) {
            console.error('Failed to execute target:', error);
        }
    }

    /**
     * Check scheduled orders
     */
    async checkScheduledOrders() {
        try {
            const scheduledOrders = this.orderService.getScheduledOrders();
            const now = new Date();

            for (const order of scheduledOrders) {
                if (order.status === 'scheduled' && order.scheduleDateTime <= now) {
                    await this.orderService.executeScheduledOrder(order.id);
                }
            }
        } catch (error) {
            console.error('Error checking scheduled orders:', error);
        }
    }

    /**
     * Auto square-off all positions
     */
    async autoSquareOff() {
        try {
            console.log('Executing auto square-off...');
            
            const positions = this.portfolioService.getAllPositions();
            
            for (const position of positions) {
                const orderData = {
                    symbol: position.symbol,
                    orderType: position.side === 'LONG' ? 'SELL' : 'BUY',
                    quantity: Math.abs(position.quantity),
                    priceType: 'MKT'
                };

                await this.orderService.placeOrder(orderData);
            }

            this.emit('autoSquareOffExecuted', { positionsSquaredOff: positions.length });
        } catch (error) {
            console.error('Failed to execute auto square-off:', error);
        }
    }

    /**
     * Check risk limits
     */
    async checkRiskLimits() {
        try {
            const stats = this.portfolioService.dayTradingStats;
            const orderStats = this.orderService.getOrderStatistics();

            // Check daily loss limit
            if (stats.totalPnL <= -this.riskLimits.maxLossPerDay) {
                await this.emergencyStop('Daily loss limit exceeded');
                return;
            }

            // Check daily profit limit
            if (stats.totalPnL >= this.riskLimits.maxProfitPerDay) {
                await this.emergencyStop('Daily profit target achieved');
                return;
            }

            // Check daily trade limit
            if (orderStats.total >= this.riskLimits.maxTradesPerDay) {
                await this.emergencyStop('Daily trade limit exceeded');
                return;
            }

            // Check position size limits
            const totalPositionValue = this.portfolioService.calculateTotalPortfolioValue();
            if (totalPositionValue >= this.riskLimits.maxPositionSize) {
                this.emit('riskLimitWarning', {
                    type: 'position_size',
                    message: 'Position size limit approaching'
                });
            }
        } catch (error) {
            console.error('Error checking risk limits:', error);
        }
    }

    /**
     * Emergency stop
     */
    async emergencyStop(reason) {
        try {
            console.log(`Emergency stop triggered: ${reason}`);
            
            // Cancel all pending orders
            await this.orderService.cancelAllPendingOrders();
            
            // Square off all positions
            await this.autoSquareOff();
            
            // Stop the engine
            await this.stop();
            
            this.emit('emergencyStop', { reason });
        } catch (error) {
            console.error('Failed to execute emergency stop:', error);
        }
    }

    /**
     * Handle order placed event
     */
    handleOrderPlaced(order) {
        this.tradingStats.tradesExecuted++;
        
        // Add to monitoring if it has stop-loss or target
        if (order.stopLoss || order.target) {
            // This would be implemented based on order structure
        }
    }

    /**
     * Handle order executed event
     */
    handleOrderExecuted(order) {
        // Update trading stats
        // This would be implemented based on order execution details
    }

    /**
     * Handle position update
     */
    handlePositionUpdate(position, oldPnL) {
        const pnlChange = position.pnl - oldPnL;
        this.tradingStats.currentPnL = position.pnl;
        this.tradingStats.dailyPnL += pnlChange;
    }

    /**
     * Reset daily statistics
     */
    resetDailyStats() {
        this.tradingStats = {
            tradesExecuted: 0,
            stopLossHits: 0,
            targetHits: 0,
            currentPnL: 0,
            dailyPnL: 0
        };
        
        this.emit('dailyStatsReset');
        console.log('Daily trading statistics reset');
    }

    /**
     * Check if market is open
     */
    isMarketOpen() {
        const now = new Date();
        const day = now.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
        const hour = now.getHours();
        const minute = now.getMinutes();
        const time = hour * 100 + minute;

        // Market is closed on weekends
        if (day === 0 || day === 6) {
            return false;
        }

        // Market hours: 9:15 AM to 3:30 PM (IST)
        return time >= 915 && time <= 1530;
    }

    /**
     * Update risk limits
     */
    updateRiskLimits(newLimits) {
        this.riskLimits = { ...this.riskLimits, ...newLimits };
        this.emit('riskLimitsUpdated', this.riskLimits);
    }

    /**
     * Get engine status
     */
    getStatus() {
        return {
            isActive: this.isActive,
            monitoredPositions: this.monitoredPositions.size,
            tradingStats: this.tradingStats,
            riskLimits: this.riskLimits,
            marketOpen: this.isMarketOpen()
        };
    }

    /**
     * Get monitored positions
     */
    getMonitoredPositions() {
        return Array.from(this.monitoredPositions.values());
    }
}

module.exports = TradingEngine;
