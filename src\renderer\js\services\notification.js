/**
 * Notification Service
 * Handles all types of notifications and alerts in the application
 */
class NotificationService {
    constructor() {
        this.notifications = [];
        this.maxNotifications = 5;
        this.defaultDuration = 5000;
        this.container = null;
        
        this.init();
    }

    /**
     * Initialize the notification service
     */
    init() {
        this.createContainer();
        this.setupEventListeners();
    }

    /**
     * Create notification container
     */
    createContainer() {
        this.container = document.getElementById('notification-container');
        
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'notification-container';
            this.container.className = 'notification-container';
            document.body.appendChild(this.container);
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for notification events from main process
        if (window.electronAPI) {
            window.electronAPI.onNotification((event, data) => {
                this.show(data.title, data.message, data.type);
            });
        }

        // Listen for custom notification events
        document.addEventListener('showNotification', (e) => {
            const { title, message, type, duration } = e.detail;
            this.show(title, message, type, duration);
        });
    }

    /**
     * Show a notification
     */
    show(title, message, type = 'info', duration = null) {
        const notification = this.createNotification(title, message, type, duration);
        this.addNotification(notification);
        return notification.id;
    }

    /**
     * Show success notification
     */
    success(title, message, duration = null) {
        return this.show(title, message, 'success', duration);
    }

    /**
     * Show error notification
     */
    error(title, message, duration = null) {
        return this.show(title, message, 'error', duration);
    }

    /**
     * Show warning notification
     */
    warning(title, message, duration = null) {
        return this.show(title, message, 'warning', duration);
    }

    /**
     * Show info notification
     */
    info(title, message, duration = null) {
        return this.show(title, message, 'info', duration);
    }

    /**
     * Create notification element
     */
    createNotification(title, message, type, duration) {
        const id = this.generateId();
        const notificationDuration = duration || this.defaultDuration;

        const notification = {
            id: id,
            title: title,
            message: message,
            type: type,
            duration: notificationDuration,
            timestamp: new Date(),
            element: null
        };

        // Create DOM element
        const element = document.createElement('div');
        element.className = `notification ${type}`;
        element.setAttribute('data-id', id);

        element.innerHTML = `
            <div class="notification-icon">
                ${this.getIcon(type)}
            </div>
            <div class="notification-content">
                <div class="notification-title">${this.escapeHtml(title)}</div>
                <div class="notification-message">${this.escapeHtml(message)}</div>
            </div>
            <button class="notification-close" onclick="notificationService.dismiss('${id}')">
                <i class="fas fa-times"></i>
            </button>
        `;

        notification.element = element;

        // Auto dismiss after duration
        if (notificationDuration > 0) {
            setTimeout(() => {
                this.dismiss(id);
            }, notificationDuration);
        }

        return notification;
    }

    /**
     * Add notification to container
     */
    addNotification(notification) {
        // Remove oldest notification if at max capacity
        if (this.notifications.length >= this.maxNotifications) {
            const oldest = this.notifications.shift();
            this.removeNotificationElement(oldest);
        }

        this.notifications.push(notification);
        this.container.appendChild(notification.element);

        // Trigger show animation
        setTimeout(() => {
            notification.element.classList.add('show');
        }, 100);

        // Log notification
        console.log(`[${notification.type.toUpperCase()}] ${notification.title}: ${notification.message}`);
    }

    /**
     * Dismiss notification by ID
     */
    dismiss(id) {
        const notification = this.notifications.find(n => n.id === id);
        if (notification) {
            this.removeNotification(notification);
        }
    }

    /**
     * Dismiss all notifications
     */
    dismissAll() {
        const notificationsToRemove = [...this.notifications];
        notificationsToRemove.forEach(notification => {
            this.removeNotification(notification);
        });
    }

    /**
     * Remove notification
     */
    removeNotification(notification) {
        // Remove from array
        const index = this.notifications.indexOf(notification);
        if (index > -1) {
            this.notifications.splice(index, 1);
        }

        // Remove from DOM
        this.removeNotificationElement(notification);
    }

    /**
     * Remove notification element from DOM
     */
    removeNotificationElement(notification) {
        if (notification.element && notification.element.parentNode) {
            notification.element.classList.remove('show');
            
            setTimeout(() => {
                if (notification.element.parentNode) {
                    notification.element.parentNode.removeChild(notification.element);
                }
            }, 300);
        }
    }

    /**
     * Get icon for notification type
     */
    getIcon(type) {
        const icons = {
            success: '<i class="fas fa-check-circle"></i>',
            error: '<i class="fas fa-exclamation-circle"></i>',
            warning: '<i class="fas fa-exclamation-triangle"></i>',
            info: '<i class="fas fa-info-circle"></i>'
        };
        
        return icons[type] || icons.info;
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Generate unique ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Show toast notification (simpler, temporary notification)
     */
    toast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        this.container.appendChild(toast);
        
        // Show animation
        setTimeout(() => toast.classList.add('show'), 100);
        
        // Remove after duration
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }

    /**
     * Show confirmation dialog
     */
    async confirm(title, message, confirmText = 'Yes', cancelText = 'No') {
        return new Promise((resolve) => {
            const modal = this.createConfirmModal(title, message, confirmText, cancelText, resolve);
            document.body.appendChild(modal);
            
            // Show modal
            setTimeout(() => modal.classList.add('show'), 100);
        });
    }

    /**
     * Create confirmation modal
     */
    createConfirmModal(title, message, confirmText, cancelText, resolve) {
        const modal = document.createElement('div');
        modal.className = 'modal confirm-modal';
        
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${this.escapeHtml(title)}</h3>
                </div>
                <div class="modal-body">
                    <p>${this.escapeHtml(message)}</p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary cancel-btn">${this.escapeHtml(cancelText)}</button>
                    <button class="btn btn-primary confirm-btn">${this.escapeHtml(confirmText)}</button>
                </div>
            </div>
        `;

        // Event handlers
        const confirmBtn = modal.querySelector('.confirm-btn');
        const cancelBtn = modal.querySelector('.cancel-btn');

        const cleanup = () => {
            modal.classList.remove('show');
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            }, 300);
        };

        confirmBtn.addEventListener('click', () => {
            cleanup();
            resolve(true);
        });

        cancelBtn.addEventListener('click', () => {
            cleanup();
            resolve(false);
        });

        // Close on backdrop click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                cleanup();
                resolve(false);
            }
        });

        return modal;
    }

    /**
     * Show loading notification
     */
    showLoading(message = 'Loading...') {
        const id = this.generateId();
        
        const element = document.createElement('div');
        element.className = 'notification info loading-notification';
        element.setAttribute('data-id', id);

        element.innerHTML = `
            <div class="notification-icon">
                <div class="loading-spinner"></div>
            </div>
            <div class="notification-content">
                <div class="notification-message">${this.escapeHtml(message)}</div>
            </div>
        `;

        const notification = {
            id: id,
            title: '',
            message: message,
            type: 'loading',
            duration: 0, // No auto dismiss
            timestamp: new Date(),
            element: element
        };

        this.notifications.push(notification);
        this.container.appendChild(element);

        setTimeout(() => {
            element.classList.add('show');
        }, 100);

        return id;
    }

    /**
     * Hide loading notification
     */
    hideLoading(id) {
        this.dismiss(id);
    }

    /**
     * Show progress notification
     */
    showProgress(message, progress = 0) {
        const id = this.generateId();
        
        const element = document.createElement('div');
        element.className = 'notification info progress-notification';
        element.setAttribute('data-id', id);

        element.innerHTML = `
            <div class="notification-content">
                <div class="notification-message">${this.escapeHtml(message)}</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${progress}%"></div>
                </div>
            </div>
            <button class="notification-close" onclick="notificationService.dismiss('${id}')">
                <i class="fas fa-times"></i>
            </button>
        `;

        const notification = {
            id: id,
            title: '',
            message: message,
            type: 'progress',
            duration: 0,
            timestamp: new Date(),
            element: element,
            progress: progress
        };

        this.notifications.push(notification);
        this.container.appendChild(element);

        setTimeout(() => {
            element.classList.add('show');
        }, 100);

        return id;
    }

    /**
     * Update progress notification
     */
    updateProgress(id, progress, message = null) {
        const notification = this.notifications.find(n => n.id === id);
        if (notification && notification.type === 'progress') {
            const progressFill = notification.element.querySelector('.progress-fill');
            if (progressFill) {
                progressFill.style.width = `${progress}%`;
            }

            if (message) {
                const messageElement = notification.element.querySelector('.notification-message');
                if (messageElement) {
                    messageElement.textContent = message;
                }
            }

            notification.progress = progress;
        }
    }

    /**
     * Get all notifications
     */
    getNotifications() {
        return [...this.notifications];
    }

    /**
     * Get notification by ID
     */
    getNotification(id) {
        return this.notifications.find(n => n.id === id);
    }

    /**
     * Clear all notifications
     */
    clear() {
        this.dismissAll();
    }

    /**
     * Set maximum number of notifications
     */
    setMaxNotifications(max) {
        this.maxNotifications = max;
    }

    /**
     * Set default duration
     */
    setDefaultDuration(duration) {
        this.defaultDuration = duration;
    }

    /**
     * Destroy notification service
     */
    destroy() {
        this.dismissAll();
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
    }
}

// Create global instance
const notificationService = new NotificationService();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationService;
} else {
    window.notificationService = notificationService;
}
