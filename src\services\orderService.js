const axios = require('axios');
const EventEmitter = require('events');

/**
 * Order Management Service for Kotak Neo Trade API
 * Handles order placement, modification, cancellation, and tracking
 */
class OrderService extends EventEmitter {
    constructor(authService) {
        super();
        this.authService = authService;
        this.baseURL = 'https://gw-napi.kotaksecurities.com';
        this.orders = new Map();
        this.orderHistory = [];
        this.scheduledOrders = new Map();
        
        // Order status mapping
        this.orderStatusMap = {
            'PENDING': 'pending',
            'OPEN': 'open',
            'COMPLETE': 'executed',
            'CANCELLED': 'cancelled',
            'REJECTED': 'rejected'
        };
    }

    /**
     * Place a new order
     */
    async placeOrder(orderData) {
        try {
            const headers = this.authService.getAuthHeaders();
            if (!headers) {
                throw new Error('Not authenticated');
            }

            // Validate order data
            this.validateOrderData(orderData);

            // Check if it's a scheduled order
            if (orderData.scheduleOrder && orderData.scheduleTime && orderData.scheduleDate) {
                return await this.scheduleOrder(orderData);
            }

            // Prepare order payload
            const orderPayload = this.prepareOrderPayload(orderData);

            const response = await axios.post(`${this.baseURL}/orders`, orderPayload, {
                headers,
                timeout: 15000
            });

            if (response.data && response.data.stat === 'Ok') {
                const orderResult = response.data.data;
                const orderId = orderResult.order_id || orderResult.oms_order_id;

                // Store order locally
                const order = {
                    orderId: orderId,
                    symbol: orderData.symbol,
                    strike: orderData.strike,
                    optionType: orderData.optionType,
                    orderType: orderData.orderType,
                    quantity: orderData.quantity,
                    price: orderData.limitPrice || 0,
                    priceType: orderData.priceType,
                    status: 'pending',
                    timestamp: new Date(),
                    stopLoss: orderData.stopLoss,
                    target: orderData.target,
                    ...orderResult
                };

                this.orders.set(orderId, order);
                this.orderHistory.push(order);

                // Emit order placed event
                this.emit('orderPlaced', order);

                // Set up stop loss and target orders if specified
                if (orderData.stopLoss > 0 || orderData.target > 0) {
                    await this.setupStopLossAndTarget(order, orderData);
                }

                return {
                    success: true,
                    orderId: orderId,
                    message: 'Order placed successfully',
                    data: order
                };
            } else {
                return {
                    success: false,
                    message: response.data?.emsg || 'Failed to place order'
                };
            }
        } catch (error) {
            console.error('Failed to place order:', error);
            return {
                success: false,
                message: error.message || 'Failed to place order'
            };
        }
    }

    /**
     * Schedule an order for future execution
     */
    async scheduleOrder(orderData) {
        try {
            const scheduleDateTime = new Date(`${orderData.scheduleDate}T${orderData.scheduleTime}`);
            const now = new Date();

            if (scheduleDateTime <= now) {
                throw new Error('Scheduled time must be in the future');
            }

            const scheduledOrderId = `SCH_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            
            const scheduledOrder = {
                id: scheduledOrderId,
                ...orderData,
                scheduleDateTime: scheduleDateTime,
                status: 'scheduled',
                createdAt: now
            };

            this.scheduledOrders.set(scheduledOrderId, scheduledOrder);

            // Set timeout to execute the order
            const delay = scheduleDateTime.getTime() - now.getTime();
            setTimeout(async () => {
                await this.executeScheduledOrder(scheduledOrderId);
            }, delay);

            this.emit('orderScheduled', scheduledOrder);

            return {
                success: true,
                orderId: scheduledOrderId,
                message: 'Order scheduled successfully',
                data: scheduledOrder
            };
        } catch (error) {
            console.error('Failed to schedule order:', error);
            return {
                success: false,
                message: error.message || 'Failed to schedule order'
            };
        }
    }

    /**
     * Execute a scheduled order
     */
    async executeScheduledOrder(scheduledOrderId) {
        try {
            const scheduledOrder = this.scheduledOrders.get(scheduledOrderId);
            if (!scheduledOrder) {
                console.error('Scheduled order not found:', scheduledOrderId);
                return;
            }

            // Update status to executing
            scheduledOrder.status = 'executing';
            this.emit('scheduledOrderExecuting', scheduledOrder);

            // Remove schedule-specific fields and place the order
            const orderData = { ...scheduledOrder };
            delete orderData.scheduleOrder;
            delete orderData.scheduleTime;
            delete orderData.scheduleDate;
            delete orderData.scheduleDateTime;

            const result = await this.placeOrder(orderData);

            if (result.success) {
                scheduledOrder.status = 'executed';
                scheduledOrder.executedOrderId = result.orderId;
                this.emit('scheduledOrderExecuted', scheduledOrder);
            } else {
                scheduledOrder.status = 'failed';
                scheduledOrder.error = result.message;
                this.emit('scheduledOrderFailed', scheduledOrder);
            }
        } catch (error) {
            console.error('Failed to execute scheduled order:', error);
            const scheduledOrder = this.scheduledOrders.get(scheduledOrderId);
            if (scheduledOrder) {
                scheduledOrder.status = 'failed';
                scheduledOrder.error = error.message;
                this.emit('scheduledOrderFailed', scheduledOrder);
            }
        }
    }

    /**
     * Cancel a scheduled order
     */
    cancelScheduledOrder(scheduledOrderId) {
        const scheduledOrder = this.scheduledOrders.get(scheduledOrderId);
        if (scheduledOrder && scheduledOrder.status === 'scheduled') {
            scheduledOrder.status = 'cancelled';
            this.emit('scheduledOrderCancelled', scheduledOrder);
            return { success: true, message: 'Scheduled order cancelled' };
        }
        return { success: false, message: 'Scheduled order not found or cannot be cancelled' };
    }

    /**
     * Modify an existing order
     */
    async modifyOrder(orderId, modifications) {
        try {
            const headers = this.authService.getAuthHeaders();
            if (!headers) {
                throw new Error('Not authenticated');
            }

            const order = this.orders.get(orderId);
            if (!order) {
                throw new Error('Order not found');
            }

            const modifyPayload = {
                order_id: orderId,
                ...modifications
            };

            const response = await axios.put(`${this.baseURL}/orders/${orderId}`, modifyPayload, {
                headers,
                timeout: 15000
            });

            if (response.data && response.data.stat === 'Ok') {
                // Update local order
                Object.assign(order, modifications, {
                    modifiedAt: new Date()
                });

                this.emit('orderModified', order);

                return {
                    success: true,
                    message: 'Order modified successfully',
                    data: order
                };
            } else {
                return {
                    success: false,
                    message: response.data?.emsg || 'Failed to modify order'
                };
            }
        } catch (error) {
            console.error('Failed to modify order:', error);
            return {
                success: false,
                message: error.message || 'Failed to modify order'
            };
        }
    }

    /**
     * Cancel an order
     */
    async cancelOrder(orderId) {
        try {
            const headers = this.authService.getAuthHeaders();
            if (!headers) {
                throw new Error('Not authenticated');
            }

            const response = await axios.delete(`${this.baseURL}/orders/${orderId}`, {
                headers,
                timeout: 15000
            });

            if (response.data && response.data.stat === 'Ok') {
                const order = this.orders.get(orderId);
                if (order) {
                    order.status = 'cancelled';
                    order.cancelledAt = new Date();
                    this.emit('orderCancelled', order);
                }

                return {
                    success: true,
                    message: 'Order cancelled successfully'
                };
            } else {
                return {
                    success: false,
                    message: response.data?.emsg || 'Failed to cancel order'
                };
            }
        } catch (error) {
            console.error('Failed to cancel order:', error);
            return {
                success: false,
                message: error.message || 'Failed to cancel order'
            };
        }
    }

    /**
     * Get order book
     */
    async getOrderBook() {
        try {
            const headers = this.authService.getAuthHeaders();
            if (!headers) {
                throw new Error('Not authenticated');
            }

            const response = await axios.get(`${this.baseURL}/orders`, {
                headers,
                timeout: 15000
            });

            if (response.data && response.data.stat === 'Ok') {
                const orders = response.data.data.map(order => this.formatOrderData(order));
                
                // Update local orders
                orders.forEach(order => {
                    this.orders.set(order.orderId, order);
                });

                return {
                    success: true,
                    data: orders
                };
            } else {
                return {
                    success: false,
                    message: response.data?.emsg || 'Failed to get order book'
                };
            }
        } catch (error) {
            console.error('Failed to get order book:', error);
            return {
                success: false,
                message: error.message || 'Failed to get order book'
            };
        }
    }

    /**
     * Get order history
     */
    async getOrderHistory(fromDate = null, toDate = null) {
        try {
            const headers = this.authService.getAuthHeaders();
            if (!headers) {
                throw new Error('Not authenticated');
            }

            const params = {};
            if (fromDate) params.from_date = fromDate;
            if (toDate) params.to_date = toDate;

            const response = await axios.get(`${this.baseURL}/orders/history`, {
                headers,
                params,
                timeout: 15000
            });

            if (response.data && response.data.stat === 'Ok') {
                const orders = response.data.data.map(order => this.formatOrderData(order));
                return {
                    success: true,
                    data: orders
                };
            } else {
                return {
                    success: false,
                    message: response.data?.emsg || 'Failed to get order history'
                };
            }
        } catch (error) {
            console.error('Failed to get order history:', error);
            return {
                success: false,
                message: error.message || 'Failed to get order history'
            };
        }
    }

    /**
     * Setup stop loss and target orders
     */
    async setupStopLossAndTarget(parentOrder, orderData) {
        try {
            const { stopLoss, target, quantity, orderType } = orderData;
            const entryPrice = parentOrder.price || parentOrder.averagePrice || 0;

            if (stopLoss > 0) {
                const stopLossPrice = orderType === 'BUY' ? 
                    entryPrice * (1 - stopLoss / 100) : 
                    entryPrice * (1 + stopLoss / 100);

                const stopLossOrder = {
                    symbol: parentOrder.symbol,
                    strike: parentOrder.strike,
                    optionType: parentOrder.optionType,
                    orderType: orderType === 'BUY' ? 'SELL' : 'BUY',
                    quantity: quantity,
                    priceType: 'SL',
                    limitPrice: stopLossPrice,
                    triggerPrice: stopLossPrice,
                    parentOrderId: parentOrder.orderId
                };

                await this.placeOrder(stopLossOrder);
            }

            if (target > 0) {
                const targetPrice = orderType === 'BUY' ? 
                    entryPrice * (1 + target / 100) : 
                    entryPrice * (1 - target / 100);

                const targetOrder = {
                    symbol: parentOrder.symbol,
                    strike: parentOrder.strike,
                    optionType: parentOrder.optionType,
                    orderType: orderType === 'BUY' ? 'SELL' : 'BUY',
                    quantity: quantity,
                    priceType: 'LMT',
                    limitPrice: targetPrice,
                    parentOrderId: parentOrder.orderId
                };

                await this.placeOrder(targetOrder);
            }
        } catch (error) {
            console.error('Failed to setup stop loss and target:', error);
        }
    }

    /**
     * Validate order data
     */
    validateOrderData(orderData) {
        const required = ['symbol', 'orderType', 'quantity', 'priceType'];
        
        for (const field of required) {
            if (!orderData[field]) {
                throw new Error(`Missing required field: ${field}`);
            }
        }

        if (orderData.priceType === 'LMT' && !orderData.limitPrice) {
            throw new Error('Limit price is required for limit orders');
        }

        if (orderData.quantity <= 0) {
            throw new Error('Quantity must be greater than 0');
        }
    }

    /**
     * Prepare order payload for API
     */
    prepareOrderPayload(orderData) {
        return {
            instrument_token: orderData.symbol,
            transaction_type: orderData.orderType,
            quantity: orderData.quantity,
            price: orderData.limitPrice || 0,
            order_type: orderData.priceType,
            validity: 'DAY',
            product: 'MIS', // Intraday
            exchange: 'NFO', // Options exchange
            trigger_price: orderData.triggerPrice || 0,
            disclosed_quantity: 0,
            tag: 'TradingBot'
        };
    }

    /**
     * Format order data from API response
     */
    formatOrderData(rawOrder) {
        return {
            orderId: rawOrder.order_id || rawOrder.oms_order_id,
            symbol: rawOrder.instrument_token || rawOrder.trading_symbol,
            orderType: rawOrder.transaction_type,
            quantity: parseInt(rawOrder.quantity || 0),
            price: parseFloat(rawOrder.price || 0),
            averagePrice: parseFloat(rawOrder.average_price || 0),
            priceType: rawOrder.order_type,
            status: this.orderStatusMap[rawOrder.order_status] || rawOrder.order_status,
            filledQuantity: parseInt(rawOrder.filled_quantity || 0),
            pendingQuantity: parseInt(rawOrder.pending_quantity || 0),
            timestamp: new Date(rawOrder.order_timestamp || rawOrder.created_at),
            exchange: rawOrder.exchange,
            product: rawOrder.product,
            validity: rawOrder.validity,
            tag: rawOrder.tag
        };
    }

    /**
     * Get all orders
     */
    getAllOrders() {
        return Array.from(this.orders.values());
    }

    /**
     * Get scheduled orders
     */
    getScheduledOrders() {
        return Array.from(this.scheduledOrders.values());
    }

    /**
     * Get order by ID
     */
    getOrder(orderId) {
        return this.orders.get(orderId);
    }

    /**
     * Get orders by status
     */
    getOrdersByStatus(status) {
        return Array.from(this.orders.values()).filter(order => order.status === status);
    }

    /**
     * Get today's orders
     */
    getTodaysOrders() {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        return Array.from(this.orders.values()).filter(order => 
            order.timestamp >= today
        );
    }

    /**
     * Cancel all pending orders
     */
    async cancelAllPendingOrders() {
        const pendingOrders = this.getOrdersByStatus('pending');
        const results = [];

        for (const order of pendingOrders) {
            const result = await this.cancelOrder(order.orderId);
            results.push({ orderId: order.orderId, result });
        }

        return results;
    }

    /**
     * Get order statistics
     */
    getOrderStatistics() {
        const orders = this.getTodaysOrders();
        
        return {
            total: orders.length,
            executed: orders.filter(o => o.status === 'executed').length,
            pending: orders.filter(o => o.status === 'pending').length,
            cancelled: orders.filter(o => o.status === 'cancelled').length,
            rejected: orders.filter(o => o.status === 'rejected').length,
            scheduled: this.scheduledOrders.size
        };
    }
}

module.exports = OrderService;
