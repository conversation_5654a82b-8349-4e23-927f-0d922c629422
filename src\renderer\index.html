<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nifty Options Trading Bot</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- Loading Screen -->
        <div id="loading-screen" class="loading-screen">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <h2>Nifty Options Trading Bot</h2>
                <p>Initializing application...</p>
            </div>
        </div>

        <!-- Main Application -->
        <div id="main-app" class="main-app hidden">
            <!-- Header -->
            <header class="app-header">
                <div class="header-left">
                    <h1 class="app-title">
                        <i class="fas fa-chart-line"></i>
                        Nifty Options Trading Bot
                    </h1>
                </div>
                <div class="header-center">
                    <div class="connection-status" id="connection-status">
                        <span class="status-indicator disconnected"></span>
                        <span class="status-text">Disconnected</span>
                    </div>
                </div>
                <div class="header-right">
                    <div class="auth-status" id="auth-status">
                        <span class="status-indicator offline"></span>
                        <span class="status-text">Not Connected</span>
                    </div>
                    <div class="trading-status" id="trading-status">
                        <span class="trading-indicator stopped"></span>
                        <span class="trading-text">Trading Stopped</span>
                    </div>
                    <button class="btn btn-primary" id="login-btn">Login</button>
                    <button class="btn btn-icon" id="settings-btn" title="Settings">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </header>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Sidebar Navigation -->
                <nav class="sidebar">
                    <ul class="nav-menu">
                        <li class="nav-item active" data-tab="dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </li>
                        <li class="nav-item" data-tab="trading">
                            <i class="fas fa-exchange-alt"></i>
                            <span>Trading</span>
                        </li>
                        <li class="nav-item" data-tab="portfolio">
                            <i class="fas fa-briefcase"></i>
                            <span>Portfolio</span>
                        </li>
                        <li class="nav-item" data-tab="orders">
                            <i class="fas fa-list-alt"></i>
                            <span>Orders</span>
                        </li>
                        <li class="nav-item" data-tab="positions">
                            <i class="fas fa-chart-pie"></i>
                            <span>Positions</span>
                        </li>
                        <li class="nav-item" data-tab="analytics">
                            <i class="fas fa-chart-bar"></i>
                            <span>Analytics</span>
                        </li>
                    </ul>
                </nav>

                <!-- Content Area -->
                <div class="content-area">
                    <!-- Dashboard Tab -->
                    <div id="dashboard-tab" class="tab-content active">
                        <div class="dashboard-grid">
                            <!-- Market Overview -->
                            <div class="card market-overview">
                                <div class="card-header">
                                    <h3>Market Overview</h3>
                                    <div class="market-time" id="market-time"></div>
                                </div>
                                <div class="card-body">
                                    <div class="market-indices">
                                        <div class="index-item" id="nifty-50">
                                            <div class="index-name">Nifty 50</div>
                                            <div class="index-price">--</div>
                                            <div class="index-change">--</div>
                                        </div>
                                        <div class="index-item" id="bank-nifty">
                                            <div class="index-name">Bank Nifty</div>
                                            <div class="index-price">--</div>
                                            <div class="index-change">--</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Stats -->
                            <div class="card quick-stats">
                                <div class="card-header">
                                    <h3>Today's Summary</h3>
                                </div>
                                <div class="card-body">
                                    <div class="stats-grid">
                                        <div class="stat-item">
                                            <div class="stat-label">P&L</div>
                                            <div class="stat-value" id="total-pnl">₹0.00</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-label">Trades</div>
                                            <div class="stat-value" id="total-trades">0</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-label">Win Rate</div>
                                            <div class="stat-value" id="win-rate">0%</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-label">Active Orders</div>
                                            <div class="stat-value" id="active-orders">0</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Trading Controls -->
                            <div class="card trading-controls">
                                <div class="card-header">
                                    <h3>Trading Controls</h3>
                                </div>
                                <div class="card-body">
                                    <div class="control-buttons">
                                        <button class="btn btn-success" id="start-trading-btn">
                                            <i class="fas fa-play"></i>
                                            Start Trading
                                        </button>
                                        <button class="btn btn-danger" id="stop-trading-btn" disabled>
                                            <i class="fas fa-stop"></i>
                                            Stop Trading
                                        </button>
                                        <button class="btn btn-warning" id="emergency-stop-btn">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            Emergency Stop
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Activity -->
                            <div class="card recent-activity">
                                <div class="card-header">
                                    <h3>Recent Activity</h3>
                                </div>
                                <div class="card-body">
                                    <div class="activity-list" id="activity-list">
                                        <div class="activity-item">
                                            <div class="activity-time">--:--:--</div>
                                            <div class="activity-text">Application started</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Trading Tab -->
                    <div id="trading-tab" class="tab-content">
                        <div class="trading-layout">
                            <!-- Option Chain -->
                            <div class="card option-chain-card">
                                <div class="card-header">
                                    <h3>Option Chain</h3>
                                    <div class="symbol-selector">
                                        <select id="symbol-select">
                                            <option value="NIFTY">Nifty 50</option>
                                            <option value="BANKNIFTY">Bank Nifty</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="option-chain-container" id="option-chain-container">
                                        <!-- Option chain will be populated here -->
                                    </div>
                                </div>
                            </div>

                            <!-- Order Form -->
                            <div class="card order-form-card">
                                <div class="card-header">
                                    <h3>Place Order</h3>
                                </div>
                                <div class="card-body">
                                    <form id="order-form" class="order-form">
                                        <!-- Order form will be populated here -->
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Other tabs will be added here -->
                    <div id="portfolio-tab" class="tab-content">
                        <div class="card">
                            <div class="card-header">
                                <h3>Portfolio</h3>
                            </div>
                            <div class="card-body">
                                <p>Portfolio content will be implemented here</p>
                            </div>
                        </div>
                    </div>

                    <div id="orders-tab" class="tab-content">
                        <div class="card">
                            <div class="card-header">
                                <h3>Order Book</h3>
                            </div>
                            <div class="card-body">
                                <p>Order book content will be implemented here</p>
                            </div>
                        </div>
                    </div>

                    <div id="positions-tab" class="tab-content">
                        <div class="card">
                            <div class="card-header">
                                <h3>Positions</h3>
                            </div>
                            <div class="card-body">
                                <p>Positions content will be implemented here</p>
                            </div>
                        </div>
                    </div>

                    <div id="analytics-tab" class="tab-content">
                        <div class="card">
                            <div class="card-header">
                                <h3>Analytics</h3>
                            </div>
                            <div class="card-body">
                                <p>Analytics content will be implemented here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- Settings Modal -->
        <div id="settings-modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Settings</h3>
                    <button class="btn btn-icon close-modal" id="close-settings">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Settings content will be populated here -->
                </div>
            </div>
        </div>

        <!-- Notification Container -->
        <div id="notification-container" class="notification-container"></div>
    </div>

    <!-- Scripts -->
    <script src="js/utils/constants.js"></script>
    <script src="js/utils/helpers.js"></script>
    <script src="js/services/api.js"></script>
    <script src="js/services/websocket.js"></script>
    <script src="js/services/notification.js"></script>
    <script src="js/components/auth.js"></script>
    <script src="js/components/dashboard.js"></script>
    <script src="js/components/trading.js"></script>
    <script src="js/components/settings.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
