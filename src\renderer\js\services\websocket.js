/**
 * WebSocket Service for Renderer Process
 * Handles real-time market data updates in the UI
 */
class WebSocketService {
    constructor() {
        this.isConnected = false;
        this.subscriptions = new Map();
        this.callbacks = new Map();
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        
        this.setupEventListeners();
    }

    /**
     * Setup event listeners for WebSocket events from main process
     */
    setupEventListeners() {
        if (window.electronAPI) {
            // Market data updates
            window.electronAPI.onMarketDataUpdate((event, data) => {
                this.handleMarketDataUpdate(data);
            });

            // Connection status updates
            window.electronAPI.onConnectionStatusUpdate?.((event, status) => {
                this.handleConnectionStatusUpdate(status);
            });

            // WebSocket errors
            window.electronAPI.onWebSocketError?.((event, error) => {
                this.handleWebSocketError(error);
            });
        }
    }

    /**
     * Initialize WebSocket connection
     */
    async initialize() {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.initializeWebSocket();
                if (result.success) {
                    console.log('WebSocket service initialized');
                    return true;
                } else {
                    console.error('Failed to initialize WebSocket:', result.message);
                    return false;
                }
            }
            return false;
        } catch (error) {
            console.error('WebSocket initialization error:', error);
            return false;
        }
    }

    /**
     * Subscribe to market data for symbols
     */
    async subscribeToMarketData(symbols, callback) {
        try {
            if (!Array.isArray(symbols)) {
                symbols = [symbols];
            }

            // Store callback for each symbol
            symbols.forEach(symbol => {
                if (!this.callbacks.has(symbol)) {
                    this.callbacks.set(symbol, new Set());
                }
                this.callbacks.get(symbol).add(callback);
                this.subscriptions.set(symbol, true);
            });

            if (window.electronAPI) {
                const result = await window.electronAPI.subscribeToMarketData(symbols);
                if (result.success) {
                    console.log('Subscribed to market data:', symbols);
                    return true;
                } else {
                    console.error('Failed to subscribe to market data:', result.message);
                    return false;
                }
            }
            return false;
        } catch (error) {
            console.error('Market data subscription error:', error);
            return false;
        }
    }

    /**
     * Unsubscribe from market data
     */
    async unsubscribeFromMarketData(symbols, callback = null) {
        try {
            if (!Array.isArray(symbols)) {
                symbols = [symbols];
            }

            symbols.forEach(symbol => {
                if (callback && this.callbacks.has(symbol)) {
                    this.callbacks.get(symbol).delete(callback);
                    
                    // If no more callbacks for this symbol, remove subscription
                    if (this.callbacks.get(symbol).size === 0) {
                        this.callbacks.delete(symbol);
                        this.subscriptions.delete(symbol);
                    }
                } else {
                    // Remove all callbacks for this symbol
                    this.callbacks.delete(symbol);
                    this.subscriptions.delete(symbol);
                }
            });

            if (window.electronAPI) {
                const result = await window.electronAPI.unsubscribeFromMarketData(symbols);
                if (result.success) {
                    console.log('Unsubscribed from market data:', symbols);
                    return true;
                } else {
                    console.error('Failed to unsubscribe from market data:', result.message);
                    return false;
                }
            }
            return false;
        } catch (error) {
            console.error('Market data unsubscription error:', error);
            return false;
        }
    }

    /**
     * Handle market data updates
     */
    handleMarketDataUpdate(data) {
        const symbol = data.symbol;
        
        if (this.callbacks.has(symbol)) {
            this.callbacks.get(symbol).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('Error in market data callback:', error);
                }
            });
        }

        // Emit custom event for other components
        const event = new CustomEvent('marketDataUpdate', {
            detail: data
        });
        document.dispatchEvent(event);
    }

    /**
     * Handle connection status updates
     */
    handleConnectionStatusUpdate(status) {
        this.isConnected = status.isConnected;
        this.reconnectAttempts = status.reconnectAttempts || 0;

        // Update UI connection status
        if (window.tradingApp) {
            window.tradingApp.updateConnectionStatus(this.isConnected);
        }

        // Emit custom event
        const event = new CustomEvent('connectionStatusUpdate', {
            detail: status
        });
        document.dispatchEvent(event);
    }

    /**
     * Handle WebSocket errors
     */
    handleWebSocketError(error) {
        console.error('WebSocket error:', error);

        // Emit custom event
        const event = new CustomEvent('webSocketError', {
            detail: error
        });
        document.dispatchEvent(event);
    }

    /**
     * Get current connection status
     */
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            subscriptions: Array.from(this.subscriptions.keys())
        };
    }

    /**
     * Subscribe to Nifty 50 index
     */
    async subscribeToNifty(callback) {
        return await this.subscribeToMarketData(['NIFTY 50'], callback);
    }

    /**
     * Subscribe to Bank Nifty index
     */
    async subscribeToBankNifty(callback) {
        return await this.subscribeToMarketData(['NIFTY BANK'], callback);
    }

    /**
     * Subscribe to option symbols
     */
    async subscribeToOptions(optionSymbols, callback) {
        return await this.subscribeToMarketData(optionSymbols, callback);
    }

    /**
     * Get live quote for a symbol
     */
    async getLiveQuote(symbol) {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.getMarketData([symbol]);
                if (result.success && result.data.length > 0) {
                    return result.data[0];
                }
            }
            return null;
        } catch (error) {
            console.error('Failed to get live quote:', error);
            return null;
        }
    }

    /**
     * Subscribe to multiple symbols with different callbacks
     */
    subscribeMultiple(subscriptions) {
        const promises = subscriptions.map(sub => {
            return this.subscribeToMarketData(sub.symbols, sub.callback);
        });
        
        return Promise.all(promises);
    }

    /**
     * Unsubscribe from all symbols
     */
    async unsubscribeAll() {
        const allSymbols = Array.from(this.subscriptions.keys());
        if (allSymbols.length > 0) {
            await this.unsubscribeFromMarketData(allSymbols);
        }
        
        this.callbacks.clear();
        this.subscriptions.clear();
    }

    /**
     * Reconnect WebSocket
     */
    async reconnect() {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.reconnectWebSocket();
                return result.success;
            }
            return false;
        } catch (error) {
            console.error('WebSocket reconnection error:', error);
            return false;
        }
    }

    /**
     * Disconnect WebSocket
     */
    async disconnect() {
        try {
            await this.unsubscribeAll();
            
            if (window.electronAPI) {
                const result = await window.electronAPI.disconnectWebSocket();
                return result.success;
            }
            return false;
        } catch (error) {
            console.error('WebSocket disconnection error:', error);
            return false;
        }
    }

    /**
     * Add event listener for market data updates
     */
    addEventListener(symbol, callback) {
        return this.subscribeToMarketData(symbol, callback);
    }

    /**
     * Remove event listener for market data updates
     */
    removeEventListener(symbol, callback) {
        return this.unsubscribeFromMarketData(symbol, callback);
    }

    /**
     * Check if symbol is subscribed
     */
    isSubscribed(symbol) {
        return this.subscriptions.has(symbol);
    }

    /**
     * Get all subscribed symbols
     */
    getSubscribedSymbols() {
        return Array.from(this.subscriptions.keys());
    }

    /**
     * Get subscription count
     */
    getSubscriptionCount() {
        return this.subscriptions.size;
    }

    /**
     * Format symbol for display
     */
    formatSymbol(symbol) {
        // Remove exchange prefix if present
        return symbol.replace(/^(NSE|BSE):/, '');
    }

    /**
     * Parse symbol data
     */
    parseSymbolData(data) {
        return {
            symbol: this.formatSymbol(data.symbol),
            ltp: parseFloat(data.ltp || 0),
            change: parseFloat(data.change || 0),
            changePercent: parseFloat(data.changePercent || 0),
            volume: parseInt(data.volume || 0),
            timestamp: data.timestamp || new Date()
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WebSocketService;
}
