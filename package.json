{"name": "nifty-options-trading-bot", "version": "1.0.0", "description": "Automated trading bot for Nifty 50 and Bank Nifty options using Kotak Neo Trade API", "main": "src/main/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "format:check": "prettier --check src/", "validate": "npm run lint && npm run format:check && npm run test", "validate:app": "node scripts/validate.js", "clean": "rimraf dist/ coverage/ .nyc_output/", "postinstall": "electron-builder install-app-deps"}, "keywords": ["trading", "options", "nifty", "bank-nifty", "kotak-neo", "automated-trading", "electron"], "author": "Trading Bot Developer", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4", "eslint": "^8.50.0", "jest": "^29.7.0", "babel-jest": "^29.7.0", "@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "prettier": "^3.0.3", "rimraf": "^5.0.0"}, "dependencies": {"axios": "^1.5.0", "ws": "^8.14.2", "node-cron": "^3.0.2", "electron-store": "^8.1.0", "moment": "^2.29.4", "csv-parser": "^3.0.0", "lodash": "^4.17.21"}, "build": {"appId": "com.tradingbot.nifty-options", "productName": "Nifty Options Trading Bot", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}, "homepage": "./", "repository": {"type": "git", "url": "https://github.com/your-username/nifty-options-trading-bot.git"}}