/**
 * Authentication Component
 * Handles TOTP login and MPIN trading authentication
 */
class AuthComponent {
    constructor(app) {
        this.app = app;
        this.isAuthenticated = false;
        this.canTrade = false;
        this.userInfo = null;
        
        this.init();
    }

    init() {
        this.createAuthModal();
        this.setupEventListeners();
        this.checkAuthStatus();
    }

    createAuthModal() {
        // Create authentication modal if it doesn't exist
        if (!document.getElementById('auth-modal')) {
            const modal = document.createElement('div');
            modal.id = 'auth-modal';
            modal.className = 'modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Kotak Neo Trade Authentication</h3>
                    </div>
                    <div class="modal-body">
                        <div id="totp-step" class="auth-step">
                            <h4>Step 1: Enter TOTP Code</h4>
                            <p>Enter the 6-digit code from your authenticator app</p>
                            <div class="form-group">
                                <label for="totp-code">TOTP Code</label>
                                <input type="text" id="totp-code" placeholder="000000" maxlength="6" pattern="\\d{6}">
                            </div>
                            <div class="auth-actions">
                                <button id="totp-login-btn" class="btn btn-primary">Login</button>
                                <button id="cancel-auth-btn" class="btn btn-secondary">Cancel</button>
                            </div>
                        </div>
                        
                        <div id="mpin-step" class="auth-step hidden">
                            <h4>Step 2: Enter MPIN for Trading (Optional)</h4>
                            <p>Enter your 4-6 digit MPIN to enable trading features</p>
                            <div class="form-group">
                                <label for="mpin-code">MPIN</label>
                                <input type="password" id="mpin-code" placeholder="****" maxlength="6">
                            </div>
                            <div class="auth-actions">
                                <button id="mpin-login-btn" class="btn btn-primary">Enable Trading</button>
                                <button id="skip-trading-btn" class="btn btn-secondary">Skip (View Only)</button>
                            </div>
                        </div>
                        
                        <div id="auth-success" class="auth-step hidden">
                            <div class="success-message">
                                <i class="fas fa-check-circle"></i>
                                <h4>Authentication Successful!</h4>
                                <p id="welcome-message">Welcome back!</p>
                                <div class="auth-info">
                                    <div class="info-item">
                                        <span class="label">UCC:</span>
                                        <span id="user-ucc">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">Mode:</span>
                                        <span id="user-mode">View</span>
                                    </div>
                                </div>
                            </div>
                            <div class="auth-actions">
                                <button id="close-auth-btn" class="btn btn-primary">Continue</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // Set up event listeners after modal is created
            this.setupEventListeners();
        }
    }

    setupEventListeners() {
        console.log('Auth: Setting up event listeners');

        // TOTP Login
        const totpBtn = document.getElementById('totp-login-btn');
        if (totpBtn) {
            totpBtn.addEventListener('click', () => {
                this.handleTOTPLogin();
            });
            console.log('Auth: TOTP login button listener attached');
        }

        // MPIN Login
        const mpinBtn = document.getElementById('mpin-login-btn');
        if (mpinBtn) {
            mpinBtn.addEventListener('click', () => {
                this.handleMPINLogin();
            });
            console.log('Auth: MPIN login button listener attached');
        }

        // Skip Trading
        const skipBtn = document.getElementById('skip-trading-btn');
        if (skipBtn) {
            skipBtn.addEventListener('click', () => {
                this.showAuthSuccess();
            });
            console.log('Auth: Skip trading button listener attached');
        }

        // Cancel/Close
        const cancelBtn = document.getElementById('cancel-auth-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.hideAuthModal();
            });
            console.log('Auth: Cancel button listener attached');
        }

        const closeBtn = document.getElementById('close-auth-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                console.log('Auth: Continue button clicked, closing modal');
                this.hideAuthModal();
            });
            console.log('Auth: Continue button listener attached');
        } else {
            console.log('Auth: Continue button not found during setup');
        }

        // Enter key handling
        document.getElementById('totp-code')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleTOTPLogin();
            }
        });

        document.getElementById('mpin-code')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleMPINLogin();
            }
        });

        // Auto-format TOTP input
        document.getElementById('totp-code')?.addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/\D/g, '').substring(0, 6);
        });

        // Auto-format MPIN input
        document.getElementById('mpin-code')?.addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/\D/g, '').substring(0, 6);
        });
    }

    async checkAuthStatus() {
        try {
            // Just check if credentials are configured, don't auto-authenticate
            const settings = await window.electronAPI.getSettings();
            if (settings.success && settings.settings.credentials) {
                console.log('Credentials are configured');
                // Update UI to show credentials are ready
                if (this.app && this.app.updateCredentialStatus) {
                    this.app.updateCredentialStatus(true);
                }
            } else {
                console.log('No credentials configured');
                if (this.app && this.app.updateCredentialStatus) {
                    this.app.updateCredentialStatus(false);
                }
            }
        } catch (error) {
            console.error('Error checking auth status:', error);
        }
    }

    async showAuthModal() {
        // First check if credentials are configured
        try {
            const settings = await window.electronAPI.getSettings();
            if (!settings.success || !settings.settings.credentials) {
                // No credentials configured, show error and redirect to settings
                if (window.notificationService) {
                    window.notificationService.error(
                        'Setup Required',
                        'Please configure your API credentials in Settings first.'
                    );
                }

                // Redirect to settings
                if (this.app && this.app.openSettings) {
                    this.app.openSettings();
                }
                return;
            }

            // Validate that all required credentials are present
            const creds = settings.settings.credentials;
            if (!creds.ucc || !creds.mobileNumber || !creds.consumerKey || !creds.environment) {
                if (window.notificationService) {
                    window.notificationService.error(
                        'Incomplete Setup',
                        'Please complete your API credentials configuration in Settings.'
                    );
                }

                if (this.app && this.app.openSettings) {
                    this.app.openSettings();
                }
                return;
            }

            // Credentials are configured, show the modal
            // Ensure modal is created first
            this.createAuthModal();

            const modal = document.getElementById('auth-modal');
            if (modal) {
                modal.classList.add('show');
                this.showTOTPStep();

                // Focus on TOTP input
                setTimeout(() => {
                    document.getElementById('totp-code')?.focus();
                }, 100);
            }
        } catch (error) {
            console.error('Error checking credentials:', error);
            if (window.notificationService) {
                window.notificationService.error(
                    'Error',
                    'Failed to check credentials. Please try again.'
                );
            }
        }
    }

    hideAuthModal() {
        console.log('Auth: hideAuthModal called, isAuthenticated:', this.isAuthenticated);
        const modal = document.getElementById('auth-modal');
        if (modal) {
            modal.classList.remove('show');
            console.log('Auth: Modal hidden');
            // Only reset modal if user is not authenticated
            // If authenticated, keep the current state for next time
            if (!this.isAuthenticated) {
                console.log('Auth: User not authenticated, resetting modal');
                this.resetAuthModal();
            } else {
                console.log('Auth: User authenticated, keeping modal state');
            }
        } else {
            console.log('Auth: Modal not found');
        }
    }

    showTOTPStep() {
        document.getElementById('totp-step')?.classList.remove('hidden');
        document.getElementById('mpin-step')?.classList.add('hidden');
        document.getElementById('auth-success')?.classList.add('hidden');
    }

    showMPINStep() {
        document.getElementById('totp-step')?.classList.add('hidden');
        document.getElementById('mpin-step')?.classList.remove('hidden');
        document.getElementById('auth-success')?.classList.add('hidden');
        
        // Focus on MPIN input
        setTimeout(() => {
            document.getElementById('mpin-code')?.focus();
        }, 100);
    }

    showAuthSuccess() {
        console.log('Auth: showAuthSuccess called, canTrade:', this.canTrade);
        document.getElementById('totp-step')?.classList.add('hidden');
        document.getElementById('mpin-step')?.classList.add('hidden');
        document.getElementById('auth-success')?.classList.remove('hidden');

        // Update user info
        if (this.userInfo) {
            document.getElementById('welcome-message').textContent =
                `Welcome back, ${this.userInfo.greetingName || 'User'}!`;
            document.getElementById('user-ucc').textContent = this.userInfo.ucc || '-';
            document.getElementById('user-mode').textContent = this.canTrade ? 'Trading' : 'View Only';
            console.log('Auth: User info updated in success screen');
        }

        // Re-attach event listener for continue button since it might be newly visible
        setTimeout(() => {
            const closeBtn = document.getElementById('close-auth-btn');
            if (closeBtn) {
                // Remove any existing listeners and add new one
                closeBtn.replaceWith(closeBtn.cloneNode(true));
                const newCloseBtn = document.getElementById('close-auth-btn');
                newCloseBtn.addEventListener('click', () => {
                    console.log('Auth: Continue button clicked from success screen');
                    this.hideAuthModal();
                });
                console.log('Auth: Continue button listener re-attached');
            }
        }, 100);
    }

    resetAuthModal() {
        // Clear inputs if they exist
        const totpInput = document.getElementById('totp-code');
        const mpinInput = document.getElementById('mpin-code');

        if (totpInput) totpInput.value = '';
        if (mpinInput) mpinInput.value = '';

        // Reset to first step
        this.showTOTPStep();

        // Clear any error states
        this.clearErrors();
    }

    async handleTOTPLogin() {
        const totpCode = document.getElementById('totp-code').value;

        if (!totpCode || totpCode.length !== 6) {
            this.showError('Please enter a valid 6-digit TOTP code');
            return;
        }

        if (!/^\d{6}$/.test(totpCode)) {
            this.showError('TOTP must be 6 digits only');
            return;
        }

        try {
            this.setLoading('totp-login-btn', true);
            this.clearErrors();

            console.log('Auth: Attempting TOTP authentication...');
            const result = await window.electronAPI.authenticate({ totp: totpCode });

            console.log('Auth: TOTP result:', result);

            if (result.success) {
                this.isAuthenticated = true;
                this.userInfo = result.data;

                // Show success notification
                if (window.notificationService) {
                    window.notificationService.success(
                        'Authentication Successful',
                        `Welcome ${this.userInfo.greetingName || 'User'}! You are now logged in (View mode)`
                    );
                }

                // Move to MPIN step
                this.showMPINStep();

                // Update app state
                if (this.app && this.app.updateAuthStatus) {
                    this.app.updateAuthStatus(true, false);
                }

            } else {
                this.showError(result.message || 'Authentication failed');
            }
        } catch (error) {
            console.error('TOTP authentication error:', error);
            this.showError('Authentication failed. Please check your connection and try again.');
        } finally {
            this.setLoading('totp-login-btn', false);
        }
    }

    async handleMPINLogin() {
        const mpinCode = document.getElementById('mpin-code').value;

        if (!mpinCode || mpinCode.length < 4) {
            this.showError('Please enter a valid MPIN (4-6 digits)');
            return;
        }

        if (!/^\d{4,6}$/.test(mpinCode)) {
            this.showError('MPIN must be 4-6 digits only');
            return;
        }

        try {
            this.setLoading('mpin-login-btn', true);
            this.clearErrors();

            console.log('Auth: Attempting MPIN authentication...');
            const result = await window.electronAPI.authenticateTrading({ mpin: mpinCode });

            console.log('Auth: MPIN result:', result);

            if (result.success) {
                this.canTrade = true;
                this.userInfo = { ...this.userInfo, ...result.data };

                // Show success notification
                if (window.notificationService) {
                    window.notificationService.success(
                        'Trading Enabled',
                        'You can now place trades! Full access granted.'
                    );
                }

                // Show success step
                this.showAuthSuccess();

                // Update app state
                if (this.app && this.app.updateAuthStatus) {
                    this.app.updateAuthStatus(true, true);
                }

            } else {
                this.showError(result.message || 'Trading authentication failed');
            }
        } catch (error) {
            console.error('MPIN authentication error:', error);
            this.showError('Trading authentication failed. Please check your MPIN and try again.');
        } finally {
            this.setLoading('mpin-login-btn', false);
        }
    }

    setLoading(buttonId, loading) {
        const button = document.getElementById(buttonId);
        if (button) {
            if (loading) {
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Authenticating...';
            } else {
                button.disabled = false;
                button.innerHTML = buttonId === 'totp-login-btn' ? 'Login' : 'Enable Trading';
            }
        }
    }

    showError(message) {
        // Remove existing error messages
        this.clearErrors();
        
        // Create error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
        
        // Add to current step
        const currentStep = document.querySelector('.auth-step:not(.hidden)');
        if (currentStep) {
            currentStep.insertBefore(errorDiv, currentStep.querySelector('.auth-actions'));
        }
    }

    clearErrors() {
        document.querySelectorAll('.error-message').forEach(el => el.remove());
    }

    // Public methods for app integration
    isUserAuthenticated() {
        return this.isAuthenticated;
    }

    canUserTrade() {
        return this.canTrade;
    }

    getUserInfo() {
        return this.userInfo;
    }

    logout() {
        this.isAuthenticated = false;
        this.canTrade = false;
        this.userInfo = null;

        // Reset the modal to initial state
        this.resetAuthModal();

        // Notify app
        if (this.app && this.app.updateAuthStatus) {
            this.app.updateAuthStatus(false, false);
        }

        // Show notification
        if (window.notificationService) {
            window.notificationService.info('Logged Out', 'You have been logged out successfully');
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthComponent;
}
