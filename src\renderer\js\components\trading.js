/**
 * Trading Component
 * Handles option chain display, order placement, and trading interface
 */
class Trading {
    constructor(app) {
        this.app = app;
        this.currentSymbol = 'NIFTY';
        this.currentExpiry = null;
        this.optionChainData = null;
        this.selectedOption = null;
        this.refreshInterval = null;
        
        this.init();
    }

    init() {
        this.createTradingInterface();
        this.setupEventListeners();
        this.loadDefaultData();
    }

    createTradingInterface() {
        this.createOptionChainTable();
        this.createOrderForm();
        this.createScheduledOrdersPanel();
    }

    createOptionChainTable() {
        const container = document.getElementById('option-chain-container');
        if (!container) return;

        container.innerHTML = `
            <div class="option-chain-header">
                <div class="symbol-info">
                    <div class="current-price" id="current-price">
                        <span class="price-label">Current Price:</span>
                        <span class="price-value">--</span>
                        <span class="price-change">--</span>
                    </div>
                    <div class="expiry-selector">
                        <label for="expiry-select">Expiry:</label>
                        <select id="expiry-select">
                            <option value="">Loading...</option>
                        </select>
                    </div>
                </div>
                <div class="chain-controls">
                    <button class="btn btn-sm" id="refresh-chain">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                    <button class="btn btn-sm" id="auto-refresh-toggle">
                        <i class="fas fa-play"></i>
                        Auto Refresh
                    </button>
                </div>
            </div>
            
            <div class="option-chain-table-container">
                <table class="option-chain-table" id="option-chain-table">
                    <thead>
                        <tr>
                            <th colspan="8" class="call-header">CALL OPTIONS</th>
                            <th class="strike-header">STRIKE</th>
                            <th colspan="8" class="put-header">PUT OPTIONS</th>
                        </tr>
                        <tr class="sub-header">
                            <th>OI</th>
                            <th>Volume</th>
                            <th>IV</th>
                            <th>LTP</th>
                            <th>Change</th>
                            <th>Bid</th>
                            <th>Ask</th>
                            <th>Action</th>
                            <th class="strike-price">Price</th>
                            <th>Action</th>
                            <th>Bid</th>
                            <th>Ask</th>
                            <th>Change</th>
                            <th>LTP</th>
                            <th>IV</th>
                            <th>Volume</th>
                            <th>OI</th>
                        </tr>
                    </thead>
                    <tbody id="option-chain-body">
                        <tr>
                            <td colspan="17" class="loading-row">
                                <div class="loading-spinner"></div>
                                Loading option chain...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }

    createOrderForm() {
        const container = document.querySelector('.order-form-card .card-body');
        if (!container) return;

        container.innerHTML = `
            <form id="order-form" class="order-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="selected-option">Selected Option</label>
                        <input type="text" id="selected-option" readonly placeholder="Select an option from the chain">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="order-type">Order Type</label>
                        <select id="order-type" name="orderType">
                            <option value="BUY">Buy</option>
                            <option value="SELL">Sell</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="quantity">Quantity (Lots)</label>
                        <input type="number" id="quantity" name="quantity" min="1" max="100" value="1">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="price-type">Price Type</label>
                        <select id="price-type" name="priceType">
                            <option value="MKT">Market</option>
                            <option value="LMT">Limit</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="limit-price">Limit Price</label>
                        <input type="number" id="limit-price" name="limitPrice" step="0.05" disabled>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="stop-loss">Stop Loss (%)</label>
                        <input type="number" id="stop-loss" name="stopLoss" step="0.1" value="2" min="0" max="50">
                    </div>
                    <div class="form-group">
                        <label for="target">Target (%)</label>
                        <input type="number" id="target" name="target" step="0.1" value="5" min="0" max="100">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="schedule-order" name="scheduleOrder">
                            <span class="checkmark"></span>
                            Schedule Order
                        </label>
                    </div>
                </div>
                
                <div class="schedule-section hidden" id="schedule-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="schedule-time">Execution Time</label>
                            <input type="time" id="schedule-time" name="scheduleTime">
                        </div>
                        <div class="form-group">
                            <label for="schedule-date">Execution Date</label>
                            <input type="date" id="schedule-date" name="scheduleDate">
                        </div>
                    </div>
                </div>
                
                <div class="order-summary" id="order-summary">
                    <div class="summary-row">
                        <span>Estimated Premium:</span>
                        <span id="estimated-premium">₹0.00</span>
                    </div>
                    <div class="summary-row">
                        <span>Total Investment:</span>
                        <span id="total-investment">₹0.00</span>
                    </div>
                    <div class="summary-row">
                        <span>Stop Loss at:</span>
                        <span id="stop-loss-price">₹0.00</span>
                    </div>
                    <div class="summary-row">
                        <span>Target at:</span>
                        <span id="target-price">₹0.00</span>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" id="clear-form">
                        <i class="fas fa-times"></i>
                        Clear
                    </button>
                    <button type="submit" class="btn btn-primary" id="place-order">
                        <i class="fas fa-shopping-cart"></i>
                        Place Order
                    </button>
                </div>
            </form>
        `;
    }

    createScheduledOrdersPanel() {
        const tradingLayout = document.querySelector('.trading-layout');
        if (!tradingLayout) return;

        const scheduledOrdersCard = document.createElement('div');
        scheduledOrdersCard.className = 'card scheduled-orders-card';
        scheduledOrdersCard.innerHTML = `
            <div class="card-header">
                <h3>Scheduled Orders</h3>
                <button class="btn btn-sm" id="refresh-scheduled">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
            </div>
            <div class="card-body">
                <div class="scheduled-orders-list" id="scheduled-orders-list">
                    <div class="empty-state">
                        <i class="fas fa-calendar-alt"></i>
                        <p>No scheduled orders</p>
                    </div>
                </div>
            </div>
        `;
        
        tradingLayout.appendChild(scheduledOrdersCard);
    }

    setupEventListeners() {
        // Symbol selector
        document.getElementById('symbol-select')?.addEventListener('change', (e) => {
            this.currentSymbol = e.target.value;
            this.loadOptionChain();
        });

        // Expiry selector
        document.getElementById('expiry-select')?.addEventListener('change', (e) => {
            this.currentExpiry = e.target.value;
            this.loadOptionChain();
        });

        // Refresh controls
        document.getElementById('refresh-chain')?.addEventListener('click', () => {
            this.loadOptionChain();
        });

        document.getElementById('auto-refresh-toggle')?.addEventListener('click', (e) => {
            this.toggleAutoRefresh(e.target);
        });

        // Order form
        document.getElementById('price-type')?.addEventListener('change', (e) => {
            const limitPriceInput = document.getElementById('limit-price');
            limitPriceInput.disabled = e.target.value === 'MKT';
        });

        document.getElementById('schedule-order')?.addEventListener('change', (e) => {
            const scheduleSection = document.getElementById('schedule-section');
            if (e.target.checked) {
                scheduleSection.classList.remove('hidden');
                this.setDefaultScheduleTime();
            } else {
                scheduleSection.classList.add('hidden');
            }
        });

        // Form inputs for real-time calculation
        ['quantity', 'limit-price', 'stop-loss', 'target'].forEach(id => {
            document.getElementById(id)?.addEventListener('input', () => {
                this.updateOrderSummary();
            });
        });

        document.getElementById('order-form')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.placeOrder();
        });

        document.getElementById('clear-form')?.addEventListener('click', () => {
            this.clearOrderForm();
        });

        // Market data updates
        document.addEventListener('marketDataUpdate', (e) => {
            this.handleMarketDataUpdate(e.detail);
        });
    }

    async loadDefaultData() {
        // Set default schedule date to today
        const today = new Date().toISOString().split('T')[0];
        const scheduleDateInput = document.getElementById('schedule-date');
        if (scheduleDateInput) {
            scheduleDateInput.value = today;
        }

        // Load expiry dates
        await this.loadExpiryDates();
        
        // Load initial option chain
        await this.loadOptionChain();
    }

    async loadExpiryDates() {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.getAvailableExpiries(this.currentSymbol);
                if (result.success) {
                    this.populateExpirySelector(result.data);
                }
            }
        } catch (error) {
            console.error('Failed to load expiry dates:', error);
        }
    }

    populateExpirySelector(expiries) {
        const select = document.getElementById('expiry-select');
        if (!select) return;

        select.innerHTML = '';
        
        expiries.forEach(expiry => {
            const option = document.createElement('option');
            option.value = expiry.date;
            option.textContent = `${expiry.label} (${expiry.type})`;
            select.appendChild(option);
        });

        // Select the first expiry by default
        if (expiries.length > 0) {
            this.currentExpiry = expiries[0].date;
            select.value = this.currentExpiry;
        }
    }

    async loadOptionChain() {
        try {
            const tbody = document.getElementById('option-chain-body');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="17" class="loading-row">
                            <div class="loading-spinner"></div>
                            Loading option chain...
                        </td>
                    </tr>
                `;
            }

            if (window.electronAPI) {
                const result = await window.electronAPI.getOptionChain({
                    symbol: this.currentSymbol,
                    expiry: this.currentExpiry
                });

                if (result.success) {
                    this.optionChainData = result.data;
                    this.renderOptionChain();
                    this.updateCurrentPrice();
                } else {
                    this.showError('Failed to load option chain', result.message);
                }
            }
        } catch (error) {
            console.error('Failed to load option chain:', error);
            this.showError('Failed to load option chain', error.message);
        }
    }

    renderOptionChain() {
        const tbody = document.getElementById('option-chain-body');
        if (!tbody || !this.optionChainData) return;

        tbody.innerHTML = '';

        this.optionChainData.strikes.forEach(strike => {
            const row = this.createOptionChainRow(strike);
            tbody.appendChild(row);
        });
    }

    createOptionChainRow(strike) {
        const row = document.createElement('tr');
        const isATM = strike.strike === this.optionChainData.atmStrike;
        
        if (isATM) {
            row.classList.add('atm-strike');
        }

        const call = strike.call || {};
        const put = strike.put || {};

        row.innerHTML = `
            <!-- Call Options -->
            <td class="oi-cell">${this.formatNumber(call.oi || 0)}</td>
            <td class="volume-cell">${this.formatNumber(call.volume || 0)}</td>
            <td class="iv-cell">${this.formatPercent(call.iv || 0)}</td>
            <td class="ltp-cell ${this.getPriceChangeClass(call.change)}">${this.formatPrice(call.ltp || 0)}</td>
            <td class="change-cell ${this.getPriceChangeClass(call.change)}">${this.formatChange(call.change || 0)}</td>
            <td class="bid-cell">${this.formatPrice(call.bid || 0)}</td>
            <td class="ask-cell">${this.formatPrice(call.ask || 0)}</td>
            <td class="action-cell">
                <button class="btn btn-xs btn-success" onclick="tradingComponent.selectOption('${strike.strike}', 'CE', 'BUY')">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="btn btn-xs btn-danger" onclick="tradingComponent.selectOption('${strike.strike}', 'CE', 'SELL')">
                    <i class="fas fa-minus"></i>
                </button>
            </td>
            
            <!-- Strike Price -->
            <td class="strike-price ${isATM ? 'atm' : ''}">${strike.strike}</td>
            
            <!-- Put Options -->
            <td class="action-cell">
                <button class="btn btn-xs btn-success" onclick="tradingComponent.selectOption('${strike.strike}', 'PE', 'BUY')">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="btn btn-xs btn-danger" onclick="tradingComponent.selectOption('${strike.strike}', 'PE', 'SELL')">
                    <i class="fas fa-minus"></i>
                </button>
            </td>
            <td class="bid-cell">${this.formatPrice(put.bid || 0)}</td>
            <td class="ask-cell">${this.formatPrice(put.ask || 0)}</td>
            <td class="change-cell ${this.getPriceChangeClass(put.change)}">${this.formatChange(put.change || 0)}</td>
            <td class="ltp-cell ${this.getPriceChangeClass(put.change)}">${this.formatPrice(put.ltp || 0)}</td>
            <td class="iv-cell">${this.formatPercent(put.iv || 0)}</td>
            <td class="volume-cell">${this.formatNumber(put.volume || 0)}</td>
            <td class="oi-cell">${this.formatNumber(put.oi || 0)}</td>
        `;

        return row;
    }

    selectOption(strike, optionType, orderType) {
        const strikeData = this.optionChainData.strikes.find(s => s.strike === parseInt(strike));
        if (!strikeData) return;

        const option = optionType === 'CE' ? strikeData.call : strikeData.put;
        if (!option) return;

        this.selectedOption = {
            strike: parseInt(strike),
            type: optionType,
            symbol: option.symbol,
            ltp: option.ltp,
            bid: option.bid,
            ask: option.ask
        };

        // Update form
        document.getElementById('selected-option').value = 
            `${this.currentSymbol} ${strike} ${optionType} @ ₹${option.ltp}`;
        document.getElementById('order-type').value = orderType;
        document.getElementById('limit-price').value = orderType === 'BUY' ? option.ask : option.bid;

        this.updateOrderSummary();
    }

    updateCurrentPrice() {
        if (!this.optionChainData) return;

        const priceElement = document.querySelector('#current-price .price-value');
        const changeElement = document.querySelector('#current-price .price-change');

        if (priceElement) {
            priceElement.textContent = `₹${this.formatPrice(this.optionChainData.underlyingPrice)}`;
        }

        // This would be updated with real-time data
        if (changeElement) {
            changeElement.textContent = '+0.00 (0.00%)';
            changeElement.className = 'price-change positive';
        }
    }

    updateOrderSummary() {
        if (!this.selectedOption) return;

        const quantity = parseInt(document.getElementById('quantity').value) || 1;
        const priceType = document.getElementById('price-type').value;
        const limitPrice = parseFloat(document.getElementById('limit-price').value) || 0;
        const stopLoss = parseFloat(document.getElementById('stop-loss').value) || 0;
        const target = parseFloat(document.getElementById('target').value) || 0;

        const price = priceType === 'MKT' ? this.selectedOption.ltp : limitPrice;
        const lotSize = this.getLotSize(this.currentSymbol);
        const totalQuantity = quantity * lotSize;
        const totalInvestment = price * totalQuantity;

        const stopLossPrice = price * (1 - stopLoss / 100);
        const targetPrice = price * (1 + target / 100);

        document.getElementById('estimated-premium').textContent = `₹${this.formatPrice(price)}`;
        document.getElementById('total-investment').textContent = `₹${this.formatPrice(totalInvestment)}`;
        document.getElementById('stop-loss-price').textContent = `₹${this.formatPrice(stopLossPrice)}`;
        document.getElementById('target-price').textContent = `₹${this.formatPrice(targetPrice)}`;
    }

    async placeOrder() {
        try {
            if (!this.selectedOption) {
                this.showError('No option selected', 'Please select an option from the chain first.');
                return;
            }

            const formData = new FormData(document.getElementById('order-form'));
            const orderData = {
                symbol: this.selectedOption.symbol,
                strike: this.selectedOption.strike,
                optionType: this.selectedOption.type,
                orderType: formData.get('orderType'),
                quantity: parseInt(formData.get('quantity')),
                priceType: formData.get('priceType'),
                limitPrice: parseFloat(formData.get('limitPrice')) || 0,
                stopLoss: parseFloat(formData.get('stopLoss')) || 0,
                target: parseFloat(formData.get('target')) || 0,
                scheduleOrder: formData.get('scheduleOrder') === 'on',
                scheduleTime: formData.get('scheduleTime'),
                scheduleDate: formData.get('scheduleDate')
            };

            const placeOrderBtn = document.getElementById('place-order');
            placeOrderBtn.disabled = true;
            placeOrderBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Placing Order...';

            if (window.electronAPI) {
                const result = await window.electronAPI.placeOrder(orderData);
                
                if (result.success) {
                    this.showSuccess('Order placed successfully', `Order ID: ${result.orderId}`);
                    this.clearOrderForm();
                } else {
                    this.showError('Failed to place order', result.message);
                }
            }
        } catch (error) {
            console.error('Failed to place order:', error);
            this.showError('Failed to place order', error.message);
        } finally {
            const placeOrderBtn = document.getElementById('place-order');
            placeOrderBtn.disabled = false;
            placeOrderBtn.innerHTML = '<i class="fas fa-shopping-cart"></i> Place Order';
        }
    }

    clearOrderForm() {
        document.getElementById('order-form').reset();
        document.getElementById('selected-option').value = '';
        document.getElementById('schedule-section').classList.add('hidden');
        this.selectedOption = null;
        this.updateOrderSummary();
    }

    setDefaultScheduleTime() {
        const now = new Date();
        const timeString = now.toTimeString().slice(0, 5);
        document.getElementById('schedule-time').value = timeString;
    }

    toggleAutoRefresh(button) {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
            button.innerHTML = '<i class="fas fa-play"></i> Auto Refresh';
            button.classList.remove('btn-warning');
        } else {
            this.refreshInterval = setInterval(() => {
                this.loadOptionChain();
            }, 30000); // Refresh every 30 seconds
            button.innerHTML = '<i class="fas fa-pause"></i> Stop Auto';
            button.classList.add('btn-warning');
        }
    }

    handleMarketDataUpdate(data) {
        if (data.symbol === this.currentSymbol) {
            this.updateCurrentPrice();
        }
        
        // Update option chain if data matches any option
        if (this.optionChainData) {
            // This would update individual option prices in real-time
            this.updateOptionPrices(data);
        }
    }

    updateOptionPrices(data) {
        // Update option prices in the table
        // This would be implemented based on the specific data format
    }

    onTabActivated() {
        // Called when trading tab is activated
        if (!this.optionChainData) {
            this.loadOptionChain();
        }
    }

    // Utility methods
    formatPrice(price) {
        return parseFloat(price).toFixed(2);
    }

    formatNumber(num) {
        return parseInt(num).toLocaleString();
    }

    formatPercent(percent) {
        return `${parseFloat(percent).toFixed(2)}%`;
    }

    formatChange(change) {
        const formatted = parseFloat(change).toFixed(2);
        return change >= 0 ? `+${formatted}` : formatted;
    }

    getPriceChangeClass(change) {
        if (change > 0) return 'positive';
        if (change < 0) return 'negative';
        return '';
    }

    getLotSize(symbol) {
        const lotSizes = {
            'NIFTY': 50,
            'BANKNIFTY': 25,
            'FINNIFTY': 40,
            'MIDCPNIFTY': 75
        };
        return lotSizes[symbol] || 50;
    }

    showSuccess(title, message) {
        if (this.app) {
            this.app.showNotification(title, message, 'success');
        }
    }

    showError(title, message) {
        if (this.app) {
            this.app.showNotification(title, message, 'error');
        }
    }
}

// Make it globally accessible for button clicks
window.tradingComponent = null;

// Export for use in main app
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Trading;
}
