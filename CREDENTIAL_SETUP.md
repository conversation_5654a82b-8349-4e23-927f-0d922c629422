# 🔐 Kotak Neo Trade API Credential Setup Guide

This guide will help you configure your Kotak Neo Trade API credentials in the trading bot application.

## 📋 **What You Need**

To use this trading bot, you need the following from Kotak Securities:

1. **Active Kotak Neo Trade Account** with trading permissions
2. **API Access** (must be enabled by Kotak Securities)
3. **Consumer Key** and **Consumer Secret** (provided by Kotak)
4. **Mobile Number** (registered with your trading account)
5. **TOTP Authenticator App** (Google Authenticator, Authy, etc.)

## 🏦 **Step 1: Get API Access from Kotak Securities**

### **Option A: Through Kotak Neo Trade Web Platform**
1. Log in to your **Kotak Neo Trade** account at [https://neo.kotaksecurities.com](https://neo.kotaksecurities.com)
2. Navigate to **Settings** → **API Management**
3. Click on **"Apply for API Access"** or **"Generate API Keys"**
4. Fill out the API application form with:
   - Purpose: Personal Trading Bot
   - Expected volume: Your trading volume
   - Contact details
5. Submit the application and wait for approval (usually 1-2 business days)

### **Option B: Contact Kotak Securities Directly**
1. Call Kotak Securities customer support: **1860-266-0808**
2. Request API access for your trading account
3. Provide your:
   - Client ID (Trading Account Number)
   - Registered mobile number
   - Email address
4. They will guide you through the API application process

## 📱 **Step 2: Setup TOTP Authentication**

1. **Download an Authenticator App**:
   - Google Authenticator (recommended)
   - Microsoft Authenticator
   - Authy
   - Any TOTP-compatible app

2. **Link your account**:
   - Kotak will provide you with a QR code or secret key
   - Scan the QR code or enter the secret key in your authenticator app
   - Your app will start generating 6-digit codes every 30 seconds

## 🔑 **Step 3: Obtain Your API Credentials**

Once approved, you'll receive:

### **Consumer Key**
- Format: Usually 16-32 character alphanumeric string
- Example: `abcd1234efgh5678ijkl9012`

### **Consumer Secret**
- Format: Usually 32-64 character alphanumeric string
- Example: `abcd1234efgh5678ijkl9012mnop3456qrst7890uvwx`

### **User ID**
- This is your trading account login ID
- Format: Usually alphanumeric, 6-12 characters
- Example: `AB1234` or `DEMO123`

## ⚙️ **Step 4: Configure in the Trading Bot**

### **4.1 Start the Application**
```bash
npm start
```

### **4.2 Open Settings**
1. Click the **⚙️ Settings** button in the top-right corner
2. Navigate to the **"API Credentials"** tab

### **4.3 Enter Your Credentials**
Fill in the following fields:

| Field | Description | Example |
|-------|-------------|---------|
| **User ID** | Your trading account login ID | `AB1234` |
| **Password** | Your trading account password | `YourPassword123` |
| **Mobile Number** | Registered mobile (10 digits) | `**********` |
| **Consumer Key** | API Consumer Key from Kotak | `abcd1234efgh5678` |
| **Consumer Secret** | API Consumer Secret from Kotak | `abcd1234efgh5678ijkl9012mnop3456` |
| **Environment** | Select `Production` for live trading | `Production` |

### **4.4 Save and Test**
1. Click **"Save Credentials"**
2. Click **"Test Connection"** to verify your setup
3. If successful, you'll see a green "✓ Connected" status

## 🔐 **Step 5: Authentication Process**

### **5.1 Login to the Application**
1. After saving credentials, you'll see the login screen
2. Enter your **6-digit TOTP code** from your authenticator app
3. Click **"Authenticate"**

### **5.2 Successful Authentication**
- You'll see "Connected" status in the header
- Real-time market data will start flowing
- All trading features will be enabled

## ⚠️ **Important Security Notes**

### **Keep Your Credentials Safe**
- ✅ Credentials are encrypted and stored locally on your computer
- ✅ Never share your Consumer Key/Secret with anyone
- ✅ Use strong passwords for your trading account
- ✅ Enable 2FA on your Kotak account

### **Environment Selection**
- **UAT (Testing)**: Use for testing without real money
- **Production**: Use for live trading with real money
- ⚠️ **Always test in UAT first before going live**

## 🔧 **Troubleshooting**

### **Common Issues and Solutions**

#### **"Invalid Credentials" Error**
- ✅ Double-check User ID, Password, Consumer Key/Secret
- ✅ Ensure API access is approved by Kotak
- ✅ Verify mobile number is correct (10 digits, no spaces)

#### **"TOTP Invalid" Error**
- ✅ Check time sync on your device and authenticator app
- ✅ Ensure you're using the latest 6-digit code
- ✅ Wait for the next code if current one is about to expire

#### **"Connection Failed" Error**
- ✅ Check your internet connection
- ✅ Verify Kotak servers are operational
- ✅ Try switching between UAT and Production environments

#### **"API Access Denied" Error**
- ✅ Contact Kotak Securities to verify API access is enabled
- ✅ Check if your account has trading permissions
- ✅ Ensure account is active and not suspended

### **Getting Help**

1. **Kotak Securities Support**: 1860-266-0808
2. **Email**: <EMAIL>
3. **Check Application Logs**: Look in the console for detailed error messages

## 📞 **Contact Information**

### **Kotak Securities**
- **Customer Care**: 1860-266-0808
- **Email**: <EMAIL>
- **Website**: [https://neo.kotaksecurities.com](https://neo.kotaksecurities.com)

### **API Documentation**
- **Developer Portal**: [https://developers.kotaksecurities.com](https://developers.kotaksecurities.com)
- **API Docs**: Available after API access approval

## ✅ **Quick Checklist**

Before starting trading, ensure:

- [ ] Kotak Neo Trade account is active
- [ ] API access is approved and active
- [ ] Consumer Key and Secret are obtained
- [ ] TOTP authenticator is set up
- [ ] Credentials are configured in the app
- [ ] Connection test is successful
- [ ] Authentication with TOTP works
- [ ] Real-time data is flowing
- [ ] Test orders work in UAT environment

## 🚀 **Ready to Trade!**

Once all steps are completed:
1. Switch to **Production** environment
2. Authenticate with your TOTP code
3. Configure your risk settings
4. Start with small positions to test
5. Monitor the application closely

**Happy Trading! 📈**

---

**Disclaimer**: This is for educational purposes. Trading involves risk. Always test thoroughly before live trading.
