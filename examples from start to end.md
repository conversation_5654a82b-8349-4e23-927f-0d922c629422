Kotak Neo Trade API 1.0.0
Welcome to Kotak NEO APIs - the future of trading
Build your own Trading Platform Today

Kotak Neo’s powerful REST APIs can help you develop your own trading Terminal and implement your strategies easily and intuitively. Our APIs give you open access to live market feeds, orders, live positions, and much more.

What are the benefits of our APIs?
Absolutely Free, No strings attached.
We charge you absolutely nothing! Build your own trading terminal at no cost!

User friendly
Our state-of-the-art APIs help you create user-friendly end-to-end broking services without breaking a sweat.

Our Service
With years of experience, we’re proud to offer a truly market-leading service. You will also be associated with a well-established brand and a full-service broker.

Dependable support
You can count on our dedicated support team for a quick turnaround for all your queries.

Real-time execution
Execute real-time transactions supported by live data feeds.

Fast and seamless
You can place up to 20 orders per second and 200 orders per min.

Generate Access Token
This is the first step before login to authenticate your API keys. You may generate the access token via API, or you can directly generate your access token from the API Manager Portal with an expiry time of your choice.

Please Note the following points:

Username and Password are the API Credentials that you receive after registering for Kotak Neo TradeAPI and not your Demat account credentials

In the Authorisation header field, you must pass the value you get after converting "consumer-key:consumer-secret" into Base64.

The value in Authorization = Basic {Base64({consumer key}:{consumer secret})}

TO SET Expiry of Access Token in WSO2 API Manager check out this


Environment	Endpoint URL
Production	https://napi.kotaksecurities.com/oauth2/token
POST
Access Token
https://napi.kotaksecurities.com/oauth2/token
Generated from cURL: curl -k -X POST https://napi.kotaksecurities.com/oauth2/token -d "grant_type=client_credentials" -H "Authorization: Basic MW1La1VYcWxIdTZlNEw0NDR6RmluRzdjNnRzYTpENGV1bEZZOXc3X0ZBRWRTWW5vRWY4blI2emth"

HEADERS
Authorization
Basic MW1La1VYcWxIdTZlNEw0NDR6RmluRzdjNnRzYTpENGV1bEZZOXc3X0ZBRWRTWW5vRWY4blI2emth

Body
urlencoded
grant_type
client_credentials

Example Request
Access Token
View More
javascript
var settings = {
  "url": "https://napi.kotaksecurities.com/oauth2/token",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Authorization": "Basic MW1La1VYcWxIdTZlNEw0NDR6RmluRzdjNnRzYTpENGV1bEZZOXc3X0ZBRWRTWW5vRWY4blI2emth"
  },
  "data": {
    "grant_type": "client_credentials"
  }
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (14)
View More
json
{
  "access_token": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "scope": "default",
  "token_type": "Bearer",
  "expires_in": *********
}
Login with TOTP
Totp Registration,
go to https://www.kotaksecurities.com/platform/kotak-neo-trade-api/ and select Register for Totp

Totp registration is a one time step where you can register for totp on your mobile and start receiving totps

Step 1 - Verify your mobile no with OTP

Step 2 - Select account, for which you want to register for totp

Step 3 - Select option to register for totp

Step 4 - You will receive a QR code, which is valid for 5mins

Step 5 - Open any authenticator app, and scan the QR code

Step 6 - You will start receiving the Totps on the authenticator apps

Step 7 - Submit the totp on the QR code page to complete the Totp registration

Totp De- registration
Step 1 - go to https://www.kotaksecurities.com/platform/kotak-neo-trade-api/ and select Register for Totp

Step 2 - Verify your mobile no with OTP

Step 3 - Select account, for which you want to register for totp

Step 4 - Select option to de-register for totp

Step 5 - Submit mpin

Step 6 - Totp has been de-registered successfully.

POST
Step 1 - Get View Token
https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/login
Generated from cURL: curl --location 'https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/login'
--header 'Authorization: Bearer eyJ4NXQiOiJNbUprWWpVMlpETmpNelpqTURBM05UZ3pObUUxTm1NNU1qTXpNR1kyWm1OaFpHUTFNakE1TmciLCJraWQiOiJaalJqTUdRek9URmhPV1EwTm1WallXWTNZemRtWkdOa1pUUmpaVEUxTlRnMFkyWTBZVEUyTlRCaVlURTRNak5tWkRVeE5qZ3pPVGM0TWpGbFkyWXpOUV9SUzI1NiIsImFsZyI6IlJTMjU2In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QOSHNa0RhEJx0QLQGIXvUcP_vyeVvvcMBftvCFlJfUaVoWBSobfXHmBSdRfwQvTeoCb1lGpTWb_JinepZ-K9abGheeFCL7RFn17IIRiPxr5RRXYX6lkzD7RzU7QNosOoKyzfccJV3UZ2Qrwi5SJAq5_V1vtZKZnP160WdxgSVHyhSCiSbNcrz4LDbXJn0jAfUzWKwXt5D5xZ-KFJF9vp4r8aZdQV5QREw2rnHhpzR8UE8oeDi3dptSidRjmhQceWtYEeRx94AjsR28b5-q40Hk1aMUo5yls_5dvmfmTFGQ6zo6TGRLgrTff2kHWHIJLTDNh5uPZLo3RpSMwfLQNsew'
--header 'neo-fin-key: neotradeapi'
--header 'Content-Type: application/json'
--data '{
"mobileNumber": "+9170**3",
"ucc": "Y*0",
"totp": "009405"
}'

HEADERS
Authorization
Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

neo-fin-key
neotradeapi

Content-Type
application/json

Body
raw (json)
json
{
    "mobileNumber": "+917********3",
    "ucc": "Y***0",
    "totp": "894913"
}
Example Request
Step 1 - Get View Token
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/login",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/json"
  },
  "data": JSON.stringify({
    "mobileNumber": "+9170******93",
    "ucc": "******",
    "totp": "******"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (27)
View More
json
{
  "data": {
    "token": "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "sid": "b979ea59-9e86-4c50-83b0-099ea7c11b07",
    "rid": "9e13da1c-5186-4bd0-b0dc-6ab819f71e18",
    "hsServerId": "",
    "isUserPwdExpired": false,
    "ucc": "YOJE0",
    "greetingName": "PRIYANKA",
    "isTrialAccount": false,
    "dataCenter": "E21",
    "searchAPIKey": "",
    "derivativesRiskDisclosure": "Risk Disclosure on Derivatives\n\nAs per a SEBI study dated 25 Jan 2023- \n• 9 out of 10 individual traders in equity Futures and Options Segment, incurred net losses.\n• On an average, loss makers registered net trading loss close to Rs.50,000.\n• Over and above the net trading losses incurred, loss makers expended an additional 28% of net trading losses as transaction costs.\n• Those making net trading profits, incurred between 15% to 50% of such profits as transaction cost.\n\nFor more information please check out : https://www.sebi.gov.in/reports-and-statistics/research/jan-2023/study-analysis-of-profit-and-loss-of-individual-traders-dealing-in-equity-fando-segment_67525.html",
    "mfAccess": 1,
    "dataCenterMap": null,
    "dormancyStatus": "A",
    "asbaStatus": "",
    "clientType": "RI",
    "isNRI": false,
    "kId": "AVRPC7535J",
    "kType": "View",
    "status": "success",
    "incRange": 0,
    "incUpdFlag": "",
    "clientGroup": ""
  }
}
POST
Step 2 - Get Final Session Token
https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/validate
Generated from cURL: curl -X 'POST'
'https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/validate'
-H 'accept: application/json'
-H 'sid: x'
-H 'Auth: x'
-H 'neo-fin-key: x'
-H 'Content-Type: application/json'
-H 'Authorization: Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
-d '{
"mpin": "**"
}'

HEADERS
accept
application/json

sid
b979ea59-9e86-4c50-83b0-099ea7c11b07

Auth
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

neo-fin-key
neotradeapi

Content-Type
application/json

Authorization
Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

Body
raw (json)
json
{
  "mpin": "******"
}
Example Request
Step 2 - Get Final Session Token
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/validate",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "sid": "b979ea59-9e86-4c50-83b0-099ea7c11b07",
    "Auth": "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/json",
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": JSON.stringify({
    "mpin": "******"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (27)
View More
json
{
  "data": {
    "token": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "sid": "8a43c2a9-004f-4186-9d9e-824c07014f60",
    "rid": "9e13da1c-5186-4bd0-b0dc-6ab819f71e18",
    "hsServerId": "",
    "isUserPwdExpired": false,
    "ucc": "YOJE0",
    "greetingName": "PRIYANKA",
    "isTrialAccount": false,
    "dataCenter": "E21",
    "searchAPIKey": "",
    "derivativesRiskDisclosure": "Risk Disclosure on Derivatives\n\nAs per a SEBI study dated 25 Jan 2023- \n• 9 out of 10 individual traders in equity Futures and Options Segment, incurred net losses.\n• On an average, loss makers registered net trading loss close to Rs.50,000.\n• Over and above the net trading losses incurred, loss makers expended an additional 28% of net trading losses as transaction costs.\n• Those making net trading profits, incurred between 15% to 50% of such profits as transaction cost.\n\nFor more information please check out : https://www.sebi.gov.in/reports-and-statistics/research/jan-2023/study-analysis-of-profit-and-loss-of-individual-traders-dealing-in-equity-fando-segment_67525.html",
    "mfAccess": 1,
    "dataCenterMap": null,
    "dormancyStatus": "A",
    "asbaStatus": "",
    "clientType": "RI",
    "isNRI": false,
    "kId": "AVRPC7535J",
    "kType": "Trade",
    "status": "success",
    "incRange": 0,
    "incUpdFlag": "",
    "clientGroup": ""
  }
}
Login
Login with OTP will sunset by 31st March, to continue hassel free login continue with TOTP based login.

The API validates the user credentials for login in two steps just like 2FA. You have to hit the same API twice first with password then with OTP.

It returns a JWT token and session id for valid users.

Note: You have to pass the country code as well in mobileNumber.

POST
1st Step: View Token
https://gw-napi.kotaksecurities.com/login/1.0/login/v2/validate
Live API URL : POST https://gw-napi.kotaksecurities.com/login/1.0/login/v2/validate

To generate final session token you need to follow 2 steps

Step I : Generate View Token :

In 1st step of validation, user will receive view token which will be used to generate the final session token

Parameters to be passed :

A) In Headers just pass :

Authorization - Access token in the format :

(Bearer {access_token})

B) In Request body pass the combination of your registered mobile number and password

C) Execute the code

Please Note : The combination of parameters which you can pass in request body are :

mobileNumber and password

pan and password

Choose any one.

D) Response body will contain

1.token - view token

2.sid - session id

These will be used to pass as parameters in Step 2 of Login

Note: If in the response of the first step the following parameter - 'isUserPwdExpired' is True then you have to change your trade password to be able to place orders.

HEADERS
accept
*/*

Content-Type
application/json

Authorization
Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

Body
raw
{
    "mobileNumber": "+917045600893",
    "password": "Kotak@123"
}
Example Request
View Token
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/login/1.0/login/v2/validate",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "*/*",
    "Content-Type": "application/json",
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": JSON.stringify({
    "mobileNumber": "+9170******93",
    "password": "******"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
201 Created
Example Response
Body
Headers (16)
View More
json
{
  "data": {
    "token": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "sid": "ebd3c21a-5174-4db1-9454-d9afd94d7340",
    "rid": "23835a76-9b50-4842-a343-0dbabeb20a8f",
    "hsServerId": "server1",
    "caches": {
      "lastUpdatedTS": "1654684389",
      "multiplewatchlists": "1654684389",
      "watchlist": "1638271224"
    },
    "ucc": "TESTYA24",
    "greetingName": "string"
  }
}
POST
2nd Step : To Generate OTP
https://gw-napi.kotaksecurities.com/login/1.0/login/otp/generate
Live API URL : POST https://gw-napi.kotaksecurities.com/login/1.0/login/otp/generate

In Parameters you need to pass USER ID

Steps to get User ID:

Select the token you have received in response on 1st Step: to generate view token
Go to any only JWT token decoder and put the view token
In decoded Payload you will find a variable : "sub"
This sub variable will contain your user id
Copy this user Id and put in parameters
Example :


In Request body pass :

userId
sendEmail = true
isWhitelisted = true
In Response body you will receive a message that OTP has been sent to your *987 and *@gmail.com - which is your registered mobile number and registered email address.

HEADERS
accept
*/*

Content-Type
application/json

Authorization
Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

Body
raw
{
    "userId": "96389eb1-649f-4297-a51f-1886fc06b83c",
    "sendEmail": true,
    "isWhitelisted": true
}
Example Request
2nd Step : To Generate OTP
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/login/1.0/login/otp/generate",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "*/*",
    "Content-Type": "application/json",
    "Authorization": "Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": JSON.stringify({
    "userId": "************",
    "sendEmail": true,
    "isWhitelisted": true
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
201 Created
Example Response
Body
Headers (16)
json
{
  "data": {
    "greetingName": "PRIYANKA",
    "mobile": "***********93",
    "email": "pr*******************@gmail.com"
  }
}
POST
3rd Step : Session Token using OTP
https://gw-napi.kotaksecurities.com/login/1.0/login/v2/validate
STEP III : LOGIN : Generate Final Session Token using OTP

API : the api used in 3rd step of Login is same that has been used in 1st step of Login

Live API URL : POST https://gw-napi.kotaksecurities.com/login/1.0/login/v2/validate)

A) In Headers:

Sid - Pass the sid generated in step1

Auth - Pass the token generated in step1

Authorization - Access token in the format :

(Bearer {access_token})

B) In Request Body:

Pass userId and OTP which you have recieved on your registered mobile number and email id

userId - Received by decoding view token (In payload data, "sub" variable will contain your userId)
otp
Please Note : Point (B) is a very important step to follow in order to generate valid session token

If this step is not done then the token which is generated will be a invalid token and the user will get an error : “Invalid Token”.

HEADERS
accept
*/*

sid
b3ebb6af-7205-43e9-8513-189472393cab

Auth
eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************.Cm90FmDDREH9Qejas89vXi5-he__B6uMDZMNxtOnElBKZKH71uzG11buq4v-gBGEguxrr0QnyEgcAjYC4nDghmCf-FvI2bqOslIO154f2i-HvP4hHCeoOQCsD9_m8fdF5M_f9_iC9gOiO_8hn3YaKhRBaq_AxqjzhqClX1btuVKMCxyU_cjQCSGiAU383yeM6woJEK_Bsq4O-bfPFBVzcYx5ZJEzLVSn9n0uZuNiJ9ksevdvrHhrha0lpxIXt5IP75bb24qYNUam6SN7oJy6ZNROPDvwf0FUCiKoesj6PyefH1Lmdp5SUu06PY3ZhPAWpTH2OghbvKwygHg972K8RQ

Content-Type
application/json

Authorization
Bearer eyJ4NXQiOiJNbUprWWpVMlpETmpNelpqTURBM05UZ3pObUUxTm1NNU1qTXpNR1kyWm1OaFpHUTFNakE1TmciLCJraWQiOiJaalJqTUdRek9URmhPV1EwTm1WallXWTNZemRtWkdOa1pUUmpaVEUxTlRnMFkyWTBZVEUyTlRCaVlURTRNak5tWkRVeE5qZ3pPVGM0TWpGbFkyWXpOUV9SUzI1NiIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TjUFKzk_bNBT6lsE1uCiWrgwXalmVw7OIdXJwP6IZNPIHSFi5LDlILZWy8LHf2YCUtn_gbWTSum5nIwuh03Tpy-zchFIYzaFbkuYvVQ_nKvxeaf-gzNJnapLYyhvFvYOxabzB_83Q8KIL_ksRTENbWejgHaupmm34LcpHAWFP4FbeeAE9ihtqA_5gPsw1M1ezJS2rTIMtpcz_-9I7AQsPjifCtbeVdAeFqLWvf_qJqTSTySh8HZIMsPBzcqUUaf1e3uzPdxKex3KO-OEFkauqvDivbmsi6J5Ll060Qvp8HZrDM9AsBJr6CrlWqlMNa4RLlsSaLPUab3_yTALKpJ7qA

Body
raw
{
    "userId": "96389eb1-649f-4297-a51f-1886fc06b83c",
    "otp": "7722"
}
Example Request
3rd Step : Session Generation
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/login/1.0/login/v2/validate",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "*/*",
    "sid": "b3ebb6af-7205-43e9-8513-189472393cab",
    "Auth": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************.Cm90FmDDREH9Qejas89vXi5-he__B6uMDZMNxtOnElBKZKH71uzG11buq4v-gBGEguxrr0QnyEgcAjYC4nDghmCf-FvI2bqOslIO154f2i-HvP4hHCeoOQCsD9_m8fdF5M_f9_iC9gOiO_8hn3YaKhRBaq_AxqjzhqClX1btuVKMCxyU_cjQCSGiAU383yeM6woJEK_Bsq4O-bfPFBVzcYx5ZJEzLVSn9n0uZuNiJ9ksevdvrHhrha0lpxIXt5IP75bb24qYNUam6SN7oJy6ZNROPDvwf0FUCiKoesj6PyefH1Lmdp5SUu06PY3ZhPAWpTH2OghbvKwygHg972K8RQ",
    "Content-Type": "application/json",
    "Authorization": "Bearer eyJ4NXQiOiJNbUprWWpVMlpETmpNelpqTURBM05UZ3pObUUxTm1NNU1qTXpNR1kyWm1OaFpHUTFNakE1TmciLCJraWQiOiJaalJqTUdRek9URmhPV1EwTm1WallXWTNZemRtWkdOa1pUUmpaVEUxTlRnMFkyWTBZVEUyTlRCaVlURTRNak5tWkRVeE5qZ3pPVGM0TWpGbFkyWXpOUV9SUzI1NiIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TjUFKzk_bNBT6lsE1uCiWrgwXalmVw7OIdXJwP6IZNPIHSFi5LDlILZWy8LHf2YCUtn_gbWTSum5nIwuh03Tpy-zchFIYzaFbkuYvVQ_nKvxeaf-gzNJnapLYyhvFvYOxabzB_83Q8KIL_ksRTENbWejgHaupmm34LcpHAWFP4FbeeAE9ihtqA_5gPsw1M1ezJS2rTIMtpcz_-9I7AQsPjifCtbeVdAeFqLWvf_qJqTSTySh8HZIMsPBzcqUUaf1e3uzPdxKex3KO-OEFkauqvDivbmsi6J5Ll060Qvp8HZrDM9AsBJr6CrlWqlMNa4RLlsSaLPUab3_yTALKpJ7qA"
  },
  "data": JSON.stringify({
    "userId": "************",
    "otp": "****"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
201 Created
Example Response
Body
Headers (16)
View More
json
{
  "data": {
    "token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.Leq2OMYd-5ezEmG3czEMJxpgkrVmmXMBFJcbOUIeN5ZFFcWT2Ta-fZsyedEcEo4ge8-RP1d0xOMn_xydjw85zBu0zy8eQGmKxZA9kPutQ-tDOdAW_lUBYIbmidZvvZ2FFU5uspFV0fZBHsvaGqDJzEmB6w5n5Rf26IEnyGwQFBtSM1VcihOr13ZvurRFlrnA-x7_r5oi2wweEC7IrhLn6dMc0U8xtHkirYL9NCN_qd0Zcw8HVlM9d2_D_Zg7E-G2zmhlYEUPGHbPYPCQB0dPx4-qyL_TJvJHTqtja0tdpcApUtTiprQHLp5fCwWoa6O4s-SRqVOh3Z0fvIRga2vO1Q",
    "sid": "b3ebb6af-7205-43e9-8513-189472393cab",
    "rid": "a9fe0821-6849-4c68-a083-ddc92d61e060",
    "hsServerId": "server1",
    "caches": {
      "lastUpdatedTS": "1654684389",
      "multiplewatchlists": "1654684389",
      "watchlist": "1638271224"
    },
    "ucc": "TESTYA24",
    "greetingName": "string"
  }
}
Scrip Master
GET
Filename
https://gw-napi.kotaksecurities.com/Files/1.0/masterscrip/v2/file-paths
For order placement, you should use the value in pTrdSymbol column to pass in the "ts" parameter.

Use the dStrikePrice and divide the vlaue by 100 to get the exact strike price.

Note: 'lExpiryDate' is the column you should refer for expiry date. For getting the readable expiry date
1. nse_fo and cde_fo: Add ********* to the epoch value and convert it to IST.

2. mcx_fo and bse_fo: Epoch (lExpiryDate) can be directly converted into human readable date.

For Indices the tokens are their names. Use the below names in place of token for indices.

View More
Exchange Identifier(Token)	Exchange Segment
Nifty 50	nse_cm
Nifty Bank	nse_cm
Nifty Fin Service	nse_cm
SENSEX	nse_cm
INDIA VIX	nse_cm
NIFTY MIDCAP 100	nse_cm
Nifty 100	nse_cm
Nifty PSU Bank	nse_cm
Nifty Pharma	nse_cm
Nifty IT	nse_cm
Nifty PSE	nse_cm
Nifty FMCG	nse_cm
Nifty 500	nse_cm
Nifty Auto	nse_cm
Nifty CPSE	nse_cm
Nifty 200	nse_cm
Nifty Next 50	nse_cm
NIFTY MID SELECT	nse_cm
SENSEX	bse_cm
BANKEX	bse_cm
SNSX50	bse_cm
HEADERS
accept
*/*

Authorization
Bearer eyJ4NXQiOiJNbUprWWpVMlpETmpNelpqTURBM05UZ3pObUUxTm1NNU1qTXpNR1kyWm1OaFpHUTFNakE1TmciLCJraWQiOiJaalJqTUdRek9URmhPV1EwTm1WallXWTNZemRtWkdOa1pUUmpaVEUxTlRnMFkyWTBZVEUyTlRCaVlURTRNak5tWkRVeE5qZ3pPVGM0TWpGbFkyWXpOUV9SUzI1NiIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PNlbb_IScbCwsx_xjSg5elL0kHoScfTpljTpsNEVjLwCSAIXaYmI8mfyOnjXFC8weYYeJvWFU95pT5ehBth9mBGfvRptJbUdPl8NnZQsGHFhkgg2QkNU4n12psRA8Q5LN38pPlZHaCOmNb_OofGTDQ5hwjFoXY5wxLVfB05fN4FIUC-ZDTbqq0Mp7RPAxmLOuNpb1G-w8DQnXLmz_RwpYyCr18vP35yQMpIQzOfvgdWbBvJW_xBDJ9FpdFTjIMvqknGlco8hdXRCpTX-GRGvMh1CVYJdu7JVyp2mpoaxKrASJcAmRnuAdeS6UEVIaAb3rEd-koHjqmp3OQelyf2MJg

Example Request
Filename
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Files/1.0/masterscrip/v2/file-paths",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "accept": "*/*",
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (26)
View More
json
{
  "data": {
    "filesPaths": [
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed/cde_fo.csv",
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed/mcx_fo.csv",
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed/nse_fo.csv",
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed/bse_fo.csv",
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed-v1/nse_cm-v1.csv",
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed-v1/bse_cm-v1.csv"
    ],
    "baseFolder": "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod"
  }
}
Orders
The Order API lets you place, modify and cancel open orders.

After generating sessionToken from Session API and obtaining instrumentToken from Scripmaster API, you are ready to place orders.

POST
Place Order
https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/rule/ms/place
Request of Place Order :

{"am":"NO", "dq":"0","es":"nse_cm", "mp":"0", "pc":"CNC", "pf":"N", "pr":"6.50", "pt":"L", "qt":"1", "rt":"DAY", "tp":"0", "ts":"IDEA-EQ", "tt":"B"}

Request of Place Cover Order :

{"am":"NO","dq":"0","es":"nse_cm","mp":"0","pc":"CO","pf":"N","pr":"7","pt":"L","qt":"1","rt":"DAY","tp":"6.90","ts":"IDEA-EQ","tt":"B"}

Request of Place Bracket Order :

{"am":"NO","dq":"0","es":"nse_cm","mp":"0","pc":"BO","pf":"N","pr":"7","pt":"L","qt":"1","rt":"DAY","tp":"0.0","ts":"IDEA-EQ","tt":"B","sot":"Absolute","slt":"Absolute","slv":"0.5","sov":"0.55","lat":"LTP","tlt":"N","tsv":"0","ig":""}

Values of BO request parameters :

Sot  - should always be Absolute or Ticks

Slt - should always be Absolute or Ticks

Slv - stop loss value (eg : 0.5)

Sov - square off value (eg : 0.55)

tlt - Y / N (Y - trailing stop loss, put Y if required, otherwise N)

tsv - (trailing SL value, if tlt is Y then required, else pass 0)

jData
View More
Field	Type	Description
am	String	AMO
cf	String	Customer firm
dq	String	Disclosed quantity
es	String	Exchange segment
mp	String	Market Protection
pc	String	Product code
pf	String	PosSqrFlg
pr	String	Price
pt	String	Order type
qt	String	Quantity
rt	String	Order Duration
tp	String	Trigger price
ts	String	Trading Symbol
tt	String	Transaction Type
ig	String	GUI Order id
sot	String	Square Off type (Absolute or Ticks) - only for BO
slt	String	Stop loss type (Absolute or Ticks) - only for BO
slv	String	Stop Loss Value - only for BO
sov	String	Square off value - only for BO
tlt	String	Trailing Stop Loss (Y, if required or else N) - only for BO
tsv	String	Trailing SL value (If tlt Y then required, else 0) - only for BO
sc (optional)	String	Tag
HEADERS
accept
application/json

Sid
8a43c2a9-004f-4186-9d9e-824c07014f60

Auth
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

neo-fin-key
neotradeapi

Content-Type
application/x-www-form-urlencoded

Authorization
Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

Body
urlencoded
jData
{"am":"NO", "dq":"0","es":"nse_cm", "mp":"0", "pc":"CNC", "pf":"N", "pr":"6.50", "pt":"L", "qt":"1", "rt":"DAY", "tp":"0", "ts":"IDEA-EQ", "tt":"B"}

Example Request
Place Order
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/rule/ms/place",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Sid": "8a43c2a9-004f-4186-9d9e-824c07014f60",
    "Auth": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": {
    "jData": "{\"am\":\"NO\", \"dq\":\"0\",\"es\":\"nse_cm\", \"mp\":\"0\", \"pc\":\"CNC\", \"pf\":\"N\", \"pr\":\"6.50\", \"pt\":\"L\", \"qt\":\"1\", \"rt\":\"DAY\", \"tp\":\"0\", \"ts\":\"IDEA-EQ\", \"tt\":\"B\"}"
  }
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (21)
json
{
  "nOrdNo": "250319000343209",
  "stat": "Ok",
  "stCode": 200
}
POST
Modify Order
https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/vr/modify
Live API URL : POST https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/vr/modify

Parameters to be passed to place order :

Authorization - Access Token (Format to pass access token is (Bearer {access_token}))

Sid - session Id (Obtained in response body of Login API)

Auth - Session Token (Obtained in response body of Login API)

Neo-fin-key - neotradeapi (pass this value as default value)

sId - hsServerId (Obtained in response body of Login API, This can be either blank, so keep this parameter as optional and you can pass blank value to this to avoid any invalid request body error)

Note: In below postman sample the neo-fin-key is passed of UAT env, In Live API pass neo-fin-key as :

neo-fin-key (for Live Env): neotradeapi

Request Parameter :

{"tk":"11536", "mp":"0", "pc":"NRML", "dd":"NA", "dq":"0", "vd":"DAY", "ts":"TCS-EQ", "tt":"B", "pr":"3001", "tp":"0", "qt":"10", "no":"220106000000185", "es":"nse_cm", "pt":"L"}

FOR understanding in detail request Json body visit : https://www.hypersync.in/apidoc_neo/#api-ORDERS-VR-Modify_Order

View More
Field	Type	Description
tk	String	Token
fq	String	Filled Quantity(optional)
mp	String	MktPro
pc	String	Product code
dd	String	DateDays
dq	String	Disc Quantity
vd	String	Validity
ts	String	Trading symbol
tt	String	Transaction type
pr	String	price
tp	String	Trigger Price
qt	String	Quantity
no	String	nest order no
es	String	Exchange segment
pt	String	Order Type
Success 200

Field	Type	Description
nOrdNo	String	Nest order number
stat	String	Status
Success Response

{

Plain Text
"nOrdNo": "200601000000021",
"stat": "Ok"
}

HEADERS
accept
application/json

Auth
eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.YEnhDlz1PI_LWSaq8twJDuGF02sotp88GQgcpmtUymdQBB2vSrqGAZ1XKA9h1OK1WIfx7OUcF8JoKJKNuSFqxA3WjSzpKaoZ8LKGfogJnPjJcKPcpf-VlDOvSOGI4H2bSW0uDGMNdWAHWmj6U1wG10ibfE__-LlV2alTvtFD4T1x5AhQwAshAmbbh90wltjPi69AI0gf_P04IhGBciqTIVb886tha-10e1VtXKwCrhrafZAeUep-t-VlrhDEkJLEStZShUY0bXz5vA_V8vDPWMLf3Ag8KLND75Jw9g8_4fDqoJSDk7LRj9AX0DvOrnc9juEOTfkDCioOaHQnmgjRrw

Sid
b3ebb6af-7205-43e9-8513-189472393cab

neo-fin-key
f784e198-bda7-439e-a1a6-177f432460b9

Content-Type
application/x-www-form-urlencoded

Authorization
Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

PARAMS
sId
server1

Body
urlencoded
jData
{"tk":"11536", "mp":"0", "pc":"NRML", "dd":"NA", "dq":"0", "vd":"DAY", "ts":"TCS-EQ", "tt":"B", "pr":"3001", "tp":"0", "qt":"10", "no":"220621000007606", "es":"nse_cm", "pt":"L", "am":"NO"}

Pass Data to be modified(Price/Quantity) along with Order No.

Example Request
Modify Order
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/vr/modify",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Auth": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "Sid": "1f47bf35-625b-4d17-a9de-35d827ec2aa2",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": "Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": {
    "jData": "{\"tk\":\"14366\", \"mp\":\"0\", \"pc\":\"CNC\", \"dd\":\"NA\", \"dq\":\"0\", \"vd\":\"DAY\", \"ts\":\"IDEA-EQ\", \"tt\":\"B\", \"pr\":\"3001\", \"tp\":\"0\", \"qt\":\"2\", \"no\":\"240702002199220\", \"es\":\"nse_cm\", \"pt\":\"L\"}"
  }
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (13)
json
{
  "stat": "Ok",
  "nOrdNo": "***************",
  "stCode": 200
}
POST
Cancel Order
https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/cancel
Live API URL : POST https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/cancel

Parameters to be passed to place order :

Authorization - Access Token (Format to pass access token is (Bearer {access_token}))

Sid - session Id (Obtained in response body of Login API)

Auth - Session Token (Obtained in response body of Login API)

Neo-fin-key - neotradeapi (pass this value as default value)

sId - hsServerId (Obtained in response body of Login API, This can be either blank, so keep this parameter as optional and you can pass blank value to this to avoid any invalid request body error)

Note: In below postman sample the neo-fin-key is passed of UAT env, In Live API pass neo-fin-key as :

neo-fin-key (for Live Env): neotradeapi

Request Body :

{"on":"2105199703091997","am":"NO"}

on -> Nest order number (we will get nest order number after order is getting placed Eg: 201118000000025)
am (Optional) -> AMO (Mandatory for AMO)
ts (Optional) -> Trading Symbol (Mandatory for AMO)

Field	Type	Description
am (optional)	String	AMO (Mandatory for AMO)
on	String	Nest order number
ts (optional)	String	Trading Symbol (Mandatory for AMO)
Sample Request :

jData:{"am":"YES","on": "2105199703091997","ts": "TCS-EQ"}

Success 200

Field	Type	Description
result	String	Result/ Nest order number
stat	String	Statu
Success response
{

Plain Text
"result": "2105199703091997",
"stat": "Ok"
}

HEADERS
accept
application/json

Sid
b3ebb6af-7205-43e9-8513-189472393cab

Auth
eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.YEnhDlz1PI_LWSaq8twJDuGF02sotp88GQgcpmtUymdQBB2vSrqGAZ1XKA9h1OK1WIfx7OUcF8JoKJKNuSFqxA3WjSzpKaoZ8LKGfogJnPjJcKPcpf-VlDOvSOGI4H2bSW0uDGMNdWAHWmj6U1wG10ibfE__-LlV2alTvtFD4T1x5AhQwAshAmbbh90wltjPi69AI0gf_P04IhGBciqTIVb886tha-10e1VtXKwCrhrafZAeUep-t-VlrhDEkJLEStZShUY0bXz5vA_V8vDPWMLf3Ag8KLND75Jw9g8_4fDqoJSDk7LRj9AX0DvOrnc9juEOTfkDCioOaHQnmgjRrw

neo-fin-key
f784e198-bda7-439e-a1a6-177f432460b9

Content-Type
application/x-www-form-urlencoded

Authorization
Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

PARAMS
sId
server1

Body
urlencoded
jData
{"on":"220621000007606", "am":"NO"}

Pass Order No. as string against on key

Example Request
Cancel Order
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/cancel",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Sid": "b3ebb6af-7205-43e9-8513-189472393cab",
    "Auth": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.YEnhDlz1PI_LWSaq8twJDuGF02sotp88GQgcpmtUymdQBB2vSrqGAZ1XKA9h1OK1WIfx7OUcF8JoKJKNuSFqxA3WjSzpKaoZ8LKGfogJnPjJcKPcpf-VlDOvSOGI4H2bSW0uDGMNdWAHWmj6U1wG10ibfE__-LlV2alTvtFD4T1x5AhQwAshAmbbh90wltjPi69AI0gf_P04IhGBciqTIVb886tha-10e1VtXKwCrhrafZAeUep-t-VlrhDEkJLEStZShUY0bXz5vA_V8vDPWMLf3Ag8KLND75Jw9g8_4fDqoJSDk7LRj9AX0DvOrnc9juEOTfkDCioOaHQnmgjRrw",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": "Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": {
    "jData": "{\"on\":\"***************\"}"
  }
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (13)
json
{
  "stat": "Ok",
  "result": "***************",
  "stCode": 200
}
POST
Cancel Cover Order
https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/co/exit
Generated from cURL: curl --location 'https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/co/exit'
--header 'accept: application/json'
--header 'Sid: 556b6a3f-c469-4394-b84f-058426d38b41'
--header 'Auth: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
--header 'neo-fin-key: neotradeapi'
--header 'Content-Type: application/x-www-form-urlencoded'
--header 'Authorization: Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
--data-urlencode 'jData={ "am": "NO", "on": "250325000241281" }'

HEADERS
accept
application/json

Sid
556b6a3f-c469-4394-b84f-058426d38b41

Auth
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

neo-fin-key
neotradeapi

Content-Type
application/x-www-form-urlencoded

Authorization
Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

Body
urlencoded
jData
{ "am": "NO", "on": "250325000241281" }

Example Request
Cancel Cover Order
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/co/exit",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Sid": "556b6a3f-c469-4394-b84f-058426d38b41",
    "Auth": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": "Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": {
    "jData": "{
  \"am\": \"NO\",
  \"on\": \"250325000321343\"
}"
  }
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (21)
json
{
  "result": "SUCCESS",
  "stat": "Ok",
  "stCode": 0
}
POST
Cancel Bracket Order
https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/bo/exit
Generated from cURL: curl --location 'https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/bo/exit'
--header 'accept: application/json'
--header 'Sid: 556b6a3f-c469-4394-b84f-058426d38b41'
--header 'Auth: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
--header 'neo-fin-key: neotradeapi'
--header 'Content-Type: application/x-www-form-urlencoded'
--header 'Authorization: Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
--data-urlencode 'jData={ "am": "NO", "on": "250325000302749" }'

HEADERS
accept
application/json

Sid
556b6a3f-c469-4394-b84f-058426d38b41

Auth
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

neo-fin-key
neotradeapi

Content-Type
application/x-www-form-urlencoded

Authorization
Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

Body
urlencoded
jData
{ "am": "NO", "on": "250325000302749" }

Example Request
Cancel Bracket Order
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/bo/exit",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Sid": "556b6a3f-c469-4394-b84f-058426d38b41",
    "Auth": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": "Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": {
    "jData": "{
  \"am\": \"NO\",
  \"on\": \"250325000324313\"
}"
  }
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (21)
json
{
  "result": "SUCCESS",
  "stat": "Ok",
  "stCode": 0
}
Order Report
GET
Order Book
https://gw-napi.kotaksecurities.com/Orders/2.0/quick/user/orders?sId=server1
To get in detail understanding of Order Books Click Here

Parameters to be passed to order Book :

Authorization - Access Token (Format to pass access token is (Bearer {Access Token}))

Sid - session Id (Obtained in response body of Login API)

Auth - Session Token (Obtained in response body of Login API)

Neo-fin-key - neotradeapi (pass this value as default value)

sId - hsServerId (Obtained in response body of Login API, This can be either blank, so keep this parameter as optional and you can pass blank value to this to avoid any invalid request body error)

View More
Field	Type	Description
algId	String	Algo id
avgPrc	String	Average price
brdLtQty	String	Board Lot Quantity
brkClnt	String	Broker client
cnlQty	Integer	Cancelled quantity
coPct	Integer	Cover order percentage
defMktProV	String	Default market protection value (Deprecated)
dscQtyPct	String	Disclose quantity percentage for exchanges (Deprecated)
dscQty	Integer	Disclosed quantity
exUsrInfo	String	Exchange user info
exCfmTm	String	Exchange confirmation time
exOrdId	String	Exchange order id
expDt	String	Expiry date (Deprecated)
expDtSsb	String	Expiry date of scrip in ssboe format (Deprecated)
exSeg	String	Exchange segment
fldQty	Integer	Filled quantity
boeSec	Integer	boeSec
mktProPct	String	Market protection percentage (Deprecated)
mktPro	String	Market protection
mfdBy	String	Modified by user
mktProFlg	String	Market protection flag (Deprecated)
noMktProFlg	String	No market protection flag (Deprecated)
nOrdNo	String	Nest order number
optTp	String	Option type (Deprecated)
ordAutSt	String	Order auth status (Deprecated)
odCrt	String	Order criteria
ordDtTm	String	Ordered time (Deprecated)
ordEntTm	String	Order entry time
ordGenTp	String	Order generation type
ordSrc	String	Order source
ordValDt	String	Order validity date
prod	String	Product code
prc	String	Price
prcTp	String	Order type
qty	Integer	Quantity
refLmtPrc	String	Reference limit price for CO order
rejRsn	String	Rejection reason
rmk	String	rmk
rptTp	String	Report type (Deprecated)
reqId	String	Request id
series	String	Series (Deprecated)
sipInd	String	Sip indicatior
stat	String	Status (Deprecated)
ordSt	String	Status
stkPrc	String	Strike price
sym	String	Symbol
symOrdId	String	Sym order id
tckSz	String	Tick size
tok	String	Token
trnsTp	String	Transaction type
trgPrc	String	Trigger price
unFldSz	String	Unfilled size
usrId	String	User
vldt	String	Validity
classification	String	Classification ID
vendorCode	String	Vendor Code
genDen	String	General Denominator
genNum	String	General Numerator
prcNum	String	Price Numerator
prcDen	String	Price Denominator
lotSz	String	Lot Size
multiplier	String	Multiplier
precision	String	Precision
hsUpTm	String	Last Updated Time
GuiOrdId	String	Gui Order ID
locId	String	Location Id
Sample Response :

{

View More
Plain Text
"stat": "Ok",
"stCode": 200,
"data": \[
    {
        "brkClnt": "--",
        "ordValDt": "NA",
        "exUsrInfo": "NA",
        "mfdBy": "NA",
        "vendorCode": "",
        "rmk": "--",
        "odCrt": "NA",
        "ordSrc": "ADMINCPPAPI_",
        "sipInd": "NA",
        "prc": "1.00",
        "prcTp": "L",
        "cnlQty": 0,
        "uSec": "975529",
        "classification": "0",
        "mktPro": "0.00",
        "ordEntTm": "--",
        "reqId": "1",
        "qty": 10,
        "unFldSz": 0,
        "mktProPct": "--",
        "exOrdId": "NA",
        "dscQty": 10,
        "expDt": "NA",
        "trgPrc": "0.00",
        "tok": "11536",
        "symOrdId": "NA",
        "fldQty": 0,
        "ordDtTm": "07-Oct-2022 15:44:45",
        "avgPrc": "0.00",
        "locId": "111111111111000",
        "algId": "NA",
        "stat": "Ok",
        "prod": "NRML",
        "exSeg": "nse_cm",
        "GuiOrdId": "**********-936751-PRABHAT-ADMINAPI",
        "usrId": "PRABHAT",
        "rptTp": "NA",
        "exCfmTm": "--",
        "hsUpTm": "2022/10/07 17:24:55",
        "ordGenTp": "NA",
        "vldt": "DAY",
        "tckSz": "0.05",
        "ordSt": "rejected",
        "trnsTp": "B",
        "refLmtPrc": 0,
        "coPct": 0,
        "nOrdNo": "***************",
        "ordAutSt": "NA",
        "rejRsn": "RMS:Rule: Check circuit limit including square off order exceeds  for entity account-PRABHAT across exchange across segment across product ",
        "boeSec": **********,
        "expDtSsb": "--",
        "dscQtyPct": "0",
        "stkPrc": "0.00",
        "sym": "TCS",
        "trdSym": "TCS-EQ",
        "multiplier": "1",
        "precision": "2",
        "noMktProFlg": "0.00",
        "genNum": "1",
        "series": "EQ",
        "prcNum": "1",
        "genDen": "1",
        "brdLtQty": "1",
        "mktProFlg": "0.00",
        "defMktProV": "0.00",
        "lotSz": "1",
        "minQty": 0,
        "optTp": "XX",
        "prcDen": "1"
    }
\]
}

HEADERS
accept
application/json

Sid
b3ebb6af-7205-43e9-8513-189472393cab

Auth
eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.YEnhDlz1PI_LWSaq8twJDuGF02sotp88GQgcpmtUymdQBB2vSrqGAZ1XKA9h1OK1WIfx7OUcF8JoKJKNuSFqxA3WjSzpKaoZ8LKGfogJnPjJcKPcpf-VlDOvSOGI4H2bSW0uDGMNdWAHWmj6U1wG10ibfE__-LlV2alTvtFD4T1x5AhQwAshAmbbh90wltjPi69AI0gf_P04IhGBciqTIVb886tha-10e1VtXKwCrhrafZAeUep-t-VlrhDEkJLEStZShUY0bXz5vA_V8vDPWMLf3Ag8KLND75Jw9g8_4fDqoJSDk7LRj9AX0DvOrnc9juEOTfkDCioOaHQnmgjRrw

neo-fin-key
neotradeapi

String, Unique Key provided to Fintech/consumers by KSL

Authorization
Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

PARAMS
sId
server1

Example Request
Order Book
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Orders/2.0/quick/user/orders",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Sid": "b3ebb6af-7205-43e9-8513-189472393cab",
    "Auth": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.YEnhDlz1PI_LWSaq8twJDuGF02sotp88GQgcpmtUymdQBB2vSrqGAZ1XKA9h1OK1WIfx7OUcF8JoKJKNuSFqxA3WjSzpKaoZ8LKGfogJnPjJcKPcpf-VlDOvSOGI4H2bSW0uDGMNdWAHWmj6U1wG10ibfE__-LlV2alTvtFD4T1x5AhQwAshAmbbh90wltjPi69AI0gf_P04IhGBciqTIVb886tha-10e1VtXKwCrhrafZAeUep-t-VlrhDEkJLEStZShUY0bXz5vA_V8vDPWMLf3Ag8KLND75Jw9g8_4fDqoJSDk7LRj9AX0DvOrnc9juEOTfkDCioOaHQnmgjRrw",
    "neo-fin-key": "neotradeapi",
    "Authorization": "Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (13)
View More
json
{
  "stat": "Ok",
  "stCode": 200,
  "data": [
    {
      "brkClnt": "--",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 0,
      "uSec": "364529",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "--",
      "reqId": "1",
      "qty": 1,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "NA",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "11536",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 14:49:32",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "**********-359607-TESTY1124A-ADMINAPI",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "--",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "DAY",
      "tckSz": "0.05",
      "ordSt": "rejected",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "RMS:Rule: Check circuit limit including square off order exceeds  for entity account-TESTYA24 across exchange across segment across product ",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "TCS",
      "trdSym": "TCS-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "--",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 0,
      "uSec": "379164",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "--",
      "reqId": "1",
      "qty": 1,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "NA",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "11536",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 14:48:39",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "**********-377269-TESTY1124A-ADMINAPI",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "--",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "DAY",
      "tckSz": "0.05",
      "ordSt": "rejected",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "RMS:Rule: Check circuit limit including square off order exceeds  for entity account-TESTYA24 across exchange across segment across product ",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "TCS",
      "trdSym": "TCS-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "--",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 0,
      "uSec": "990007",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "--",
      "reqId": "1",
      "qty": 1,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "NA",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "11536",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 14:30:37",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "SMCS_oO7saO2M",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "--",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "IOC",
      "tckSz": "0.05",
      "ordSt": "rejected",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "RMS:Rule: Check circuit limit including square off order exceeds  for entity account-TESTYA24 across exchange across segment across product ",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "TCS",
      "trdSym": "TCS-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "08081",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 0,
      "uSec": "014523",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "21-Jun-2022 14:30:38",
      "reqId": "1",
      "qty": 5,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "****************",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "5900",
      "symOrdId": "NA",
      "fldQty": 5,
      "ordDtTm": "21-Jun-2022 14:30:38",
      "avgPrc": "658.70",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "SMCS_xcwlNEK4",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "21-Jun-2022 14:30:38",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "IOC",
      "tckSz": "0.05",
      "ordSt": "complete",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "--",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "AXISBANK",
      "trdSym": "AXISBANK-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "08081",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 0,
      "uSec": "997689",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "21-Jun-2022 14:30:37",
      "reqId": "1",
      "qty": 15,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "****************",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "1660",
      "symOrdId": "NA",
      "fldQty": 15,
      "ordDtTm": "21-Jun-2022 14:30:37",
      "avgPrc": "268.10",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "SMCS_zqqYkPFW",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "21-Jun-2022 14:30:37",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "IOC",
      "tckSz": "0.05",
      "ordSt": "complete",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "--",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "ITC",
      "trdSym": "ITC-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "08081",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 2,
      "uSec": "990914",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "21-Jun-2022 14:30:37",
      "reqId": "1",
      "qty": 2,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "1100000000056015",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "1333",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 14:30:37",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "SMCS_AL4Sd9y3",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "21-Jun-2022 14:30:37",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "IOC",
      "tckSz": "0.05",
      "ordSt": "cancelled",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "16388 : Unmatched orders cancelled by the system  ",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "HDFCBANK",
      "trdSym": "HDFCBANK-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "08081",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 1,
      "uSec": "561984",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "21-Jun-2022 14:21:07",
      "reqId": "1",
      "qty": 1,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "****************",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "1333",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 14:21:07",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "SMCS_1Mf8WSrN",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "21-Jun-2022 14:21:07",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "IOC",
      "tckSz": "0.05",
      "ordSt": "cancelled",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "16388 : Unmatched orders cancelled by the system  ",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "HDFCBANK",
      "trdSym": "HDFCBANK-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "08081",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 0,
      "uSec": "556960",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "21-Jun-2022 14:21:07",
      "reqId": "1",
      "qty": 2,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "****************",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "5900",
      "symOrdId": "NA",
      "fldQty": 2,
      "ordDtTm": "21-Jun-2022 14:21:07",
      "avgPrc": "641.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "SMCS_edg9l5UO",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "21-Jun-2022 14:21:07",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "IOC",
      "tckSz": "0.05",
      "ordSt": "complete",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "--",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "AXISBANK",
      "trdSym": "AXISBANK-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "08081",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 0,
      "uSec": "065827",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "21-Jun-2022 14:07:50",
      "reqId": "1",
      "qty": 2,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "****************",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "5900",
      "symOrdId": "NA",
      "fldQty": 2,
      "ordDtTm": "21-Jun-2022 14:07:50",
      "avgPrc": "643.70",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "SMCS_kAeDQgi1",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "21-Jun-2022 14:07:50",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "IOC",
      "tckSz": "0.05",
      "ordSt": "complete",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "--",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "AXISBANK",
      "trdSym": "AXISBANK-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "--",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 0,
      "uSec": "014372",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "--",
      "reqId": "1",
      "qty": 1,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "NA",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "11536",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 14:07:27",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "SMCS_11xzz",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "--",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "IOC",
      "tckSz": "0.05",
      "ordSt": "rejected",
      "trnsTp": "S",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "RMS:Rule: Check circuit limit including square off order exceeds  for entity account-TESTYA24 across exchange across segment across product ",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "TCS",
      "trdSym": "TCS-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "--",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 0,
      "uSec": "138095",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "--",
      "reqId": "1",
      "qty": 1,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "NA",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "11536",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 13:46:52",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "**********-121583-TESTY1124A-ADMINAPI",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "--",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "DAY",
      "tckSz": "0.05",
      "ordSt": "rejected",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "RMS:Rule: Check circuit limit including square off order exceeds  for entity account-TESTYA24 across exchange across segment across product ",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "TCS",
      "trdSym": "TCS-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "--",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 0,
      "uSec": "975980",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "--",
      "reqId": "1",
      "qty": 1,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "NA",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "11536",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 12:57:54",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "**********-973859-TESTY1124A-ADMINAPI",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "--",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "DAY",
      "tckSz": "0.05",
      "ordSt": "rejected",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "RMS:Rule: Check circuit limit including square off order exceeds  for entity account-TESTYA24 across exchange across segment across product ",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "TCS",
      "trdSym": "TCS-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "08081",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "450.00",
      "prcTp": "L",
      "cnlQty": 0,
      "uSec": "479411",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "21-Jun-2022 12:31:26",
      "reqId": "1",
      "qty": 1,
      "unFldSz": 1,
      "mktProPct": "--",
      "exOrdId": "1100000000021938",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "3045",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 12:31:26",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "NRML",
      "exSeg": "nse_cm",
      "GuiOrdId": "1655794886-438565-TESTY1124A-ADMINAPI",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "21-Jun-2022 12:31:26",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "DAY",
      "tckSz": "0.05",
      "ordSt": "open",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "220621000000053",
      "ordAutSt": "NA",
      "rejRsn": "--",
      "boeSec": 1655794886,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "SBIN",
      "trdSym": "SBIN-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "--",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "450.00",
      "prcTp": "L",
      "cnlQty": 0,
      "uSec": "381275",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "--",
      "reqId": "1",
      "qty": 1,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "NA",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "3045",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 10:35:41",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "NRML",
      "exSeg": "nse_cm",
      "GuiOrdId": "**********-377025-TESTY1124A-ADMINAPI",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "--",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "DAY",
      "tckSz": "0.05",
      "ordSt": "rejected",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "RMS:Margin Exceeds,Cash Available:0.00,Additional margin required:450.00 for entity account-TESTYA24 across exchange across segment across product ",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "SBIN",
      "trdSym": "SBIN-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "--",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 0,
      "uSec": "972237",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "--",
      "reqId": "1",
      "qty": 1,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "NA",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "3045",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 10:32:35",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "NRML",
      "exSeg": "nse_cm",
      "GuiOrdId": "**********-968798-TESTY1124A-ADMINAPI",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "--",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "DAY",
      "tckSz": "0.05",
      "ordSt": "rejected",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "RMS:Margin Exceeds,Cash Available:0.00,Additional margin required:455.05 for entity account-TESTYA24 across exchange across segment across product ",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "SBIN",
      "trdSym": "SBIN-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "--",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 0,
      "uSec": "887816",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "21-Jun-2022 10:32:25",
      "reqId": "1",
      "qty": 1,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "1100000000000249",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "3045",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 10:32:25",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "1655787745-856688-TESTY1124A-ADMINAPI",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "--",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "DAY",
      "tckSz": "0.05",
      "ordSt": "rejected",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "220621000000006",
      "ordAutSt": "NA",
      "rejRsn": "16387 : Security is not allowed to trade in this market  ",
      "boeSec": 1655787745,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "SBIN",
      "trdSym": "SBIN-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "--",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 0,
      "uSec": "701200",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "--",
      "reqId": "1",
      "qty": 1,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "NA",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "3045",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 10:32:17",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "NRML",
      "exSeg": "nse_cm",
      "GuiOrdId": "**********-698166-TESTY1124A-ADMINAPI",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "--",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "DAY",
      "tckSz": "0.05",
      "ordSt": "rejected",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "RMS:Margin Exceeds,Cash Available:0.00,Additional margin required:455.05 for entity account-TESTYA24 across exchange across segment across product ",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "SBIN",
      "trdSym": "SBIN-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "--",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 0,
      "uSec": "714965",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "21-Jun-2022 10:32:05",
      "reqId": "1",
      "qty": 1,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "1100000000000247",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "3045",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 10:32:05",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "1655787725-697246-TESTY1124A-ADMINAPI",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "--",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "DAY",
      "tckSz": "0.05",
      "ordSt": "rejected",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "220621000000004",
      "ordAutSt": "NA",
      "rejRsn": "16387 : Security is not allowed to trade in this market  ",
      "boeSec": 1655787725,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "SBIN",
      "trdSym": "SBIN-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "08081",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "TESTY1124A",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 1,
      "uSec": "477697",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "21-Jun-2022 10:28:36",
      "reqId": "1",
      "qty": 1,
      "unFldSz": 1,
      "mktProPct": "--",
      "exOrdId": "1100000000000213",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "3045",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 10:28:36",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "1655787482-366947-TESTY1124A-ADMINAPI",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "21-Jun-2022 10:28:36",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "DAY",
      "tckSz": "0.05",
      "ordSt": "cancelled",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "220621000000003",
      "ordAutSt": "NA",
      "rejRsn": "--",
      "boeSec": 1655787516,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "SBIN",
      "trdSym": "SBIN-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "brkClnt": "--",
      "ordValDt": "NA",
      "exUsrInfo": "NA",
      "mfdBy": "NA",
      "vendorCode": "",
      "rmk": "--",
      "odCrt": "NA",
      "ordSrc": "ADMINCPPAPI_MOB",
      "sipInd": "NA",
      "prc": "0.00",
      "prcTp": "MKT",
      "cnlQty": 0,
      "uSec": "388520",
      "classification": "0",
      "mktPro": "0.00",
      "ordEntTm": "--",
      "reqId": "1",
      "qty": 1,
      "unFldSz": 0,
      "mktProPct": "--",
      "exOrdId": "NA",
      "dscQty": 0,
      "expDt": "NA",
      "trgPrc": "0.00",
      "tok": "11536",
      "symOrdId": "NA",
      "fldQty": 0,
      "ordDtTm": "21-Jun-2022 10:26:56",
      "avgPrc": "0.00",
      "algId": "NA",
      "stat": "Ok",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "GuiOrdId": "**********-371808-TESTY1124A-ADMINAPI",
      "usrId": "TESTY1124A",
      "rptTp": "NA",
      "exCfmTm": "--",
      "hsUpTm": "2022/06/21 14:55:28",
      "ordGenTp": "NA",
      "vldt": "DAY",
      "tckSz": "0.05",
      "ordSt": "rejected",
      "trnsTp": "B",
      "refLmtPrc": 0,
      "coPct": 0,
      "nOrdNo": "***************",
      "ordAutSt": "NA",
      "rejRsn": "RMS:Rule: Check circuit limit including square off order exceeds  for entity account-TESTYA24 across exchange across segment across product ",
      "boeSec": **********,
      "expDtSsb": "--",
      "dscQtyPct": "0",
      "stkPrc": "0.00",
      "sym": "TCS",
      "trdSym": "TCS-EQ",
      "multiplier": "1",
      "precision": "2",
      "noMktProFlg": "0.00",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": "1",
      "mktProFlg": "0.00",
      "defMktProV": "0.00",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    }
  ]
}
POST
Order History
https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/history?sId=server1
To get in detail understanding of Order History Click Here

Parameters to be passed to order History :

Authorization - Access Token (Format to pass access token is (Bearer {Access Token}))

Sid - session Id (Obtained in response body of Login API)

Auth - Session Token (Obtained in response body of Login API)

Neo-fin-key - neotradeapi (pass this value as default value)

sId - hsServerId (Obtained in response body of Login API, This can be either blank, so keep this parameter as optional and you can pass blank value to this to avoid any invalid request body error)

Request Body

Field	Type	Description
nOrdNo	String	Nest order number
Success Response Field

View More
Field	Type	Description
trdSym	String	Trading symbol
prc	String	Price
qty	Integer	Quantity
ordSt	String	Order status
trnsTp	String	Transaction type
prcTp	String	Order type
brdLtQty	Integer	Board lot quantity
exch	String	Exchange
exchTmstp	String	Exchange time stamp
nOrdNo	String	Nest order number
nReqId	String	Nest request id
sym	String	Symbol name
avgPrc	String	Average price
trgPrc	String	Trigger price
dclQty	String	Disclosed quantity
exchOrdId	String	Exchange order id
rejRsn	String	Rejection reason
ordDur	String	Order duration
prod	String	Product type
rptTp	String	Report type (Deprecated)
cstFrm	String	Customer firm
ordSrc	String	Order source
flDtTm	String	Filled date & time
ordGenTp	String	Order generation type
scripName	String	Scrip name
legOrdInd	String	Leg order indicator (Deprecated)
fldQty	Integer	Filled shares/quantity
ordUsrMsg	String	Order user message (Deprecated)
mfdBy	String	Modified by
unFldSz	Integer	Unfilled size
vendorCode	String	Vendor Code
classification	String	Classification ID
prcNum	String	Price numerator
genNum	String	General numerator
prcDen	String	Price denominator
genDen	String	General denominator
lotSz	String	Lot Size
multiplier	String	Multiplier
precision	String	Precision
mktPro	String	Market Protection
GuiOrdId	String	Gui Order ID
symOrdId	String	Reference Order Number
locId	String	Location Id
Sample Success Response Data

View More
json
{
    "stat": "Ok",
    "stCode": 200,
    "data": [
        {
            "cstFrm": "C",
            "mfdBy": "XX",
            "vendorCode": "",
            "trgPrc": "0.00",
            "symOrdId": "NA",
            "fldQty": 0,
            "avgPrc": "0.00",
            "ordSrc": "ADMINCPPAPI_",
            "locId": "NA",
            "prc": "1.00",
            "prcTp": "L",
            "nReqId": "1",
            "dclQty": "10",
            "prod": "NRML",
            "trdSym": "TCS-EQ",
            "GuiOrdId": "**********-936751-PRABHAT-ADMINAPI",
            "rptTp": "NA",
            "classification": "0",
            "mktPro": "0.00",
            "ordGenTp": "--",
            "flDtTm": "07-Oct-2022 15:44:45",
            "ordSt": "put order req received",
            "trnsTp": "B",
            "qty": 10,
            "exch": "NSE",
            "nOrdNo": "***************",
            "legOrdInd": "NA",
            "unFldSz": 10,
            "exchOrdId": "NA",
            "ordUsrMsg": "NA",
            "ordDur": "DAY",
            "exchTmstp": "--",
            "rejRsn": "--",
            "scripName": "TATA CONSULTANCY SERV LT",
            "exSeg": "nse_cm",
            "sym": "TCS",
            "stkPrc": "0.00",
            "multiplier": "1",
            "precision": "2",
            "expDt": "NA",
            "tok": "11536",
            "genNum": "1",
            "prcNum": "1",
            "series": "EQ",
            "genDen": "1",
            "brdLtQty": 1,
            "lotSz": "1",
            "prcDen": "1",
            "optTp": "XX"
        }
    ]
}
HEADERS
accept
application/json

Sid
b3ebb6af-7205-43e9-8513-189472393cab

Auth
eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.YEnhDlz1PI_LWSaq8twJDuGF02sotp88GQgcpmtUymdQBB2vSrqGAZ1XKA9h1OK1WIfx7OUcF8JoKJKNuSFqxA3WjSzpKaoZ8LKGfogJnPjJcKPcpf-VlDOvSOGI4H2bSW0uDGMNdWAHWmj6U1wG10ibfE__-LlV2alTvtFD4T1x5AhQwAshAmbbh90wltjPi69AI0gf_P04IhGBciqTIVb886tha-10e1VtXKwCrhrafZAeUep-t-VlrhDEkJLEStZShUY0bXz5vA_V8vDPWMLf3Ag8KLND75Jw9g8_4fDqoJSDk7LRj9AX0DvOrnc9juEOTfkDCioOaHQnmgjRrw

neo-fin-key
neotradeapi

Content-Type
application/x-www-form-urlencoded

Authorization
Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

PARAMS
sId
server1

Body
urlencoded
jData
{"nOrdNo":"220621000007606"}

Pass Order No. in json to get Order History

Example Request
Order History
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/history",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Sid": "b3ebb6af-7205-43e9-8513-189472393cab",
    "Auth": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.YEnhDlz1PI_LWSaq8twJDuGF02sotp88GQgcpmtUymdQBB2vSrqGAZ1XKA9h1OK1WIfx7OUcF8JoKJKNuSFqxA3WjSzpKaoZ8LKGfogJnPjJcKPcpf-VlDOvSOGI4H2bSW0uDGMNdWAHWmj6U1wG10ibfE__-LlV2alTvtFD4T1x5AhQwAshAmbbh90wltjPi69AI0gf_P04IhGBciqTIVb886tha-10e1VtXKwCrhrafZAeUep-t-VlrhDEkJLEStZShUY0bXz5vA_V8vDPWMLf3Ag8KLND75Jw9g8_4fDqoJSDk7LRj9AX0DvOrnc9juEOTfkDCioOaHQnmgjRrw",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": "Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": {
    "jData": "{\"nOrdNo\":\"***************\"}"
  }
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (13)
View More
json
{
  "stat": "Ok",
  "stCode": 200,
  "data": [
    {
      "cstFrm": "C",
      "mfdBy": "XX",
      "vendorCode": "",
      "trgPrc": "0.00",
      "symOrdId": "NA",
      "fldQty": 0,
      "avgPrc": "0.00",
      "ordSrc": "ADMINCPPAPI_MOB",
      "prc": "0.00",
      "prcTp": "MKT",
      "nReqId": "1",
      "dclQty": "0",
      "prod": "CNC",
      "trdSym": "TCS-EQ",
      "GuiOrdId": "**********-377269-TESTY1124A-ADMINAPI",
      "rptTp": "NA",
      "classification": "0",
      "mktPro": "0.00",
      "ordGenTp": "--",
      "flDtTm": "21-Jun-2022 14:48:39",
      "ordSt": "rejected",
      "trnsTp": "B",
      "qty": 1,
      "exch": "NSE",
      "nOrdNo": "***************",
      "legOrdInd": "NA",
      "unFldSz": 0,
      "exchOrdId": "NA",
      "ordUsrMsg": "NA",
      "ordDur": "DAY",
      "exchTmstp": "--",
      "rejRsn": "RMS:Rule: Check circuit limit including square off order exceeds  for entity account-TESTYA24 across exchange across segment across product ",
      "scripName": "TATA CONSULTANCY SERV LT",
      "exSeg": "nse_cm",
      "sym": "TCS",
      "stkPrc": "0.00",
      "multiplier": "1",
      "precision": "2",
      "expDt": "NA",
      "tok": "11536",
      "genNum": "1",
      "prcNum": "1",
      "series": "EQ",
      "genDen": "1",
      "brdLtQty": 1,
      "lotSz": "1",
      "prcDen": "1",
      "optTp": "XX"
    },
    {
      "cstFrm": "C",
      "mfdBy": "XX",
      "vendorCode": "",
      "trgPrc": "0.00",
      "symOrdId": "NA",
      "fldQty": 0,
      "avgPrc": "0.00",
      "ordSrc": "ADMINCPPAPI_MOB",
      "prc": "0.00",
      "prcTp": "MKT",
      "nReqId": "1",
      "dclQty": "0",
      "prod": "CNC",
      "trdSym": "TCS-EQ",
      "GuiOrdId": "**********-377269-TESTY1124A-ADMINAPI",
      "rptTp": "NA",
      "classification": "0",
      "mktPro": "0.00",
      "ordGenTp": "--",
      "flDtTm": "21-Jun-2022 14:48:39",
      "ordSt": "validation pending",
      "trnsTp": "B",
      "qty": 1,
      "exch": "NSE",
      "nOrdNo": "***************",
      "legOrdInd": "NA",
      "unFldSz": 1,
      "exchOrdId": "NA",
      "ordUsrMsg": "NA",
      "ordDur": "DAY",
      "exchTmstp": "--",
      "rejRsn": "--",
      "scripName": "TATA CONSULTANCY SERV LT",
      "exSeg": "nse_cm",
      "sym": "TCS",
      "stkPrc": "0.00",
      "multiplier": "1",
      "precision": "2",
      "expDt": "NA",
      "tok": "11536",
      "genNum": "1",
      "prcNum": "1",
      "series": "EQ",
      "genDen": "1",
      "brdLtQty": 1,
      "lotSz": "1",
      "prcDen": "1",
      "optTp": "XX"
    },
    {
      "cstFrm": "C",
      "mfdBy": "XX",
      "vendorCode": "",
      "trgPrc": "0.00",
      "symOrdId": "NA",
      "fldQty": 0,
      "avgPrc": "0.00",
      "ordSrc": "ADMINCPPAPI_MOB",
      "prc": "0.00",
      "prcTp": "MKT",
      "nReqId": "1",
      "dclQty": "0",
      "prod": "CNC",
      "trdSym": "TCS-EQ",
      "GuiOrdId": "**********-377269-TESTY1124A-ADMINAPI",
      "rptTp": "NA",
      "classification": "0",
      "mktPro": "0.00",
      "ordGenTp": "--",
      "flDtTm": "21-Jun-2022 14:48:39",
      "ordSt": "put order req received",
      "trnsTp": "B",
      "qty": 1,
      "exch": "NSE",
      "nOrdNo": "***************",
      "legOrdInd": "NA",
      "unFldSz": 1,
      "exchOrdId": "NA",
      "ordUsrMsg": "NA",
      "ordDur": "DAY",
      "exchTmstp": "--",
      "rejRsn": "--",
      "scripName": "TATA CONSULTANCY SERV LT",
      "exSeg": "nse_cm",
      "sym": "TCS",
      "stkPrc": "0.00",
      "multiplier": "1",
      "precision": "2",
      "expDt": "NA",
      "tok": "11536",
      "genNum": "1",
      "prcNum": "1",
      "series": "EQ",
      "genDen": "1",
      "brdLtQty": 1,
      "lotSz": "1",
      "prcDen": "1",
      "optTp": "XX"
    }
  ]
}
GET
Trade Book
https://gw-napi.kotaksecurities.com/Orders/2.0/quick/user/trades?sId=server1
To get in detail understanding of Trade Book Click Here

Parameters to be passed to Trade Book :

Authorization - Access Token (Format to pass access token is (Bearer {Access Token}))

Sid - session Id (Obtained in response body of Login API)

Auth - Session Token (Obtained in response body of Login API)

Neo-fin-key - neotradeapi (pass this value as default value)

sId - hsServerId (Obtained in response body of Login API, This can be either blank, so keep this parameter as optional and you can pass blank value to this to avoid any invalid request body error)

Success Response Field Details

View More
Field	Type	Description
algCat	String	Algo category
algId	String	Algo id
avgPrc	String	Average price
brkClnt	String	Broker client
cstFrm	String	Customer firm
exOrdId	String	Exchange order id
exSeg	String	Exchange segment
exTm	String	Exchange time
exp	String	Expiry
flId	String	Fill id
flLeg	Integer	Filled leg
flDt	String	Filled date
fldQty	Integer	Filled quantity
flTm	String	Filled time
minQty	Integer	Minimum quantity
nReqId	String	Nest order request id
nOrdNo	String	Nest order number
ordDur	String	Order duration
prod	String	Product code
prcTp	String	Order type
prc	String	Price (Deprecated)
qty	Integer	Quantity (Deprecated)
rptTp	String	Report type
sym	String	Symbol
tok	String	Token
tm	String	Time (Deprecated)
trnsTp	String	Transaction type
trdSym	String	Trading symbol
actId	String	Account id
brdLtQty	Integer	Board lot quantity
expDt	String	Expiry date
optTp	String	Option type
ordGenTp	String	Order generation type
posFlg	String	Position flag (Deprecated)
rmk	String	Remarks
series	String	Series
stat	String	Status (Deprecated)
stkPrc	String	Strike price
usrId	String	User id
genDen	String	General Denominator
genNum	String	General Numerator
prcNum	String	Price Numerator
prcDen	String	Price Denominator
lotSz	String	Lot Size
multiplier	String	Multiplier
precision	String	Precision
ordSrc	String	Order Source
hsUpTm	String	Last Updated Time
locId	String	Location Id
Sample Success Response

View More
json
{
    "stat": "Ok",
    "stCode": 200,
    "data": [
        {
            "exOrdId": "****************",
            "brkClnt": "NA",
            "cstFrm": "C",
            "actId": "PRABHAT",
            "rmk": "--",
            "fldQty": 11,
            "flDt": "07-Oct-2022",
            "avgPrc": "3194.00",
            "ordSrc": "NA",
            "algId": "NA",
            "locId": "333333333333100",
            "prcTp": "L",
            "prod": "CNC",
            "exTm": "07-Oct-2022 13:04:14",
            "nReqId": "1",
            "exSeg": "nse_cm",
            "trdSym": "TCS-EQ",
            "GuiOrdId": "1665128054-406046-PRABHAT-ADMINAPI",
            "flLeg": 1,
            "rptTp": "fill",
            "usrId": "PRABHAT",
            "hsUpTm": "2022/10/07 17:21:27",
            "ordGenTp": "--",
            "flId": "50030822",
            "flTm": "13:04:14",
            "trnsTp": "B",
            "nOrdNo": "221007000000354",
            "algCat": "NA",
            "ordDur": "DAY",
            "boeSec": 1665128054,
            "stkPrc": "0.00",
            "sym": "TCS",
            "multiplier": "1",
            "precision": "2",
            "expDt": "NA",
            "tok": "11536",
            "genNum": "1",
            "series": "EQ",
            "prcNum": "1",
            "genDen": "1",
            "brdLtQty": 1,
            "exp": "--",
            "lotSz": "1",
            "minQty": 0,
            "optTp": "XX",
            "prcDen": "1"
        }
    ]
}
HEADERS
accept
application/json

Sid
b3ebb6af-7205-43e9-8513-189472393cab

Auth
eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.YEnhDlz1PI_LWSaq8twJDuGF02sotp88GQgcpmtUymdQBB2vSrqGAZ1XKA9h1OK1WIfx7OUcF8JoKJKNuSFqxA3WjSzpKaoZ8LKGfogJnPjJcKPcpf-VlDOvSOGI4H2bSW0uDGMNdWAHWmj6U1wG10ibfE__-LlV2alTvtFD4T1x5AhQwAshAmbbh90wltjPi69AI0gf_P04IhGBciqTIVb886tha-10e1VtXKwCrhrafZAeUep-t-VlrhDEkJLEStZShUY0bXz5vA_V8vDPWMLf3Ag8KLND75Jw9g8_4fDqoJSDk7LRj9AX0DvOrnc9juEOTfkDCioOaHQnmgjRrw

neo-fin-key
neotradeapi

Authorization
Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

PARAMS
sId
server1

Example Request
Trade Book
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Orders/2.0/quick/user/trades",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Sid": "b3ebb6af-7205-43e9-8513-189472393cab",
    "Auth": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.YEnhDlz1PI_LWSaq8twJDuGF02sotp88GQgcpmtUymdQBB2vSrqGAZ1XKA9h1OK1WIfx7OUcF8JoKJKNuSFqxA3WjSzpKaoZ8LKGfogJnPjJcKPcpf-VlDOvSOGI4H2bSW0uDGMNdWAHWmj6U1wG10ibfE__-LlV2alTvtFD4T1x5AhQwAshAmbbh90wltjPi69AI0gf_P04IhGBciqTIVb886tha-10e1VtXKwCrhrafZAeUep-t-VlrhDEkJLEStZShUY0bXz5vA_V8vDPWMLf3Ag8KLND75Jw9g8_4fDqoJSDk7LRj9AX0DvOrnc9juEOTfkDCioOaHQnmgjRrw",
    "neo-fin-key": "neotradeapi",
    "Authorization": "Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (13)
View More
json
{
  "stat": "Ok",
  "stCode": 200,
  "data": [
    {
      "exOrdId": "****************",
      "brkClnt": "NA",
      "cstFrm": "C",
      "actId": "TESTYA24",
      "rmk": "--",
      "fldQty": 5,
      "flDt": "21-Jun-2022",
      "avgPrc": "658.70",
      "ordSrc": null,
      "algId": "NA",
      "prcTp": "MKT",
      "prod": "CNC",
      "exTm": "21-Jun-2022 14:30:38",
      "nReqId": "1",
      "exSeg": "nse_cm",
      "trdSym": "AXISBANK-EQ",
      "GuiOrdId": "SMCS_xcwlNEK4",
      "flLeg": 1,
      "rptTp": "fill",
      "usrId": "TESTY1124A",
      "hsUpTm": "2022/06/21 15:08:47",
      "ordGenTp": "--",
      "flId": "42857",
      "flTm": "14:30:38",
      "trnsTp": "B",
      "nOrdNo": "***************",
      "algCat": "NA",
      "ordDur": "DAY",
      "boeSec": **********,
      "stkPrc": "0.00",
      "sym": "AXISBANK",
      "multiplier": "1",
      "precision": "2",
      "expDt": "NA",
      "tok": "5900",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": 1,
      "exp": "--",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "exOrdId": "****************",
      "brkClnt": "NA",
      "cstFrm": "C",
      "actId": "TESTYA24",
      "rmk": "--",
      "fldQty": 15,
      "flDt": "21-Jun-2022",
      "avgPrc": "268.10",
      "ordSrc": null,
      "algId": "NA",
      "prcTp": "MKT",
      "prod": "CNC",
      "exTm": "21-Jun-2022 14:30:37",
      "nReqId": "1",
      "exSeg": "nse_cm",
      "trdSym": "ITC-EQ",
      "GuiOrdId": "SMCS_zqqYkPFW",
      "flLeg": 1,
      "rptTp": "fill",
      "usrId": "TESTY1124A",
      "hsUpTm": "2022/06/21 15:08:47",
      "ordGenTp": "--",
      "flId": "********",
      "flTm": "14:30:37",
      "trnsTp": "B",
      "nOrdNo": "***************",
      "algCat": "NA",
      "ordDur": "DAY",
      "boeSec": **********,
      "stkPrc": "0.00",
      "sym": "ITC",
      "multiplier": "1",
      "precision": "2",
      "expDt": "NA",
      "tok": "1660",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": 1,
      "exp": "--",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "exOrdId": "****************",
      "brkClnt": "NA",
      "cstFrm": "C",
      "actId": "TESTYA24",
      "rmk": "--",
      "fldQty": 2,
      "flDt": "21-Jun-2022",
      "avgPrc": "641.00",
      "ordSrc": null,
      "algId": "NA",
      "prcTp": "MKT",
      "prod": "CNC",
      "exTm": "21-Jun-2022 14:21:07",
      "nReqId": "1",
      "exSeg": "nse_cm",
      "trdSym": "AXISBANK-EQ",
      "GuiOrdId": "SMCS_edg9l5UO",
      "flLeg": 1,
      "rptTp": "fill",
      "usrId": "TESTY1124A",
      "hsUpTm": "2022/06/21 15:08:47",
      "ordGenTp": "--",
      "flId": "41349",
      "flTm": "14:21:07",
      "trnsTp": "B",
      "nOrdNo": "***************",
      "algCat": "NA",
      "ordDur": "DAY",
      "boeSec": **********,
      "stkPrc": "0.00",
      "sym": "AXISBANK",
      "multiplier": "1",
      "precision": "2",
      "expDt": "NA",
      "tok": "5900",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": 1,
      "exp": "--",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "exOrdId": "****************",
      "brkClnt": "NA",
      "cstFrm": "C",
      "actId": "TESTYA24",
      "rmk": "--",
      "fldQty": 1,
      "flDt": "21-Jun-2022",
      "avgPrc": "644.65",
      "ordSrc": null,
      "algId": "NA",
      "prcTp": "MKT",
      "prod": "CNC",
      "exTm": "21-Jun-2022 14:07:50",
      "nReqId": "1",
      "exSeg": "nse_cm",
      "trdSym": "AXISBANK-EQ",
      "GuiOrdId": "SMCS_kAeDQgi1",
      "flLeg": 1,
      "rptTp": "fill",
      "usrId": "TESTY1124A",
      "hsUpTm": "2022/06/21 15:08:47",
      "ordGenTp": "--",
      "flId": "35790",
      "flTm": "14:07:50",
      "trnsTp": "B",
      "nOrdNo": "***************",
      "algCat": "NA",
      "ordDur": "DAY",
      "boeSec": **********,
      "stkPrc": "0.00",
      "sym": "AXISBANK",
      "multiplier": "1",
      "precision": "2",
      "expDt": "NA",
      "tok": "5900",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": 1,
      "exp": "--",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "exOrdId": "****************",
      "brkClnt": "NA",
      "cstFrm": "C",
      "actId": "TESTYA24",
      "rmk": "--",
      "fldQty": 1,
      "flDt": "21-Jun-2022",
      "avgPrc": "642.75",
      "ordSrc": null,
      "algId": "NA",
      "prcTp": "MKT",
      "prod": "CNC",
      "exTm": "21-Jun-2022 14:07:50",
      "nReqId": "1",
      "exSeg": "nse_cm",
      "trdSym": "AXISBANK-EQ",
      "GuiOrdId": "SMCS_kAeDQgi1",
      "flLeg": 1,
      "rptTp": "fill",
      "usrId": "TESTY1124A",
      "hsUpTm": "2022/06/21 15:08:47",
      "ordGenTp": "--",
      "flId": "35789",
      "flTm": "14:07:50",
      "trnsTp": "B",
      "nOrdNo": "***************",
      "algCat": "NA",
      "ordDur": "DAY",
      "boeSec": **********,
      "stkPrc": "0.00",
      "sym": "AXISBANK",
      "multiplier": "1",
      "precision": "2",
      "expDt": "NA",
      "tok": "5900",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": 1,
      "exp": "--",
      "lotSz": "1",
      "minQty": 0,
      "optTp": "XX",
      "prcDen": "1"
    }
  ]
}
Positions
To get in detail understanding of Positions Book Click Here

Parameters to be passed to cancel order :

Authorization - Access Token (Format to pass access token is (Bearer {Access Token}))

Sid - session Id (Obtained in response body of Login API)

Auth - Session Token (Obtained in response body of Login API)

Neo-fin-key - neotradeapi (pass this value as default value)

sId - hsServerId (Obtained in response body of Login API, This can be either blank, so keep this parameter as optional and you can pass blank value to this to avoid any invalid request body error)

GET
Position Book
https://gw-napi.kotaksecurities.com/Orders/2.0/quick/user/positions?sId=server1
Success Response Fields Details

View More
Fields	Type	Description
actId	String	Account id
brdLtQty	Integer	Board lot quantity
cfBuyAmt	String	CF buy amount
cfSellAmt	String	CF sell amount
cfBuyQty	String	CF buy quantity
cfSellQty	String	CF sell quantity
exSeg	String	Exchange segment
buyAmt	String	Fill buy amount
sellAmt	String	Fill sell amount
flBuyQty	String	Fill buy quantity
flSellQty	String	Fill sell quantity
optTp	String	Option type
prod	String	Product code
series	String	Series
stkPrc	String	Strike price
tok	String	Token
trdSym	String	Trading symbol
type	String	Type
sym	String	Symbol name
genDen	String	General Denominator
genNum	String	General Numerator
prcNum	String	Price Numerator
prcDen	String	Price Denominator
posFlg	String	Position flag
lotSz	String	Lot Size
multiplier	String	Multiplier
precision	String	Precision
hsUpTm	String	Last Updated Time
Sample Success Response Detail

View More
json
{
    "stat": "Ok",
    "stCode": 200,
    "data": [
        {
            "buyAmt": "2625.00",
            "cfSellAmt": "0.00",
            "prod": "NRML",
            "exSeg": "nse_fo",
            "sqrFlg": "Y",
            "actId": "PRS2206",
            "cfBuyQty": "0",
            "cfSellQty": "0",
            "tok": "53179",
            "flBuyQty": "25",
            "flSellQty": "25",
            "sellAmt": "2625.00",
            "posFlg": "true",
            "cfBuyAmt": "0.00",
            "stkPrc": "0.00",
            "trdSym": "BANKNIFTY21JULFUT",
            "sym": "BANKNIFTY",
            "expDt": "29 Jul, 2021",
            "type": "FUTIDX",
            "series": "XX",
            "brdLtQty": 25,
            "exp": "**********",
            "optTp": "XX",
            "genNum": "1",
            "genDen": "1",
            "prcNum": "1",
            "prcDen": "1",
            "lotSz": "25",
            "multiplier": "1",
            "precision": "2",
            "hsUpTm": "2021/07/13 18:34:44"
        }
    ]
}
HEADERS
accept
application/json

Sid
b3ebb6af-7205-43e9-8513-189472393cab

Auth
eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.YEnhDlz1PI_LWSaq8twJDuGF02sotp88GQgcpmtUymdQBB2vSrqGAZ1XKA9h1OK1WIfx7OUcF8JoKJKNuSFqxA3WjSzpKaoZ8LKGfogJnPjJcKPcpf-VlDOvSOGI4H2bSW0uDGMNdWAHWmj6U1wG10ibfE__-LlV2alTvtFD4T1x5AhQwAshAmbbh90wltjPi69AI0gf_P04IhGBciqTIVb886tha-10e1VtXKwCrhrafZAeUep-t-VlrhDEkJLEStZShUY0bXz5vA_V8vDPWMLf3Ag8KLND75Jw9g8_4fDqoJSDk7LRj9AX0DvOrnc9juEOTfkDCioOaHQnmgjRrw

neo-fin-key
neotradeapi

Authorization
Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

PARAMS
sId
server1

Example Request
Position Book
View More
javascript
var settings = {
  "url": "https://nsbxapi-gw.kotaksecurities.com/Orders/2.0/quick/user/positions",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Sid": "b3ebb6af-7205-43e9-8513-189472393cab",
    "Auth": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.YEnhDlz1PI_LWSaq8twJDuGF02sotp88GQgcpmtUymdQBB2vSrqGAZ1XKA9h1OK1WIfx7OUcF8JoKJKNuSFqxA3WjSzpKaoZ8LKGfogJnPjJcKPcpf-VlDOvSOGI4H2bSW0uDGMNdWAHWmj6U1wG10ibfE__-LlV2alTvtFD4T1x5AhQwAshAmbbh90wltjPi69AI0gf_P04IhGBciqTIVb886tha-10e1VtXKwCrhrafZAeUep-t-VlrhDEkJLEStZShUY0bXz5vA_V8vDPWMLf3Ag8KLND75Jw9g8_4fDqoJSDk7LRj9AX0DvOrnc9juEOTfkDCioOaHQnmgjRrw",
    "neo-fin-key": "f784e198-bda7-439e-a1a6-177f432460b9",
    "Authorization": "Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (13)
View More
json
{
  "stat": "Ok",
  "stCode": 200,
  "data": [
    {
      "buyAmt": "5862.90",
      "cfSellAmt": "0.00",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "sqrFlg": "Y",
      "actId": "TESTYA24",
      "cfBuyQty": "0",
      "hsUpTm": "2022/06/21 15:11:02",
      "cfSellQty": "0",
      "tok": "5900",
      "flBuyQty": "9",
      "flSellQty": "0",
      "sellAmt": "0.00",
      "posFlg": "true",
      "cfBuyAmt": "0.00",
      "stkPrc": "0.00",
      "trdSym": "AXISBANK-EQ",
      "sym": "AXISBANK",
      "multiplier": "1",
      "precision": "2",
      "expDt": "NA",
      "type": "NA",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": 1,
      "exp": "--",
      "lotSz": "1",
      "optTp": "XX",
      "prcDen": "1"
    },
    {
      "buyAmt": "4021.50",
      "cfSellAmt": "0.00",
      "prod": "CNC",
      "exSeg": "nse_cm",
      "sqrFlg": "Y",
      "actId": "TESTYA24",
      "cfBuyQty": "0",
      "hsUpTm": "2022/06/21 15:11:02",
      "cfSellQty": "0",
      "tok": "1660",
      "flBuyQty": "15",
      "flSellQty": "0",
      "sellAmt": "0.00",
      "posFlg": "true",
      "cfBuyAmt": "0.00",
      "stkPrc": "0.00",
      "trdSym": "ITC-EQ",
      "sym": "ITC",
      "multiplier": "1",
      "precision": "2",
      "expDt": "NA",
      "type": "NA",
      "genNum": "1",
      "series": "EQ",
      "prcNum": "1",
      "genDen": "1",
      "brdLtQty": 1,
      "exp": "--",
      "lotSz": "1",
      "optTp": "XX",
      "prcDen": "1"
    }
  ]
}
Portfolio
GET
Porfolio-Holdings
https://gw-napi.kotaksecurities.com/Portfolio/1.0/portfolio/v1/holdings?alt=false
HEADERS
accept
*/*

sid
6f8446e5-c43e-4e72-9f8f-b3b827355ba7

Auth
eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************.LE2gZl72PHNlSceKNufBpUGe0rD1OB8imT2nBhwqnk7xFxtY0JJLJKFr919eUqoGHD3Nv0kwDGmtd1HUYrx6pN4I1mhISa3XGYwJRt2GrXhR99pEtun3DZsDa3Bxd2lIPnpwRIdB_yn8JyVn9BxqCEOx84T1j_WDcrFi5Y-E0fzwoc067IJkc1j81gZR-7_4TQ92pQaYoo2_ht1baOL1KhrZBL9DmindBN6FzgTLz-TMmyNHh5PPz0xhQY6eKBRvW4A_a0AuBCACuK4R5h1gGmUaGQ0LWjgFmnHIHfq5jaWP95koFq_CyUzGMfMYJYEpRnu8Eu0qaARPLgbDu0YGRA

Authorization
Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

PARAMS
alt
false

Example Request
Porfolio-Holdings
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Portfolio/1.0/portfolio/v1/holdings?alt=false",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "accept": "*/*",
    "sid": "6f8446e5-c43e-4e72-9f8f-b3b827355ba7",
    "Auth": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************.LE2gZl72PHNlSceKNufBpUGe0rD1OB8imT2nBhwqnk7xFxtY0JJLJKFr919eUqoGHD3Nv0kwDGmtd1HUYrx6pN4I1mhISa3XGYwJRt2GrXhR99pEtun3DZsDa3Bxd2lIPnpwRIdB_yn8JyVn9BxqCEOx84T1j_WDcrFi5Y-E0fzwoc067IJkc1j81gZR-7_4TQ92pQaYoo2_ht1baOL1KhrZBL9DmindBN6FzgTLz-TMmyNHh5PPz0xhQY6eKBRvW4A_a0AuBCACuK4R5h1gGmUaGQ0LWjgFmnHIHfq5jaWP95koFq_CyUzGMfMYJYEpRnu8Eu0qaARPLgbDu0YGRA",
    "Authorization": "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (13)
View More
json
{
  "data": [
    {
      "symbol": "YESBANK",
      "displaySymbol": "YESBANK",
      "averagePrice": 21.1225,
      "quantity": 4,
      "exchangeSegment": "nse_cm",
      "exchangeIdentifier": "11915",
      "holdingCost": 84.49,
      "mktValue": 79,
      "scripId": "dade08eae3d978dcb31940b6da2cfbab4ab395d3",
      "instrumentToken": 7169,
      "instrumentType": "Equity",
      "isAlternateScrip": false,
      "closingPrice": 19.75
    },
    {
      "symbol": "YESBANK",
      "displaySymbol": "YESBANK",
      "averagePrice": 21.1225,
      "quantity": 4,
      "exchangeSegment": "bse_cm",
      "exchangeIdentifier": "532648",
      "holdingCost": 84.49,
      "mktValue": 79,
      "scripId": "40297c23c30022e35db0e59e7ca3a30c7a5c6906",
      "instrumentToken": 7168,
      "instrumentType": "Equity",
      "isAlternateScrip": true,
      "closingPrice": 19.75
    },
    {
      "symbol": "CESC",
      "displaySymbol": "CESC",
      "averagePrice": 80.01,
      "quantity": 2,
      "exchangeSegment": "nse_cm",
      "exchangeIdentifier": "628",
      "holdingCost": 160.02,
      "mktValue": 147.6,
      "scripId": "fb94935fb38a1dd7f87c52e562d6756636fcb7f3",
      "instrumentToken": 955,
      "instrumentType": "Equity",
      "isAlternateScrip": false,
      "closingPrice": 73.8
    },
    {
      "symbol": "CESC",
      "displaySymbol": "CESC",
      "averagePrice": 80.01,
      "quantity": 2,
      "exchangeSegment": "bse_cm",
      "exchangeIdentifier": "500084",
      "holdingCost": 160.02,
      "mktValue": 147.6,
      "scripId": "22995f58a180b89e279e9d74df05545bc7fd02c9",
      "instrumentToken": 954,
      "instrumentType": "Equity",
      "isAlternateScrip": true,
      "closingPrice": 73.8
    },
    {
      "symbol": "GPIL",
      "displaySymbol": "GPIL",
      "averagePrice": 280.4275,
      "quantity": 4,
      "exchangeSegment": "nse_cm",
      "exchangeIdentifier": "13409",
      "holdingCost": 1121.71,
      "mktValue": 1629.4,
      "scripId": "5960fc030a164a0af93334089dc9cf2a8896594e",
      "instrumentToken": 7964,
      "instrumentType": "Equity",
      "isAlternateScrip": false,
      "closingPrice": 407.35
    },
    {
      "symbol": "GPIL",
      "displaySymbol": "GPIL",
      "averagePrice": 280.4275,
      "quantity": 4,
      "exchangeSegment": "bse_cm",
      "exchangeIdentifier": "532734",
      "holdingCost": 1121.71,
      "mktValue": 1629.4,
      "scripId": "21a74b89385b75997f4c19891074f5e1a538bf49",
      "instrumentToken": 7963,
      "instrumentType": "Equity",
      "isAlternateScrip": true,
      "closingPrice": 407.35
    }
  ]
}
Limits
POST
Limits
https://gw-napi.kotaksecurities.com/Orders/2.0/quick/user/limits?sId=server1
To get in detail understanding of RMS Limit Click Here

Parameters to be passed to Limits :

Authorization - Access Token (Format to pass access token is (Bearer {Access Token}))

Sid - session Id (Obtained in response body of Login API)

Auth - Session Token (Obtained in response body of Login API)

Neo-fin-key - neotradeapi (pass this value as default value)

sId - hsServerId (Obtained in response body of Login API, This can be either blank, so keep this parameter as optional and you can pass blank value to this to avoid any invalid request body error)

Request Body :

json
jData:{"seg": "CASH","exch": "NSE","prod": "ALL"}
Success Response Fields Description

View More
Field	Type	Description
AddMrgnMisPrsnt	String	Additional margin for MIS
AddMrgnNrmlPrsnt	String	Additional margin for NRML
AddPreExpMrgnMisPrsnt	String	Additional pre-expiry margin for MIS
AddPreExpMrgnNrmlPrsnt	String	Additional pre-expiry margin for NRML
AdhocLimitMult	String	Adhoc limit multiplier
AdhocMargin	String	Adhoc margin
AmountUtilizedPrsnt	String	Amount utilized
AmtUntilizedPrsnt	String	Amount un-utilized
BoardLotLimit	String	Board lot limit
BrokeragePrsnt	String	Brokerage percentage
CashRlsMtomPrsnt	String	Cash realised MtoM
CashUnRlsMtomPrsnt	String	Cash unrealised MtoM
Category	String	Category
CdsSpreadBenefit	String	CDS spread benefit
CncMrgnVarPrsnt	String	CNC margin value at risk
CncSellcrdPresent	String	CNC sell credits
Collateral	String	Collateral
CollateralValue	String	Collateral value
CollateralValueMult	String	Collateral value multiplier
ComExpsrMrgnMisPrsnt	String	COM exposure margin for MIS
ComExpsrMrgnNrmlPrsnt	String	COM exposure margin for NRML
ComRlsMtomPrsnt	String	COM realised MtoM
ComSpanMrgnMisPrsnt	String	COM span margin for MIS
ComSpanMrgnNrmlPrsnt	String	COM span margin for NRML
ComUnRlsMtomPrsnt	String	COM unrealised MtoM
CurExpMrgnMisPrsnt	String	CUR exposure margin for MIS
CurExpMrgnNrmlPrsnt	String	CUR exposure margin for NRML
CurPremiumMisPrsnt	String	CUR premium for MIS
CurPremiumNrmlPrsnt	String	CUR premium for NRML
CurRlsMtomPrsnt	String	CUR realised MtoM
CurSpanMrgnMisPrsnt	String	CUR span margin for MIS
CurSpanMrgnNrmlPrsnt	String	CUR span margin for NRML
CurUnRlsMtomPrsnt	String	CUR unrealised MtoM
DaneLimit	String	Dane limit
DeliveryMarginPresent	String	Delivery margin
DeliveryMrgnMisPrsnt	String	Delivery margin MIS
DeliveryMrgnNrmlPrsnt	String	Delivery margin NRML
ExposureMarginPrsnt	String	Exposure margin
FoExpMrgnMisPrsnt	String	FO exposure margin for MIS
FoExpMrgnNrmlPrsnt	String	FO exposure margin for NRML
FoPremiumMisPrsnt	String	FO premium for MIS
FoPremiumNrmlPrsnt	String	FO premium for NRML
FoRlsMtomPrsnt	String	FO realised MtoM
FoSpanrgnMisPrsnt	String	FO span margin for MIS
FoSpanrgnNrmlPrsnt	String	FO span margin for NRML
FoUnRlsMtomPrsnt	String	FO unrealised MtoM
MarginScripBasketPrsnt	String	Margin scrip basket
MarginUsed	String	Margin used
MarginUsedPrsnt	String	Margin used present
MarginVarPrsnt	String	Margin for value at risk
MarginWarningPrcntPrsnt	String	Margin warning percentage
MrgnScrpBsktCncPrsnt	String	Margin scrip basket for CNC
MrgnScrpBsktMisPrsnt	String	Margin scrip basket for MIS
MrgnScrpBsktNrmlPrsnt	String	Margin scrip basket for NRML
MrgnVarMisPrsnt	String	Margin value at risk for MIS
MrgnVarNrmlPrsnt	String	Margin value at risk for NRML
MtomSquareOffWarningPrcntPrsnt	String	MtoM square off warning percentage
MtomWarningPrcntPrsnt	String	MtoM warning percentage
NationalCashMult	String	National cash
Net	String	Net cash available
NfospreadBenefit	String	NFO spread benefit
NotionalCash	String	Notional cash
PremiumPrsnt	String	Premium present
RealizedMtomPrsnt	String	Realized MtoM present
RmsCollateral	String	RMS collateral
RmsCollateralMult	String	RMS collateral multiplier
RmsPayInAmt	String	RMS pay-in amount
RmsPayOutAmt	String	RMS pay-out amount
SpanMarginPrsnt	String	Span margin
SpecialMarginPrsnt	String	Special margin
SplMrgnMisPrsnt	String	Special margin for MIS
SplMrgnNrmlPrsnt	String	Special margin for NRML
TenderMrgnMisPrsnt	String	Tender margin for MIS
TenderMrgnNrmlPrsnt	String	Tender margin for NRML
UnrealizedMtomPrsnt	String	Unrealized MtoM
AuxRmsCollateral	String	AUX RMS Collateral
AuxAdhocMargin	String	AUX Daylong Cash
AuxNotionalCash	String	AUX Uncleared Cash
stat	String	Status
SlbmLimit	String	SLBM Limit
SlbmVarElm	String	SLBM Var Elm
SlbmMtom	String	SLBM M to M
LendingFee	String	Lending Fee
RmsMutualFundAmt	String	RMS Mutual Fund Amount
Sample Success Response :

View More
json
{
    "AddPreExpMrgnMisPrsnt": "0.00",
    "CurExpMrgnNrmlPrsnt": "0.00",
    "CurPremiumNrmlPrsnt": "0.00",
    "RmsPayOutAmt": "0.00",
    "AdhocMargin": "0.00",
    "MrgnScrpBsktCncPrsnt": "0.00",
    "FoPremiumMisPrsnt": "0.00",
    "MrgnVarNrmlPrsnt": "0.00",
    "AmountUtilizedPrsnt": "0.00",
    "SplMrgnMisPrsnt": "0.00",
    "CurSpanMrgnNrmlPrsnt": "0.00",
    "SlbmVarElm": "0.00",
    "AuxRmsCollateral": "0.00",
    "FoUnRlsMtomPrsnt": "0.00",
    "AuxNotionalCash": "0.00",
    "AuxAdhocMargin": "0.00",
    "DaneLimit": "0.00",
    "RmsMutualFundAmt": "0.00",
    "RmsPayInAmt": "0.00",
    "CurPremiumMisPrsnt": "0.00",
    "SlbmLimit": "0.00",
    "MarginWarningPrcntPrsnt": "0.00",
    "MrgnVarMisPrsnt": "0.00",
    "CurRlsMtomPrsnt": "0.00",
    "ExposureMarginPrsnt": "0.00",
    "NfospreadBenefit": "0.00",
    "stCode": 200,
    "CollateralValue": "0.00",
    "AddMrgnNrmlPrsnt": "0.00",
    "RmsCollateralMult": "0.00",
    "CncMrgnVarPrsnt": "0.00",
    "BoardLotLimit": "0",
    "ComRlsMtomPrsnt": "0.00",
    "MarginVarPrsnt": "0.00",
    "DeliveryMarginPresent": "0.00",
    "RmsCollateral": "0.00",
    "ComExpsrMrgnMisPrsnt": "0.00",
    "CollateralValueMult": "0.00",
    "FoSpanrgnMisPrsnt": "0.00",
    "Category": "NA",
    "CurUnRlsMtomPrsnt": "0.00",
    "SpanMarginPrsnt": "0.00",
    "CncSellcrdPresent": "0.00",
    "Collateral": "0.00",
    "MrgnScrpBsktNrmlPrsnt": "0.00",
    "UnrealizedMtomPrsnt": "0.00",
    "MrgnScrpBsktMisPrsnt": "0.00",
    "RealizedMtomPrsnt": "0.00",
    "MtomWarningPrcntPrsnt": "0.00",
    "ComSpanMrgnNrmlPrsnt": "0.00",
    "DeliveryMrgnNrmlPrsnt": "0.00",
    "AdhocLimitMult": "0.00",
    "FoPremiumNrmlPrsnt": "0.00",
    "SpecialMarginPrsnt": "0.00",
    "SplMrgnNrmlPrsnt": "0.00",
    "MtomSquareOffWarningPrcntPrsnt": "0.00",
    "MarginUsedPrsnt": "0.00",
    "FoRlsMtomPrsnt": "0.00",
    "stat": "Ok",
    "NationalCashMult": "0.00",
    "FoSpanrgnNrmlPrsnt": "0.00",
    "AmtUntilizedPrsnt": "0.00",
    "PremiumPrsnt": "0.00",
    "AddMrgnMisPrsnt": "0.00",
    "DeliveryMrgnMisPrsnt": "0.00",
    "SlbmMtom": "0.00",
    "CdsSpreadBenefit": "0.00",
    "ComExpsrMrgnNrmlPrsnt": "0.00",
    "FoExpMrgnNrmlPrsnt": "0.00",
    "BrokeragePrsnt": "0.00",
    "CashUnRlsMtomPrsnt": "0.00",
    "AddPreExpMrgnNrmlPrsnt": "0.00",
    "CurSpanMrgnMisPrsnt": "0.00",
    "ComUnRlsMtomPrsnt": "0.00",
    "CashRlsMtomPrsnt": "0.00",
    "NotionalCash": "0.00",
    "CurExpMrgnMisPrsnt": "0.00",
    "MarginUsed": "0.00",
    "LendingFee": "0.00",
    "Net": "0.00",
    "TenderMrgnMisPrsnt": "0.00",
    "FoExpMrgnMisPrsnt": "0.00",
    "TenderMrgnNrmlPrsnt": "0.00",
    "MarginScripBasketPrsnt": "0.00",
    "ComSpanMrgnMisPrsnt": "0.00"
}
HEADERS
accept
application/json

Sid
b3ebb6af-7205-43e9-8513-189472393cab

Auth
eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.YEnhDlz1PI_LWSaq8twJDuGF02sotp88GQgcpmtUymdQBB2vSrqGAZ1XKA9h1OK1WIfx7OUcF8JoKJKNuSFqxA3WjSzpKaoZ8LKGfogJnPjJcKPcpf-VlDOvSOGI4H2bSW0uDGMNdWAHWmj6U1wG10ibfE__-LlV2alTvtFD4T1x5AhQwAshAmbbh90wltjPi69AI0gf_P04IhGBciqTIVb886tha-10e1VtXKwCrhrafZAeUep-t-VlrhDEkJLEStZShUY0bXz5vA_V8vDPWMLf3Ag8KLND75Jw9g8_4fDqoJSDk7LRj9AX0DvOrnc9juEOTfkDCioOaHQnmgjRrw

neo-fin-key
neotradeapi

Content-Type
application/x-www-form-urlencoded

Authorization
Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

PARAMS
sId
server1

Body
urlencoded
jData
{"seg":"CASH","exch":"NSE","prod":"ALL"}

Example Request
Limits
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Orders/2.0/quick/user/limits",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Sid": "b3ebb6af-7205-43e9-8513-189472393cab",
    "Auth": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.YEnhDlz1PI_LWSaq8twJDuGF02sotp88GQgcpmtUymdQBB2vSrqGAZ1XKA9h1OK1WIfx7OUcF8JoKJKNuSFqxA3WjSzpKaoZ8LKGfogJnPjJcKPcpf-VlDOvSOGI4H2bSW0uDGMNdWAHWmj6U1wG10ibfE__-LlV2alTvtFD4T1x5AhQwAshAmbbh90wltjPi69AI0gf_P04IhGBciqTIVb886tha-10e1VtXKwCrhrafZAeUep-t-VlrhDEkJLEStZShUY0bXz5vA_V8vDPWMLf3Ag8KLND75Jw9g8_4fDqoJSDk7LRj9AX0DvOrnc9juEOTfkDCioOaHQnmgjRrw",
    "neo-fin-key": "f784e198-bda7-439e-a1a6-177f432460b9",
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": "Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": {
    "jData": "{\"seg\":\"CASH\",\"exch\":\"NSE\",\"prod\":\"ALL\"}"
  }
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (13)
View More
json
{
  "AddPreExpMrgnMisPrsnt": "0.00",
  "CurExpMrgnNrmlPrsnt": "0.00",
  "CurPremiumNrmlPrsnt": "0.00",
  "RmsPayOutAmt": "0.00",
  "AdhocMargin": "0.00",
  "MrgnScrpBsktCncPrsnt": "0.00",
  "FoPremiumMisPrsnt": "0.00",
  "MrgnVarNrmlPrsnt": "0.00",
  "AmountUtilizedPrsnt": "0.00",
  "SplMrgnMisPrsnt": "0.00",
  "CurSpanMrgnNrmlPrsnt": "0.00",
  "AuxRmsCollateral": "0.00",
  "FoUnRlsMtomPrsnt": "0.00",
  "AuxNotionalCash": "0.00",
  "AuxAdhocMargin": "0.00",
  "DaneLimit": "0.00",
  "RmsPayInAmt": "0.00",
  "CurPremiumMisPrsnt": "0.00",
  "MarginWarningPrcntPrsnt": "0.00",
  "MrgnVarMisPrsnt": "0.00",
  "CurRlsMtomPrsnt": "0.00",
  "ExposureMarginPrsnt": "0.00",
  "NfospreadBenefit": "0.00",
  "stCode": 200,
  "CollateralValue": "0.00",
  "AddMrgnNrmlPrsnt": "0.00",
  "RmsCollateralMult": "0.00",
  "CncMrgnVarPrsnt": "0.00",
  "BoardLotLimit": "0",
  "ComRlsMtomPrsnt": "0.00",
  "MarginVarPrsnt": "0.00",
  "DeliveryMarginPresent": "0.00",
  "RmsCollateral": "0.00",
  "ComExpsrMrgnMisPrsnt": "0.00",
  "CollateralValueMult": "0.00",
  "FoSpanrgnMisPrsnt": "0.00",
  "Category": "CLIENT",
  "CurUnRlsMtomPrsnt": "0.00",
  "SpanMarginPrsnt": "0.00",
  "CncSellcrdPresent": "0.00",
  "Collateral": "0.00",
  "MrgnScrpBsktNrmlPrsnt": "0.00",
  "UnrealizedMtomPrsnt": "0.00",
  "MrgnScrpBsktMisPrsnt": "0.00",
  "RealizedMtomPrsnt": "0.00",
  "MtomWarningPrcntPrsnt": "0.00",
  "ComSpanMrgnNrmlPrsnt": "0.00",
  "DeliveryMrgnNrmlPrsnt": "0.00",
  "AdhocLimitMult": "0.00",
  "FoPremiumNrmlPrsnt": "0.00",
  "SpecialMarginPrsnt": "0.00",
  "SplMrgnNrmlPrsnt": "0.00",
  "MtomSquareOffWarningPrcntPrsnt": "0.00",
  "MarginUsedPrsnt": "0.00",
  "FoRlsMtomPrsnt": "0.00",
  "stat": "Ok",
  "NationalCashMult": "0.00",
  "FoSpanrgnNrmlPrsnt": "0.00",
  "AmtUntilizedPrsnt": "0.00",
  "PremiumPrsnt": "0.00",
  "AddMrgnMisPrsnt": "0.00",
  "DeliveryMrgnMisPrsnt": "0.00",
  "CdsSpreadBenefit": "0.00",
  "ComExpsrMrgnNrmlPrsnt": "0.00",
  "FoExpMrgnNrmlPrsnt": "0.00",
  "BrokeragePrsnt": "0.00",
  "CashUnRlsMtomPrsnt": "0.00",
  "AddPreExpMrgnNrmlPrsnt": "0.00",
  "CurSpanMrgnMisPrsnt": "0.00",
  "ComUnRlsMtomPrsnt": "0.00",
  "CashRlsMtomPrsnt": "0.00",
  "NotionalCash": "0.00",
  "CurExpMrgnMisPrsnt": "0.00",
  "MarginUsed": "0.00",
  "Net": "0.00",
  "TenderMrgnMisPrsnt": "0.00",
  "FoExpMrgnMisPrsnt": "0.00",
  "TenderMrgnNrmlPrsnt": "0.00",
  "MarginScripBasketPrsnt": "0.00",
  "ComSpanMrgnMisPrsnt": "0.00"
}
Margin
POST
Margin
https://gw-napi.kotaksecurities.com/Orders/2.0/quick/user/check-margin?sId=server1
To get in detail understanding of Check Margin Click Here

Parameters to be passed to Check Margin :

Authorization - Access Token (Format to pass access token is (Bearer {Access Token}))

Sid - session Id (Obtained in response body of Login API)

Auth - Session Token (Obtained in response body of Login API)

Neo-fin-key - neotradeapi (pass this value as default value)

sId - hsServerId (Obtained in response body of Login API, This can be either blank, so keep this parameter as optional and you can pass blank value to this to avoid any invalid request body error)

Request Body :

View More
Field	Type	Description
brkName	String	Broker name
brnchId	String	Branch id of user
exSeg	String	Exchange segment
prc	String	Price (should be in Rupees, for MKT send 0)
prcTp	String	Order type (from DefaultLogin/NormalLogin rest api)
prod	String	Product Code
qty	String	Quantity
slAbsOrTks (optional)	String	Stop loss type (Absolute or Ticks) - only for BO
slVal (optional)	String	Stop Loss Value - only for BO
sqrOffAbsOrTks (optional)	String	Square Off type (Absolute or Ticks) - only for BO
sqrOffVal (optional)	String	Square off value - only for BO
tok	String	Token number
trailSL (optional)	String	Trailing Stop Loss optional (Y, if required or else N) - only for BO
trgPrc (optional)	String	Trigger price(Only for CO, in Rupees)
trnsTp	String	Transaction type
tSLTks. (optional)	String	Trailing SL value (Y, if required or else N) - only for BO
View More
json
jData:{
"actId":"VEDANTH",
"brkName":"TELIGENZ",
"brnchId":"KALYANNAGAR",
"exSeg":"nse_cm",
"prc":"12500",
"prcTp":"L",
"prod":"CNC",
"qty":"1",
"tok":"11536",
"trnsTp":"B"
}
Success Response Details

Field	Type	Description
avlCash	String	Total cash available
avlMrgn	String	Available margin
insufFund	String	Insufficient fund
mrgnUsd	String	Margin used
ordMrgn	String	Order margin
reqdMrgn	String	Required margin
rmsVldtd	String	RMS validated
stat	String	Status
totMrgnUsd	String	Total margin used
Success Sample Response

View More
json
{
    "avlCash": "1000.00",
    "avlMrgn": "1000.00",
    "insufFund": "150.00",
    "mrgnUsd": "0.00",
    "ordMrgn": "1150.00",
    "reqdMrgn": "1150.00",
    "rmsVldtd": 78,
    "stCode": 200,
    "stat": "Ok",
    "totMrgnUsd": "1150.00"
}
HEADERS
accept
application/json

Sid
b3ebb6af-7205-43e9-8513-189472393cab

Auth
eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.YEnhDlz1PI_LWSaq8twJDuGF02sotp88GQgcpmtUymdQBB2vSrqGAZ1XKA9h1OK1WIfx7OUcF8JoKJKNuSFqxA3WjSzpKaoZ8LKGfogJnPjJcKPcpf-VlDOvSOGI4H2bSW0uDGMNdWAHWmj6U1wG10ibfE__-LlV2alTvtFD4T1x5AhQwAshAmbbh90wltjPi69AI0gf_P04IhGBciqTIVb886tha-10e1VtXKwCrhrafZAeUep-t-VlrhDEkJLEStZShUY0bXz5vA_V8vDPWMLf3Ag8KLND75Jw9g8_4fDqoJSDk7LRj9AX0DvOrnc9juEOTfkDCioOaHQnmgjRrw

neo-fin-key
neotradeapi

Content-Type
application/x-www-form-urlencoded

Authorization
Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

PARAMS
sId
server1

Body
urlencoded
jData
{"brkName":"KOTAK", "brnchId":"ONLINE", "exSeg":"nse_cm", "prc":"12500", "prcTp":"L", "prod":"CNC", "qty":"1", "tok":"11536", "trnsTp":"B"}

Example Request
Margin
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Orders/2.0/quick/user/check-margin",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Sid": "8a43c2a9-004f-4186-9d9e-824c07014f60",
    "Auth": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": {
    "jData": "{\"brkName\":\"KOTAK\", \"brnchId\":\"ONLINE\", \"exSeg\":\"nse_cm\", \"prc\":\"12500\", \"prcTp\":\"L\", \"prod\":\"CNC\", \"qty\":\"1\", \"tok\":\"11536\", \"trnsTp\":\"B\"}"
  }
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (21)
View More
json
{
  "avlCash": "-17.920000",
  "totMrgnUsd": "12500.000000",
  "mrgnUsd": "0.000000",
  "ordMrgn": "12500.000000",
  "rmsVldtd": "NOT_OK",
  "reqdMrgn": "12500.000000",
  "avlMrgn": "-17.920000",
  "insufFund": "12517.920000",
  "stat": "Ok",
  "stCode": 200
}
Quotes
URL : https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/{neoSymbols}/{quoteType}

Example parameters to be passed in neoSymbols : A comma-separated list of neosymbols to fetch quotes

neoSymbols (string) - nse_cm|2885,bse_cm|532174,nse_fo|65500,bse_cm|540376

Example of quoteType -

quoteType (string) - Type of data to retrieve. Options include ltp, depth, oi, ohlc, circuit_limits, scrip_details, 52W, or all. Defaults to all.

GET
Quotes
https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/nse_cm%7C2885%2Cbse_cm%7C532174%2Cnse_fo%7C65500%2Cbse_cm%7C540376/all
Generated from cURL: curl -X 'GET'
'https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/nse_cm%7C2885%2Cbse_cm%7C532174%2Cnse_fo%7C65500%2Cbse_cm%7C540376/all'
-H 'accept: application/json'
-H 'Authorization: Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'

HEADERS
accept
application/json

Authorization
Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

Example Request
Get Quotes
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/nse_cm%7C2885%2Cbse_cm%7C532174%2Cnse_fo%7C65500%2Cbse_cm%7C540376/all",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Authorization": "Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (30)
View More
json
[
  {
    "exchange_token": "2885",
    "display_symbol": "RELIANCE-EQ",
    "exchange": "nse_cm",
    "lstup_time": "**********",
    "ltp": "1244.1500",
    "last_traded_quantity": "22",
    "total_buy": "441407",
    "total_sell": "330754",
    "last_volume": "625712",
    "avg_cost": "1239.8500",
    "open_int": "0",
    "change": "-7.0000",
    "per_change": "-0.5600",
    "low_price_range": "1126.0500",
    "high_price_range": "1376.2500",
    "year_high": "3217.6",
    "year_low": "1156",
    "ohlc": {
      "open": "1233.0500",
      "high": "1245.0000",
      "low": "1233.0500",
      "close": "1251.1500"
    },
    "depth": {
      "buy": [
        {
          "price": "1244.0500",
          "quantity": "96",
          "orders": "1"
        },
        {
          "price": "1244.0000",
          "quantity": "1440",
          "orders": "28"
        },
        {
          "price": "1243.9500",
          "quantity": "74",
          "orders": "5"
        },
        {
          "price": "1243.9000",
          "quantity": "604",
          "orders": "2"
        },
        {
          "price": "1243.8500",
          "quantity": "4",
          "orders": "1"
        }
      ],
      "sell": [
        {
          "price": "1244.1500",
          "quantity": "596",
          "orders": "3"
        },
        {
          "price": "1244.2000",
          "quantity": "205",
          "orders": "2"
        },
        {
          "price": "1244.2500",
          "quantity": "159",
          "orders": "1"
        },
        {
          "price": "1244.3000",
          "quantity": "530",
          "orders": "1"
        },
        {
          "price": "1244.4000",
          "quantity": "95",
          "orders": "2"
        }
      ]
    }
  },
  {
    "exchange_token": "532174",
    "display_symbol": "ICICIBANK-A",
    "exchange": "bse_cm",
    "lstup_time": "**********",
    "ltp": "1325.2500",
    "last_traded_quantity": "1",
    "total_buy": "78511",
    "total_sell": "649789",
    "last_volume": "3432",
    "avg_cost": "1321.7900",
    "open_int": "0",
    "change": "-6.2000",
    "per_change": "-0.4700",
    "low_price_range": "1198.3500",
    "high_price_range": "1464.5500",
    "year_high": "1372.5",
    "year_low": "1048.35",
    "ohlc": {
      "open": "1306.5500",
      "high": "1328.0000",
      "low": "1306.5500",
      "close": "1331.4500"
    },
    "depth": {
      "buy": [
        {
          "price": "1324.8500",
          "quantity": "51",
          "orders": "3"
        },
        {
          "price": "1324.8000",
          "quantity": "156",
          "orders": "3"
        },
        {
          "price": "1324.7500",
          "quantity": "47",
          "orders": "3"
        },
        {
          "price": "1324.7000",
          "quantity": "162",
          "orders": "6"
        },
        {
          "price": "1324.6500",
          "quantity": "87",
          "orders": "4"
        }
      ],
      "sell": [
        {
          "price": "1325.8500",
          "quantity": "7",
          "orders": "1"
        },
        {
          "price": "1325.9000",
          "quantity": "33",
          "orders": "1"
        },
        {
          "price": "1326.0000",
          "quantity": "116",
          "orders": "3"
        },
        {
          "price": "1326.0500",
          "quantity": "15",
          "orders": "1"
        },
        {
          "price": "1326.1000",
          "quantity": "30",
          "orders": "2"
        }
      ]
    }
  },
  {
    "exchange_token": "65500",
    "display_symbol": "ABCAPITAL25JUN250PE",
    "exchange": "nse_fo",
    "lstup_time": "Mon Jan 01 1900 05:21:10 GMT+0521 (India Standard Time)",
    "ltp": "0",
    "last_traded_quantity": "0",
    "total_buy": "0",
    "total_sell": "0",
    "last_volume": "0",
    "avg_cost": "",
    "open_int": "0",
    "change": "0",
    "per_change": "0",
    "low_price_range": "37.5",
    "high_price_range": "77.5",
    "year_high": "0",
    "year_low": "0",
    "ohlc": {
      "open": "0",
      "high": "0",
      "low": "0",
      "close": "0"
    },
    "depth": {
      "buy": [
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        }
      ],
      "sell": [
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        }
      ]
    }
  },
  {
    "exchange_token": "540376",
    "display_symbol": "DMART-A",
    "exchange": "bse_cm",
    "lstup_time": "**********",
    "ltp": "4130.0000",
    "last_traded_quantity": "2",
    "total_buy": "14845",
    "total_sell": "12822",
    "last_volume": "1317",
    "avg_cost": "4120.5200",
    "open_int": "0",
    "change": "13.7000",
    "per_change": "0.3300",
    "low_price_range": "3704.7000",
    "high_price_range": "4527.9000",
    "year_high": "5484",
    "year_low": "3337.1",
    "ohlc": {
      "open": "4116.3500",
      "high": "4136.9000",
      "low": "4079.5000",
      "close": "4116.3000"
    },
    "depth": {
      "buy": [
        {
          "price": "4128.7500",
          "quantity": "15",
          "orders": "4"
        },
        {
          "price": "4128.7000",
          "quantity": "9",
          "orders": "6"
        },
        {
          "price": "4128.6500",
          "quantity": "2",
          "orders": "1"
        },
        {
          "price": "4128.6000",
          "quantity": "2",
          "orders": "1"
        },
        {
          "price": "4128.5500",
          "quantity": "2",
          "orders": "1"
        }
      ],
      "sell": [
        {
          "price": "4135.3500",
          "quantity": "1",
          "orders": "1"
        },
        {
          "price": "4136.0000",
          "quantity": "1",
          "orders": "1"
        },
        {
          "price": "4136.3000",
          "quantity": "2",
          "orders": "1"
        },
        {
          "price": "4136.4500",
          "quantity": "4",
          "orders": "1"
        },
        {
          "price": "4136.5000",
          "quantity": "4",
          "orders": "2"
        }
      ]
    }
  }
]
Websocket
Kotak Provides two types of websocket

Market Feed - wss://mlhsm.kotaksecurities.com

Real Time Order Updates - refer demo.js file

Documentation for connecting to these websocket is in the form of SDKs.

Refer Below Link for Websocket :

JS SDK - https://drive.google.com/file/d/1EzypWrmB-Uw7PlEw-XSOfmmp2y1mxLfN/view

Python SDK - https://github.com/Priyanka15802/Neo_sdk_v2

Support Email ID in case of queries - <EMAIL>