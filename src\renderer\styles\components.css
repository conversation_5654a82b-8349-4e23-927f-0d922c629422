/* Component-specific styles */

/* Notification System */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
}

.notification {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    transform: translateX(100%);
    transition: transform var(--transition-normal);
    max-width: 100%;
    word-wrap: break-word;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.info {
    border-left: 4px solid var(--info-color);
}

.notification-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    margin-top: 2px;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.notification-message {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.notification-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Toast Notifications */
.toast {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    box-shadow: var(--shadow-md);
    font-size: var(--font-size-sm);
    opacity: 0;
    transform: translateY(-20px);
    transition: all var(--transition-normal);
}

.toast.show {
    opacity: 1;
    transform: translateY(0);
}

.toast.toast-success {
    border-left: 4px solid var(--success-color);
    color: var(--success-color);
}

.toast.toast-error {
    border-left: 4px solid var(--danger-color);
    color: var(--danger-color);
}

.toast.toast-warning {
    border-left: 4px solid var(--warning-color);
    color: var(--warning-color);
}

.toast.toast-info {
    border-left: 4px solid var(--info-color);
    color: var(--info-color);
}

/* Loading Components */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

.loading-text {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal);
}

.progress-fill.success {
    background: var(--success-color);
}

.progress-fill.warning {
    background: var(--warning-color);
}

.progress-fill.danger {
    background: var(--danger-color);
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge.badge-primary {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.badge.badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.badge.badge-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.badge.badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.badge.badge-info {
    background: rgba(6, 182, 212, 0.1);
    color: var(--info-color);
}

.badge.badge-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

/* Tooltips */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background: var(--bg-dark);
    color: var(--text-inverse);
    text-align: center;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    font-size: var(--font-size-xs);
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--bg-dark) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Dropdown */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-content {
    display: none;
    position: absolute;
    background: var(--bg-card);
    min-width: 200px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    z-index: 1000;
    top: 100%;
    left: 0;
    margin-top: var(--spacing-xs);
}

.dropdown.show .dropdown-content {
    display: block;
}

.dropdown-item {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    text-decoration: none;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    transition: background-color var(--transition-fast);
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background: var(--bg-tertiary);
}

.dropdown-item.disabled {
    color: var(--text-muted);
    cursor: not-allowed;
}

.dropdown-item.disabled:hover {
    background: none;
}

/* Tabs */
.tabs {
    border-bottom: 1px solid var(--border-color);
}

.tab-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.tab-item {
    margin-right: var(--spacing-md);
}

.tab-link {
    display: block;
    padding: var(--spacing-md) var(--spacing-lg);
    text-decoration: none;
    color: var(--text-secondary);
    border-bottom: 2px solid transparent;
    transition: all var(--transition-fast);
    font-weight: 500;
}

.tab-link:hover {
    color: var(--text-primary);
    border-bottom-color: var(--border-hover);
}

.tab-link.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    padding: var(--spacing-lg) 0;
}

/* Accordion */
.accordion {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.accordion-item {
    border-bottom: 1px solid var(--border-color);
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-header {
    background: var(--bg-tertiary);
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color var(--transition-fast);
}

.accordion-header:hover {
    background: var(--bg-secondary);
}

.accordion-title {
    font-weight: 600;
    color: var(--text-primary);
}

.accordion-icon {
    transition: transform var(--transition-fast);
}

.accordion-item.active .accordion-icon {
    transform: rotate(180deg);
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-normal);
}

.accordion-item.active .accordion-content {
    max-height: 500px;
}

.accordion-body {
    padding: var(--spacing-lg);
}

/* Data Tables */
.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
}

.data-table th,
.data-table td {
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table tr:hover {
    background: var(--bg-tertiary);
}

.data-table .sortable {
    cursor: pointer;
    user-select: none;
}

.data-table .sortable:hover {
    background: var(--bg-secondary);
}

.data-table .sort-icon {
    margin-left: var(--spacing-xs);
    opacity: 0.5;
}

.data-table .sortable.asc .sort-icon,
.data-table .sortable.desc .sort-icon {
    opacity: 1;
}

/* Pagination */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

.pagination-item {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-card);
    color: var(--text-primary);
    text-decoration: none;
    transition: all var(--transition-fast);
    min-width: 40px;
    text-align: center;
}

.pagination-item:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-hover);
}

.pagination-item.active {
    background: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
}

.pagination-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-item.disabled:hover {
    background: var(--bg-card);
    border-color: var(--border-color);
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-dot.online {
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.status-dot.offline {
    background: var(--danger-color);
}

.status-dot.pending {
    background: var(--warning-color);
    animation: pulse 2s infinite;
}

.status-dot.neutral {
    background: var(--secondary-color);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes slideOutRight {
    from { transform: translateX(0); }
    to { transform: translateX(100%); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Utility Classes */
.fade-in {
    animation: fadeIn var(--transition-normal);
}

.fade-out {
    animation: fadeOut var(--transition-normal);
}

.slide-in-right {
    animation: slideInRight var(--transition-normal);
}

.slide-out-right {
    animation: slideOutRight var(--transition-normal);
}

.bounce {
    animation: bounce 1s;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-uppercase {
    text-transform: uppercase;
}

.text-lowercase {
    text-transform: lowercase;
}

.text-capitalize {
    text-transform: capitalize;
}

.font-weight-normal {
    font-weight: 400;
}

.font-weight-medium {
    font-weight: 500;
}

.font-weight-semibold {
    font-weight: 600;
}

.font-weight-bold {
    font-weight: 700;
}

.d-none {
    display: none !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: flex !important;
}

.d-inline-flex {
    display: inline-flex !important;
}

.justify-content-start {
    justify-content: flex-start !important;
}

.justify-content-center {
    justify-content: center !important;
}

.justify-content-end {
    justify-content: flex-end !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.align-items-start {
    align-items: flex-start !important;
}

.align-items-center {
    align-items: center !important;
}

.align-items-end {
    align-items: flex-end !important;
}

.flex-wrap {
    flex-wrap: wrap !important;
}

.flex-nowrap {
    flex-wrap: nowrap !important;
}

.flex-grow-1 {
    flex-grow: 1 !important;
}

.flex-shrink-0 {
    flex-shrink: 0 !important;
}

/* Credential Help Section */
.credential-help {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.credential-help h5 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.credential-help ol {
    margin: var(--spacing-sm) 0;
    padding-left: var(--spacing-lg);
}

.credential-help li {
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

.credential-help p {
    margin-top: var(--spacing-sm);
    color: var(--text-muted);
    font-size: var(--font-size-xs);
    font-style: italic;
}

/* Authentication Modal */
.auth-step {
    padding: var(--spacing-lg);
}

.auth-step.hidden {
    display: none;
}

.auth-step h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.auth-step p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.auth-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    margin-top: var(--spacing-lg);
}

.success-message {
    text-align: center;
    padding: var(--spacing-lg);
}

.success-message i {
    font-size: 3rem;
    color: var(--success-color);
    margin-bottom: var(--spacing-md);
}

.success-message h4 {
    color: var(--success-color);
    margin-bottom: var(--spacing-sm);
}

.auth-info {
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin: var(--spacing-lg) 0;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.info-item .label {
    font-weight: 500;
    color: var(--text-secondary);
}

.error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid var(--danger-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    margin: var(--spacing-md) 0;
    color: var(--danger-color);
    font-size: var(--font-size-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.form-help {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
    display: block;
}

/* Trading Panel Styles */
.trading-panel {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-sm);
}

/* Option Chain Styles */
.option-chain-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: var(--bg-secondary);
    border-radius: 8px;
}

.symbol-info {
    display: flex;
    gap: 20px;
    align-items: center;
}

.symbol-selector, .expiry-selector {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.symbol-selector label, .expiry-selector label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.symbol-selector select, .expiry-selector select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 14px;
}

.current-price {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.price-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.price-value {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.price-change {
    font-size: 14px;
    font-weight: 500;
}

.price-change.positive {
    color: var(--success-color);
}

.price-change.negative {
    color: var(--danger-color);
}

/* Option Chain Table */
.option-chain-table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.option-chain-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

.option-chain-table th {
    background: var(--bg-secondary);
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    border-bottom: 2px solid var(--border-color);
}

.call-header {
    background: var(--success-color);
    color: white;
}

.put-header {
    background: var(--danger-color);
    color: white;
}

.strike-header {
    background: var(--primary-color);
    color: white;
    font-weight: 700;
}

.option-chain-table td {
    padding: 8px 6px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
}

.option-chain-row:hover {
    background: var(--bg-secondary);
}

.option-chain-row.selected {
    background: var(--primary-color-light);
    border: 2px solid var(--primary-color);
}

.strike-cell {
    font-weight: 600;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.ltp-cell {
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.call-ltp {
    color: var(--success-color);
}

.put-ltp {
    color: var(--danger-color);
}

/* Price Animation */
.price-up {
    background-color: var(--success-color) !important;
    color: white !important;
    animation: priceFlash 1s ease-out;
}

.price-down {
    background-color: var(--danger-color) !important;
    color: white !important;
    animation: priceFlash 1s ease-out;
}

@keyframes priceFlash {
    0% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.change-cell.positive {
    color: var(--success-color);
}

.change-cell.negative {
    color: var(--danger-color);
}

.action-cell button {
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-weight: 500;
}

.btn-buy {
    background: var(--success-color);
    color: white;
}

.btn-buy:hover {
    background: var(--success-color-dark);
}

.btn-sell {
    background: var(--danger-color);
    color: white;
}

.btn-sell:hover {
    background: var(--danger-color-dark);
}

/* Market Data Display */
.market-data-display {
    padding: 20px;
    background: var(--card-bg);
    border-radius: 8px;
    margin-top: 20px;
}

.market-data-display h4 {
    color: var(--text-primary);
    margin-bottom: 15px;
    text-align: center;
}

.strikes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    max-width: 800px;
    margin: 0 auto;
}

.strike-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px;
    background: var(--bg-secondary);
    border-radius: 6px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.strike-item:hover {
    background: var(--primary-color-light);
    transform: translateY(-2px);
}

.strike-item.current-strike {
    border-color: var(--primary-color);
    background: var(--primary-color-light);
    font-weight: bold;
}

.strike-price {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.strike-type {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    color: white;
    font-weight: 500;
}

.strike-item .strike-type {
    background: var(--text-secondary);
}

.current-strike .strike-type {
    background: var(--primary-color);
}

.loading-row {
    text-align: center;
    padding: 40px;
    color: var(--text-secondary);
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    text-align: center;
    color: var(--text-secondary);
}

.error-icon {
    font-size: 48px;
    color: var(--danger-color);
    margin-bottom: 20px;
}

.error-content h4 {
    color: var(--text-primary);
    margin-bottom: 10px;
}

.error-content p {
    margin-bottom: 20px;
    color: var(--text-secondary);
}

.loading-row {
    text-align: center;
    padding: 40px;
    color: var(--text-secondary);
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
