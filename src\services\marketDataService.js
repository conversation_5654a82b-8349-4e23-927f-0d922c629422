const axios = require('axios');

/**
 * Market Data Service for Kotak Neo Trade API
 * Handles market data API calls and quote management
 */
class MarketDataService {
    constructor(authService) {
        this.authService = authService;
        this.baseURL = 'https://gw-napi.kotaksecurities.com';
        this.cache = new Map();
        this.cacheTimeout = 5000; // 5 seconds cache
    }

    /**
     * Get quote for a single symbol
     */
    async getQuote(symbol, exchange = 'NSE') {
        try {
            const headers = this.authService.getAuthHeaders();
            if (!headers) {
                throw new Error('Not authenticated');
            }

            const cacheKey = `${exchange}:${symbol}`;
            const cached = this.cache.get(cacheKey);
            
            if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
                return { success: true, data: cached.data };
            }

            const response = await axios.get(`${this.baseURL}/quotes`, {
                headers,
                params: {
                    instrument_token: symbol,
                    exchange: exchange
                },
                timeout: 10000
            });

            if (response.data && response.data.stat === 'Ok') {
                const quoteData = this.formatQuoteData(response.data.data);
                
                // Cache the result
                this.cache.set(cacheKey, {
                    data: quoteData,
                    timestamp: Date.now()
                });

                return { success: true, data: quoteData };
            } else {
                return {
                    success: false,
                    message: response.data?.emsg || 'Failed to get quote'
                };
            }
        } catch (error) {
            console.error('Failed to get quote:', error);
            return {
                success: false,
                message: error.message || 'Failed to get quote'
            };
        }
    }

    /**
     * Get quotes for multiple symbols
     */
    async getQuotes(symbols, exchange = 'NSE') {
        try {
            const headers = this.authService.getAuthHeaders();
            if (!headers) {
                throw new Error('Not authenticated');
            }

            if (!Array.isArray(symbols)) {
                symbols = [symbols];
            }

            const response = await axios.post(`${this.baseURL}/quotes/multiple`, {
                instruments: symbols.map(symbol => ({
                    instrument_token: symbol,
                    exchange: exchange
                }))
            }, {
                headers,
                timeout: 15000
            });

            if (response.data && response.data.stat === 'Ok') {
                const quotesData = response.data.data.map(quote => this.formatQuoteData(quote));
                return { success: true, data: quotesData };
            } else {
                return {
                    success: false,
                    message: response.data?.emsg || 'Failed to get quotes'
                };
            }
        } catch (error) {
            console.error('Failed to get quotes:', error);
            return {
                success: false,
                message: error.message || 'Failed to get quotes'
            };
        }
    }

    /**
     * Get market depth for a symbol
     */
    async getMarketDepth(symbol, exchange = 'NSE') {
        try {
            const headers = this.authService.getAuthHeaders();
            if (!headers) {
                throw new Error('Not authenticated');
            }

            const response = await axios.get(`${this.baseURL}/market-depth`, {
                headers,
                params: {
                    instrument_token: symbol,
                    exchange: exchange
                },
                timeout: 10000
            });

            if (response.data && response.data.stat === 'Ok') {
                const depthData = this.formatDepthData(response.data.data);
                return { success: true, data: depthData };
            } else {
                return {
                    success: false,
                    message: response.data?.emsg || 'Failed to get market depth'
                };
            }
        } catch (error) {
            console.error('Failed to get market depth:', error);
            return {
                success: false,
                message: error.message || 'Failed to get market depth'
            };
        }
    }

    /**
     * Get option chain for Nifty or Bank Nifty
     */
    async getOptionChain(symbol, expiry = null) {
        try {
            const headers = this.authService.getAuthHeaders();
            if (!headers) {
                throw new Error('Not authenticated');
            }

            // Get current price first to determine ATM strike
            const currentQuote = await this.getQuote(symbol);
            if (!currentQuote.success) {
                throw new Error('Failed to get current price');
            }

            const currentPrice = currentQuote.data.ltp;
            const atmStrike = this.getATMStrike(currentPrice, symbol);

            // Calculate strike range (5 above and 5 below ATM)
            const strikeInterval = symbol === 'NIFTY' ? 50 : 100;
            const strikes = [];
            
            for (let i = -5; i <= 5; i++) {
                strikes.push(atmStrike + (i * strikeInterval));
            }

            const response = await axios.get(`${this.baseURL}/option-chain`, {
                headers,
                params: {
                    symbol: symbol,
                    expiry: expiry,
                    strikes: strikes.join(',')
                },
                timeout: 15000
            });

            if (response.data && response.data.stat === 'Ok') {
                const optionChain = this.formatOptionChainData(response.data.data, currentPrice, atmStrike);
                return { success: true, data: optionChain };
            } else {
                return {
                    success: false,
                    message: response.data?.emsg || 'Failed to get option chain'
                };
            }
        } catch (error) {
            console.error('Failed to get option chain:', error);
            return {
                success: false,
                message: error.message || 'Failed to get option chain'
            };
        }
    }

    /**
     * Get historical data
     */
    async getHistoricalData(symbol, interval = '1minute', fromDate, toDate) {
        try {
            const headers = this.authService.getAuthHeaders();
            if (!headers) {
                throw new Error('Not authenticated');
            }

            const response = await axios.get(`${this.baseURL}/historical`, {
                headers,
                params: {
                    instrument_token: symbol,
                    interval: interval,
                    from: fromDate,
                    to: toDate
                },
                timeout: 30000
            });

            if (response.data && response.data.stat === 'Ok') {
                return { success: true, data: response.data.data };
            } else {
                return {
                    success: false,
                    message: response.data?.emsg || 'Failed to get historical data'
                };
            }
        } catch (error) {
            console.error('Failed to get historical data:', error);
            return {
                success: false,
                message: error.message || 'Failed to get historical data'
            };
        }
    }

    /**
     * Search for instruments
     */
    async searchInstruments(query, exchange = 'NSE') {
        try {
            const headers = this.authService.getAuthHeaders();
            if (!headers) {
                throw new Error('Not authenticated');
            }

            const response = await axios.get(`${this.baseURL}/search`, {
                headers,
                params: {
                    q: query,
                    exchange: exchange
                },
                timeout: 10000
            });

            if (response.data && response.data.stat === 'Ok') {
                return { success: true, data: response.data.data };
            } else {
                return {
                    success: false,
                    message: response.data?.emsg || 'Failed to search instruments'
                };
            }
        } catch (error) {
            console.error('Failed to search instruments:', error);
            return {
                success: false,
                message: error.message || 'Failed to search instruments'
            };
        }
    }

    /**
     * Format quote data
     */
    formatQuoteData(rawData) {
        return {
            symbol: rawData.tk || rawData.instrument_token,
            tradingSymbol: rawData.ts || rawData.trading_symbol,
            ltp: parseFloat(rawData.lp || rawData.last_price || 0),
            change: parseFloat(rawData.c || rawData.net_change || 0),
            changePercent: parseFloat(rawData.pc || rawData.percentage_change || 0),
            volume: parseInt(rawData.v || rawData.volume || 0),
            open: parseFloat(rawData.o || rawData.ohlc?.open || 0),
            high: parseFloat(rawData.h || rawData.ohlc?.high || 0),
            low: parseFloat(rawData.l || rawData.ohlc?.low || 0),
            close: parseFloat(rawData.lc || rawData.ohlc?.close || 0),
            bid: parseFloat(rawData.bp1 || rawData.depth?.buy?.[0]?.price || 0),
            ask: parseFloat(rawData.sp1 || rawData.depth?.sell?.[0]?.price || 0),
            bidQty: parseInt(rawData.bq1 || rawData.depth?.buy?.[0]?.quantity || 0),
            askQty: parseInt(rawData.sq1 || rawData.depth?.sell?.[0]?.quantity || 0),
            timestamp: new Date()
        };
    }

    /**
     * Format market depth data
     */
    formatDepthData(rawData) {
        return {
            symbol: rawData.tk || rawData.instrument_token,
            bids: this.formatDepthLevels(rawData.depth?.buy || []),
            asks: this.formatDepthLevels(rawData.depth?.sell || []),
            timestamp: new Date()
        };
    }

    /**
     * Format depth levels
     */
    formatDepthLevels(levels) {
        return levels.map(level => ({
            price: parseFloat(level.price || 0),
            quantity: parseInt(level.quantity || 0),
            orders: parseInt(level.orders || 0)
        }));
    }

    /**
     * Format option chain data
     */
    formatOptionChainData(rawData, currentPrice, atmStrike) {
        const optionChain = {
            underlyingPrice: currentPrice,
            atmStrike: atmStrike,
            strikes: []
        };

        // Group options by strike price
        const strikeMap = new Map();
        
        rawData.forEach(option => {
            const strike = parseFloat(option.strike_price);
            if (!strikeMap.has(strike)) {
                strikeMap.set(strike, { strike, call: null, put: null });
            }
            
            const optionData = {
                symbol: option.trading_symbol,
                ltp: parseFloat(option.last_price || 0),
                change: parseFloat(option.net_change || 0),
                changePercent: parseFloat(option.percentage_change || 0),
                volume: parseInt(option.volume || 0),
                oi: parseInt(option.oi || 0),
                bid: parseFloat(option.bid || 0),
                ask: parseFloat(option.ask || 0),
                iv: parseFloat(option.iv || 0)
            };
            
            if (option.instrument_type === 'CE') {
                strikeMap.get(strike).call = optionData;
            } else if (option.instrument_type === 'PE') {
                strikeMap.get(strike).put = optionData;
            }
        });

        // Convert to array and sort by strike
        optionChain.strikes = Array.from(strikeMap.values())
            .sort((a, b) => a.strike - b.strike);

        return optionChain;
    }

    /**
     * Get ATM strike price
     */
    getATMStrike(currentPrice, symbol) {
        const strikeInterval = symbol === 'NIFTY' ? 50 : 100;
        return Math.round(currentPrice / strikeInterval) * strikeInterval;
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * Get cache statistics
     */
    getCacheStats() {
        return {
            size: this.cache.size,
            timeout: this.cacheTimeout
        };
    }
}

module.exports = MarketDataService;
