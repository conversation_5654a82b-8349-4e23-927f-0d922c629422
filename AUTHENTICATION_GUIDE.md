# 🔐 Kotak Neo Trade Authentication Guide

## ✅ **Fixed Issues & Updates**

### **Jest Configuration Fixed**
- ✅ Fixed `moduleNameMapping` → `moduleNameMapper`
- ✅ Removed missing watch plugins
- ✅ Added Babel configuration for ES6+ support

### **Authentication Flow Corrected**
- ✅ Implemented proper 3-step Kotak Neo API authentication
- ✅ Added UCC (Unique Client Code) field - **REQUIRED**
- ✅ Removed incorrect User ID/Password fields
- ✅ Added MPIN support for trading

## 🔑 **Correct Authentication Flow**

### **Step 1: Get Client Access Token**
```javascript
// Using Consumer Key + Consumer Secret
POST https://napi.kotaksecurities.com/oauth2/token
Authorization: Basic {Base64(consumerKey:consumerSecret)}
Body: grant_type=client_credentials
```

### **Step 2: Get View Token (TOTP Login)**
```javascript
POST https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/login
Headers:
  Authorization: Bearer {clientAccessToken}
  neo-fin-key: neotradeapi
Body:
  {
    "mobileNumber": "+************",
    "ucc": "YOJE0",
    "totp": "123456"
  }
```

### **Step 3: Get Trading Token (MPIN - Optional)**
```javascript
POST https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/validate
Headers:
  Authorization: Bearer {clientAccessToken}
  Auth: {viewToken}
  sid: {sessionId}
  neo-fin-key: neotradeapi
Body:
  {
    "mpin": "1234"
  }
```

## 📋 **Required Credentials**

### **From Kotak Securities:**
1. **Consumer Key** - API key from Kotak
2. **Consumer Secret** - API secret from Kotak
3. **UCC** - Unique Client Code (e.g., YOJE0)
4. **Mobile Number** - Registered 10-digit number
5. **TOTP** - 6-digit code from authenticator app
6. **MPIN** - 4-6 digit trading PIN (optional, for trading)

### **NOT Required:**
- ❌ User ID (removed)
- ❌ Password (removed)

## 🚀 **How to Run the Application**

### **1. Install Dependencies**
```cmd
npm install
```

### **2. Run Tests (Optional)**
```cmd
npm test
```

### **3. Start Application**
```cmd
npm start
```

### **4. Configure Credentials**
1. Click **⚙️ Settings**
2. Go to **API Credentials** tab
3. Fill in:
   - **UCC**: Your unique client code
   - **Mobile Number**: 10 digits (no +91)
   - **Consumer Key**: From Kotak API portal
   - **Consumer Secret**: From Kotak API portal
4. Click **Save Credentials**
5. Click **Test Connection**

### **5. Login**
1. Enter **6-digit TOTP** code
2. Click **Authenticate**
3. Optionally enter **MPIN** for trading

## 🔧 **Technical Implementation**

### **New Authentication Service**
- **File**: `src/services/kotakAuthService.js`
- **Features**: 
  - Proper 3-step authentication
  - Secure credential storage
  - Session management
  - Trading/View mode support

### **Updated Components**
- **Settings**: Added UCC field, removed User ID/Password
- **Auth Modal**: TOTP + MPIN authentication flow
- **Main Process**: Uses new KotakAuthService

### **API Headers Required**
```javascript
{
  "Authorization": "Bearer {clientAccessToken}",
  "Auth": "{viewToken or tradingToken}",
  "sid": "{sessionId}",
  "neo-fin-key": "neotradeapi",
  "Content-Type": "application/json"
}
```

## 📱 **TOTP Setup**

### **1. Download Authenticator App**
- Google Authenticator (recommended)
- Microsoft Authenticator
- Authy

### **2. Setup with Kotak**
1. Login to Kotak Neo Trade
2. Go to Settings → Security
3. Enable TOTP/2FA
4. Scan QR code with authenticator app
5. App will generate 6-digit codes every 30 seconds

## 🛠️ **Troubleshooting**

### **"Invalid UCC" Error**
- ✅ Check UCC format (usually 5-10 alphanumeric characters)
- ✅ Verify UCC with Kotak customer service
- ✅ Ensure UCC is active and not suspended

### **"Invalid TOTP" Error**
- ✅ Check device time synchronization
- ✅ Use fresh 6-digit code (not expired)
- ✅ Verify authenticator app is properly synced

### **"Invalid Consumer Key/Secret" Error**
- ✅ Verify API access is approved by Kotak
- ✅ Check Consumer Key and Secret are correct
- ✅ Ensure API credentials are active

### **"Mobile Number Invalid" Error**
- ✅ Use 10-digit format: `9876543210`
- ✅ Don't include +91 or country code
- ✅ Verify mobile number is registered with Kotak

### **PowerShell Execution Policy Error**
```cmd
# Run this in PowerShell as Administrator
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Or use Command Prompt instead of PowerShell
```

## 📞 **Getting Help**

### **Kotak Securities Support**
- **Phone**: 1860-266-0808
- **Email**: <EMAIL>
- **Website**: [neo.kotaksecurities.com](https://neo.kotaksecurities.com)

### **API Documentation**
- Available after API access approval
- Developer portal: Contact Kotak for access

## ✅ **Verification Checklist**

Before using the application:

- [ ] Node.js 16+ installed
- [ ] Dependencies installed (`npm install`)
- [ ] Kotak Neo Trade account active
- [ ] API access approved by Kotak
- [ ] Consumer Key and Secret obtained
- [ ] UCC (Unique Client Code) known
- [ ] Mobile number registered with Kotak
- [ ] TOTP authenticator app setup
- [ ] Application starts successfully
- [ ] Credentials configured and tested
- [ ] TOTP authentication works
- [ ] Market data flows (if authenticated)

## 🎉 **Ready to Trade!**

Once all steps are completed:
1. **Authenticate** with TOTP (View mode)
2. **Optionally** authenticate with MPIN (Trading mode)
3. **Configure** risk settings
4. **Start** with small test trades
5. **Monitor** the application closely

**Happy Trading! 📈**

---

**Disclaimer**: This is for educational purposes. Trading involves risk. Always test thoroughly before live trading.
