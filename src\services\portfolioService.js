const axios = require('axios');
const EventEmitter = require('events');

/**
 * Portfolio Service for Kotak Neo Trade API
 * Handles portfolio tracking, position monitoring, and P&L calculations
 */
class PortfolioService extends EventEmitter {
    constructor(authService) {
        super();
        this.authService = authService;
        this.baseURL = 'https://gw-napi.kotaksecurities.com';
        this.positions = new Map();
        this.holdings = new Map();
        this.dayTradingStats = {
            totalPnL: 0,
            realizedPnL: 0,
            unrealizedPnL: 0,
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            totalInvestment: 0,
            currentValue: 0
        };
    }

    /**
     * Get current positions
     */
    async getPositions() {
        try {
            const headers = this.authService.getAuthHeaders();
            if (!headers) {
                throw new Error('Not authenticated');
            }

            const response = await axios.get(`${this.baseURL}/positions`, {
                headers,
                timeout: 15000
            });

            if (response.data && response.data.stat === 'Ok') {
                const positions = response.data.data.map(pos => this.formatPositionData(pos));
                
                // Update local positions
                this.positions.clear();
                positions.forEach(position => {
                    this.positions.set(position.symbol, position);
                });

                // Calculate day trading stats
                this.calculateDayTradingStats();

                this.emit('positionsUpdated', positions);

                return {
                    success: true,
                    data: positions
                };
            } else {
                return {
                    success: false,
                    message: response.data?.emsg || 'Failed to get positions'
                };
            }
        } catch (error) {
            console.error('Failed to get positions:', error);
            return {
                success: false,
                message: error.message || 'Failed to get positions'
            };
        }
    }

    /**
     * Get holdings
     */
    async getHoldings() {
        try {
            const headers = this.authService.getAuthHeaders();
            if (!headers) {
                throw new Error('Not authenticated');
            }

            const response = await axios.get(`${this.baseURL}/holdings`, {
                headers,
                timeout: 15000
            });

            if (response.data && response.data.stat === 'Ok') {
                const holdings = response.data.data.map(holding => this.formatHoldingData(holding));
                
                // Update local holdings
                this.holdings.clear();
                holdings.forEach(holding => {
                    this.holdings.set(holding.symbol, holding);
                });

                this.emit('holdingsUpdated', holdings);

                return {
                    success: true,
                    data: holdings
                };
            } else {
                return {
                    success: false,
                    message: response.data?.emsg || 'Failed to get holdings'
                };
            }
        } catch (error) {
            console.error('Failed to get holdings:', error);
            return {
                success: false,
                message: error.message || 'Failed to get holdings'
            };
        }
    }

    /**
     * Get portfolio summary
     */
    async getPortfolioSummary() {
        try {
            const [positionsResult, holdingsResult] = await Promise.all([
                this.getPositions(),
                this.getHoldings()
            ]);

            const summary = {
                positions: positionsResult.success ? positionsResult.data : [],
                holdings: holdingsResult.success ? holdingsResult.data : [],
                dayTradingStats: this.dayTradingStats,
                totalPortfolioValue: this.calculateTotalPortfolioValue(),
                riskMetrics: this.calculateRiskMetrics()
            };

            return {
                success: true,
                data: summary
            };
        } catch (error) {
            console.error('Failed to get portfolio summary:', error);
            return {
                success: false,
                message: error.message || 'Failed to get portfolio summary'
            };
        }
    }

    /**
     * Format position data
     */
    formatPositionData(rawPosition) {
        const quantity = parseInt(rawPosition.quantity || 0);
        const buyPrice = parseFloat(rawPosition.buy_price || rawPosition.average_price || 0);
        const currentPrice = parseFloat(rawPosition.last_price || rawPosition.ltp || 0);
        const pnl = (currentPrice - buyPrice) * Math.abs(quantity);
        const pnlPercent = buyPrice > 0 ? ((currentPrice - buyPrice) / buyPrice) * 100 : 0;

        return {
            symbol: rawPosition.trading_symbol || rawPosition.instrument_token,
            exchange: rawPosition.exchange,
            product: rawPosition.product,
            quantity: quantity,
            buyPrice: buyPrice,
            currentPrice: currentPrice,
            pnl: pnl,
            pnlPercent: pnlPercent,
            dayChange: parseFloat(rawPosition.day_change || 0),
            dayChangePercent: parseFloat(rawPosition.day_change_percentage || 0),
            marketValue: currentPrice * Math.abs(quantity),
            investedValue: buyPrice * Math.abs(quantity),
            side: quantity > 0 ? 'LONG' : 'SHORT',
            timestamp: new Date()
        };
    }

    /**
     * Format holding data
     */
    formatHoldingData(rawHolding) {
        const quantity = parseInt(rawHolding.quantity || 0);
        const buyPrice = parseFloat(rawHolding.average_price || 0);
        const currentPrice = parseFloat(rawHolding.last_price || rawHolding.ltp || 0);
        const pnl = (currentPrice - buyPrice) * quantity;
        const pnlPercent = buyPrice > 0 ? ((currentPrice - buyPrice) / buyPrice) * 100 : 0;

        return {
            symbol: rawHolding.trading_symbol || rawHolding.instrument_token,
            exchange: rawHolding.exchange,
            quantity: quantity,
            buyPrice: buyPrice,
            currentPrice: currentPrice,
            pnl: pnl,
            pnlPercent: pnlPercent,
            marketValue: currentPrice * quantity,
            investedValue: buyPrice * quantity,
            dayChange: parseFloat(rawHolding.day_change || 0),
            dayChangePercent: parseFloat(rawHolding.day_change_percentage || 0),
            timestamp: new Date()
        };
    }

    /**
     * Calculate day trading statistics
     */
    calculateDayTradingStats() {
        const positions = Array.from(this.positions.values());
        
        let totalPnL = 0;
        let totalInvestment = 0;
        let currentValue = 0;
        let winningTrades = 0;
        let losingTrades = 0;

        positions.forEach(position => {
            totalPnL += position.pnl;
            totalInvestment += position.investedValue;
            currentValue += position.marketValue;

            if (position.pnl > 0) {
                winningTrades++;
            } else if (position.pnl < 0) {
                losingTrades++;
            }
        });

        this.dayTradingStats = {
            totalPnL: totalPnL,
            realizedPnL: 0, // This would come from completed trades
            unrealizedPnL: totalPnL,
            totalTrades: positions.length,
            winningTrades: winningTrades,
            losingTrades: losingTrades,
            totalInvestment: totalInvestment,
            currentValue: currentValue,
            winRate: positions.length > 0 ? (winningTrades / positions.length) * 100 : 0
        };

        this.emit('dayTradingStatsUpdated', this.dayTradingStats);
    }

    /**
     * Calculate total portfolio value
     */
    calculateTotalPortfolioValue() {
        const positionsValue = Array.from(this.positions.values())
            .reduce((total, pos) => total + pos.marketValue, 0);
        
        const holdingsValue = Array.from(this.holdings.values())
            .reduce((total, holding) => total + holding.marketValue, 0);

        return positionsValue + holdingsValue;
    }

    /**
     * Calculate risk metrics
     */
    calculateRiskMetrics() {
        const positions = Array.from(this.positions.values());
        
        if (positions.length === 0) {
            return {
                maxDrawdown: 0,
                sharpeRatio: 0,
                volatility: 0,
                beta: 0,
                var95: 0 // Value at Risk 95%
            };
        }

        // Calculate basic risk metrics
        const pnlValues = positions.map(pos => pos.pnl);
        const totalPnL = pnlValues.reduce((sum, pnl) => sum + pnl, 0);
        const avgPnL = totalPnL / pnlValues.length;
        
        // Calculate standard deviation (volatility)
        const variance = pnlValues.reduce((sum, pnl) => sum + Math.pow(pnl - avgPnL, 2), 0) / pnlValues.length;
        const volatility = Math.sqrt(variance);

        // Calculate max drawdown
        let maxDrawdown = 0;
        let peak = 0;
        let runningPnL = 0;

        pnlValues.forEach(pnl => {
            runningPnL += pnl;
            if (runningPnL > peak) {
                peak = runningPnL;
            }
            const drawdown = (peak - runningPnL) / peak * 100;
            if (drawdown > maxDrawdown) {
                maxDrawdown = drawdown;
            }
        });

        // Simple Sharpe ratio calculation (assuming risk-free rate of 6%)
        const riskFreeRate = 0.06 / 252; // Daily risk-free rate
        const sharpeRatio = volatility > 0 ? (avgPnL - riskFreeRate) / volatility : 0;

        // Value at Risk (95% confidence)
        const sortedPnL = [...pnlValues].sort((a, b) => a - b);
        const var95Index = Math.floor(sortedPnL.length * 0.05);
        const var95 = sortedPnL[var95Index] || 0;

        return {
            maxDrawdown: maxDrawdown,
            sharpeRatio: sharpeRatio,
            volatility: volatility,
            beta: 1.0, // Simplified, would need market data for actual calculation
            var95: Math.abs(var95)
        };
    }

    /**
     * Get position by symbol
     */
    getPosition(symbol) {
        return this.positions.get(symbol);
    }

    /**
     * Get holding by symbol
     */
    getHolding(symbol) {
        return this.holdings.get(symbol);
    }

    /**
     * Get all positions
     */
    getAllPositions() {
        return Array.from(this.positions.values());
    }

    /**
     * Get all holdings
     */
    getAllHoldings() {
        return Array.from(this.holdings.values());
    }

    /**
     * Get positions by exchange
     */
    getPositionsByExchange(exchange) {
        return Array.from(this.positions.values()).filter(pos => pos.exchange === exchange);
    }

    /**
     * Get option positions
     */
    getOptionPositions() {
        return this.getPositionsByExchange('NFO');
    }

    /**
     * Get equity positions
     */
    getEquityPositions() {
        return this.getPositionsByExchange('NSE');
    }

    /**
     * Calculate position size for new trades
     */
    calculatePositionSize(symbol, riskAmount, stopLossPercent) {
        try {
            // Get current price (this would typically come from market data)
            const currentPrice = 100; // Placeholder
            
            if (stopLossPercent <= 0) {
                throw new Error('Stop loss percentage must be greater than 0');
            }

            const riskPerShare = currentPrice * (stopLossPercent / 100);
            const quantity = Math.floor(riskAmount / riskPerShare);

            return {
                quantity: quantity,
                riskPerShare: riskPerShare,
                totalRisk: quantity * riskPerShare,
                investmentRequired: quantity * currentPrice
            };
        } catch (error) {
            console.error('Failed to calculate position size:', error);
            return null;
        }
    }

    /**
     * Check if position exists
     */
    hasPosition(symbol) {
        return this.positions.has(symbol);
    }

    /**
     * Check if holding exists
     */
    hasHolding(symbol) {
        return this.holdings.has(symbol);
    }

    /**
     * Get portfolio performance metrics
     */
    getPerformanceMetrics() {
        const stats = this.dayTradingStats;
        const riskMetrics = this.calculateRiskMetrics();

        return {
            totalReturn: stats.totalPnL,
            totalReturnPercent: stats.totalInvestment > 0 ? (stats.totalPnL / stats.totalInvestment) * 100 : 0,
            winRate: stats.winRate,
            profitFactor: stats.losingTrades > 0 ? Math.abs(stats.totalPnL / (stats.losingTrades * -1)) : 0,
            averageWin: stats.winningTrades > 0 ? stats.totalPnL / stats.winningTrades : 0,
            averageLoss: stats.losingTrades > 0 ? stats.totalPnL / stats.losingTrades : 0,
            maxDrawdown: riskMetrics.maxDrawdown,
            sharpeRatio: riskMetrics.sharpeRatio,
            volatility: riskMetrics.volatility,
            var95: riskMetrics.var95
        };
    }

    /**
     * Export portfolio data
     */
    exportPortfolioData() {
        return {
            positions: this.getAllPositions(),
            holdings: this.getAllHoldings(),
            dayTradingStats: this.dayTradingStats,
            performanceMetrics: this.getPerformanceMetrics(),
            riskMetrics: this.calculateRiskMetrics(),
            exportedAt: new Date()
        };
    }

    /**
     * Update position with real-time price
     */
    updatePositionPrice(symbol, newPrice) {
        const position = this.positions.get(symbol);
        if (position) {
            const oldPnL = position.pnl;
            position.currentPrice = newPrice;
            position.pnl = (newPrice - position.buyPrice) * Math.abs(position.quantity);
            position.pnlPercent = position.buyPrice > 0 ? ((newPrice - position.buyPrice) / position.buyPrice) * 100 : 0;
            position.marketValue = newPrice * Math.abs(position.quantity);
            position.timestamp = new Date();

            // Recalculate day trading stats
            this.calculateDayTradingStats();

            // Emit position update event
            this.emit('positionUpdated', position, oldPnL);
        }
    }

    /**
     * Clear all data
     */
    clearData() {
        this.positions.clear();
        this.holdings.clear();
        this.dayTradingStats = {
            totalPnL: 0,
            realizedPnL: 0,
            unrealizedPnL: 0,
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            totalInvestment: 0,
            currentValue: 0
        };
    }
}

module.exports = PortfolioService;
