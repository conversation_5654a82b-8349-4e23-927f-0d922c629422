#!/bin/bash

echo "========================================"
echo "  Nifty Options Trading Bot Setup"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js (v16 or higher) first."
    echo "Visit: https://nodejs.org/"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

echo "[1/5] Installing dependencies..."
echo "Running: npm install"
if npm install; then
    print_status "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi
echo

echo "[2/5] Validating application setup..."
echo "Running: npm run validate:app"
if npm run validate:app; then
    print_status "Application validation completed"
else
    print_warning "Application validation found issues"
    print_info "You may continue, but please review the warnings above"
fi
echo

echo "[3/5] Running tests..."
echo "Running: npm test"
if npm test; then
    print_status "Tests completed successfully"
else
    print_warning "Some tests failed"
    print_info "You may continue, but please review the test results above"
fi
echo

echo "[4/5] Checking application structure..."
if [ -f "src/main/main.js" ] && [ -f "src/renderer/index.html" ]; then
    print_status "Application structure is valid"
else
    print_error "Application structure is invalid"
    exit 1
fi
echo

echo "[5/5] Setup completed!"
echo
print_info "Next steps:"
echo "1. Start the application: npm start"
echo "2. Configure your Kotak Neo Trade API credentials in Settings"
echo "3. Test the connection using the 'Test Connection' button"
echo "4. Authenticate with your TOTP code"
echo "5. Start trading!"
echo
print_info "To build for distribution, run: npm run build"
echo

# Ask if user wants to start the application now
read -p "Would you like to start the application now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo
    print_info "Starting the application..."
    print_info "The application will start in a new window"
    print_info "Press Ctrl+C in this terminal to stop the application"
    echo
    npm start
fi
