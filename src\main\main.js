const { app, BrowserWindow, <PERSON>u, ipc<PERSON>ain, dialog } = require('electron');
const path = require('path');
const isDev = process.argv.includes('--dev');

// Import services
const KotakAuthService = require('../services/kotakAuthService');
const MarketDataService = require('../services/marketDataService');
const WebSocketService = require('../services/websocketService');
const OptionChainService = require('../services/optionChainService');
const OrderService = require('../services/orderService');
const PortfolioService = require('../services/portfolioService');
const TradingEngine = require('../services/tradingEngine');

class TradingBotApp {
    constructor() {
        this.mainWindow = null;
        this.isQuitting = false;

        // Initialize services
        this.authService = new KotakAuthService();
        this.marketDataService = new MarketDataService(this.authService);
        this.wsService = new WebSocketService();
        this.optionChainService = new OptionChainService(this.marketDataService);
        this.orderService = new OrderService(this.authService);
        this.portfolioService = new PortfolioService(this.authService);
        this.tradingEngine = new TradingEngine(
            this.orderService,
            this.portfolioService,
            this.marketDataService,
            this.wsService
        );

        this.setupApp();
        this.setupIPC();
        this.setupServiceEventListeners();
    }

    // This is a temporary test function to run when the app starts.
    // It proves your backend logic works without needing the UI.
    async testBackendLtpFetch() {
        console.log('--- RUNNING BACKEND TEST ---');
        // Wait a moment for auth service to be ready
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Simulate the frontend calling the handler for Nifty 50
        const niftyResult = await ipcMain.handlers.get('get-index-ltp')(null, { indexName: 'Nifty 50' });

        if (niftyResult.success) {
            console.log(`✅ SUCCESS (Nifty 50): LTP is ${niftyResult.ltp}`);
        } else {
            console.error(`❌ FAILURE (Nifty 50): ${niftyResult.error}`);
        }

        // Simulate the frontend calling the handler for Bank Nifty
        const bankNiftyResult = await ipcMain.handlers.get('get-index-ltp')(null, { indexName: 'Bank Nifty' });

        if (bankNiftyResult.success) {
            console.log(`✅ SUCCESS (Bank Nifty): LTP is ${bankNiftyResult.ltp}`);
        } else {
            console.error(`❌ FAILURE (Bank Nifty): ${bankNiftyResult.error}`);
        }
        console.log('--- BACKEND TEST COMPLETE ---');
    }

    setupApp() {
        // Handle app ready
        app.whenReady().then(() => {
            this.createMainWindow();
            this.createMenu();

            // Run the backend test after window is created
            this.testBackendLtpFetch();
            
            app.on('activate', () => {
                if (BrowserWindow.getAllWindows().length === 0) {
                    this.createMainWindow();
                }
            });
        });

        // Handle window close
        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                app.quit();
            }
        });

        // Handle before quit
        app.on('before-quit', () => {
            this.isQuitting = true;
        });
    }

    createMainWindow() {
        this.mainWindow = new BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1200,
            minHeight: 800,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: path.join(__dirname, 'preload.js')
            },
            icon: path.join(__dirname, '../../assets/icon.png'),
            show: false,
            titleBarStyle: 'default'
        });

        // Load the app
        this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));

        // Show window when ready
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            
            if (isDev) {
                this.mainWindow.webContents.openDevTools();
            }
        });

        // Handle window close
        this.mainWindow.on('close', (event) => {
            if (!this.isQuitting) {
                event.preventDefault();
                this.mainWindow.hide();
            }
        });

        // Handle window closed
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
    }

    createMenu() {
        const template = [
            {
                label: 'File',
                submenu: [
                    {
                        label: 'Settings',
                        accelerator: 'CmdOrCtrl+,',
                        click: () => {
                            this.mainWindow.webContents.send('open-settings');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'Exit',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            this.isQuitting = true;
                            app.quit();
                        }
                    }
                ]
            },
            {
                label: 'Trading',
                submenu: [
                    {
                        label: 'Start Trading',
                        accelerator: 'CmdOrCtrl+S',
                        click: () => {
                            this.mainWindow.webContents.send('start-trading');
                        }
                    },
                    {
                        label: 'Stop Trading',
                        accelerator: 'CmdOrCtrl+T',
                        click: () => {
                            this.mainWindow.webContents.send('stop-trading');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'View Portfolio',
                        accelerator: 'CmdOrCtrl+P',
                        click: () => {
                            this.mainWindow.webContents.send('view-portfolio');
                        }
                    }
                ]
            },
            {
                label: 'View',
                submenu: [
                    { role: 'reload' },
                    { role: 'forceReload' },
                    { role: 'toggleDevTools' },
                    { type: 'separator' },
                    { role: 'resetZoom' },
                    { role: 'zoomIn' },
                    { role: 'zoomOut' },
                    { type: 'separator' },
                    { role: 'togglefullscreen' }
                ]
            },
            {
                label: 'Help',
                submenu: [
                    {
                        label: 'About',
                        click: () => {
                            dialog.showMessageBox(this.mainWindow, {
                                type: 'info',
                                title: 'About Nifty Options Trading Bot',
                                message: 'Nifty Options Trading Bot v1.0.0',
                                detail: 'Automated trading bot for Nifty 50 and Bank Nifty options using Kotak Neo Trade API'
                            });
                        }
                    }
                ]
            }
        ];

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    setupServiceEventListeners() {
        // WebSocket events
        this.wsService.on('connected', () => {
            this.sendToRenderer('connection-status-update', { isConnected: true });
        });

        this.wsService.on('disconnected', () => {
            this.sendToRenderer('connection-status-update', { isConnected: false });
        });

        this.wsService.on('quote', (data) => {
            this.sendToRenderer('market-data-update', data);
        });

        // Order events
        this.orderService.on('orderPlaced', (order) => {
            this.sendToRenderer('order-update', { type: 'placed', order });
        });

        this.orderService.on('orderExecuted', (order) => {
            this.sendToRenderer('order-update', { type: 'executed', order });
        });

        // Portfolio events
        this.portfolioService.on('positionsUpdated', (positions) => {
            this.sendToRenderer('portfolio-update', { positions });
        });

        // Trading engine events
        this.tradingEngine.on('engineStarted', () => {
            this.sendToRenderer('trading-engine-update', { isActive: true });
        });

        this.tradingEngine.on('engineStopped', () => {
            this.sendToRenderer('trading-engine-update', { isActive: false });
        });

        this.tradingEngine.on('stopLossExecuted', (data) => {
            this.sendToRenderer('notification', {
                title: 'Stop Loss Executed',
                message: `${data.symbol} at ₹${data.price}`,
                type: 'warning'
            });
        });

        this.tradingEngine.on('targetExecuted', (data) => {
            this.sendToRenderer('notification', {
                title: 'Target Achieved',
                message: `${data.symbol} at ₹${data.price}`,
                type: 'success'
            });
        });
    }

    sendToRenderer(channel, data) {
        if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send(channel, data);
        }
    }

    setupIPC() {
        // Authentication
        ipcMain.handle('authenticate', async (event, credentials) => {
            try {
                console.log('Main: Handling authentication request');
                const result = await this.authService.authenticate(credentials.totp);

                if (result.success) {
                    console.log('Main: Authentication successful, initializing WebSocket');
                    // Initialize WebSocket with auth token
                    try {
                        await this.wsService.initialize({
                            accessToken: this.authService.accessToken,
                            sessionId: this.authService.sessionId,
                            ucc: this.authService.ucc
                        });
                    } catch (wsError) {
                        console.warn('Main: WebSocket initialization failed:', wsError.message);
                        // Don't fail authentication if WebSocket fails
                    }
                }

                return result;
            } catch (error) {
                console.error('Main: Authentication error:', error);
                return { success: false, message: error.message };
            }
        });

        // Trading authentication (MPIN)
        ipcMain.handle('authenticate-trading', async (event, credentials) => {
            try {
                console.log('Main: Handling trading authentication request');
                const result = await this.authService.authenticateForTrading(credentials.mpin);
                return result;
            } catch (error) {
                console.error('Main: Trading authentication error:', error);
                return { success: false, message: error.message };
            }
        });

        // Market data
        ipcMain.handle('get-market-data', async (event, symbols) => {
            try {
                if (Array.isArray(symbols)) {
                    return await this.marketDataService.getQuotes(symbols);
                } else {
                    return await this.marketDataService.getQuote(symbols);
                }
            } catch (error) {
                return { success: false, message: error.message };
            }
        });

        ipcMain.handle('get-option-chain', async (event, params) => {
            try {
                return await this.optionChainService.getOptionChainForTrading(
                    params.symbol,
                    params.strikeCount || 5
                );
            } catch (error) {
                return { success: false, message: error.message };
            }
        });

        // Orders
        ipcMain.handle('place-order', async (event, orderData) => {
            try {
                return await this.orderService.placeOrder(orderData);
            } catch (error) {
                return { success: false, message: error.message };
            }
        });

        ipcMain.handle('cancel-order', async (event, orderId) => {
            try {
                return await this.orderService.cancelOrder(orderId);
            } catch (error) {
                return { success: false, message: error.message };
            }
        });

        ipcMain.handle('get-order-book', async (event) => {
            try {
                return await this.orderService.getOrderBook();
            } catch (error) {
                return { success: false, message: error.message };
            }
        });

        // Portfolio
        ipcMain.handle('get-portfolio', async (event) => {
            try {
                return await this.portfolioService.getPortfolioSummary();
            } catch (error) {
                return { success: false, message: error.message };
            }
        });

        ipcMain.handle('get-positions', async (event) => {
            try {
                return await this.portfolioService.getPositions();
            } catch (error) {
                return { success: false, message: error.message };
            }
        });

        // Trading Engine
        ipcMain.handle('start-trading-engine', async (event) => {
            try {
                return await this.tradingEngine.start();
            } catch (error) {
                return { success: false, message: error.message };
            }
        });

        ipcMain.handle('stop-trading-engine', async (event) => {
            try {
                return await this.tradingEngine.stop();
            } catch (error) {
                return { success: false, message: error.message };
            }
        });

        ipcMain.handle('get-trading-status', async (event) => {
            try {
                return { success: true, data: this.tradingEngine.getStatus() };
            } catch (error) {
                return { success: false, message: error.message };
            }
        });

        // Settings
        ipcMain.handle('save-settings', async (event, settings) => {
            try {
                if (settings.credentials) {
                    this.authService.saveCredentials(settings.credentials);
                }
                return { success: true };
            } catch (error) {
                return { success: false, message: error.message };
            }
        });

        ipcMain.handle('get-settings', async (event) => {
            try {
                const credentials = this.authService.getCredentials();
                return {
                    success: true,
                    settings: {
                        credentials: credentials ? {
                            ucc: credentials.ucc,
                            mobileNumber: credentials.mobileNumber,
                            consumerKey: credentials.consumerKey,
                            environment: credentials.environment
                        } : null
                    }
                };
            } catch (error) {
                return { success: false, message: error.message };
            }
        });

        // Test connection
        ipcMain.handle('test-connection', async (event) => {
            try {
                console.log('Main: Testing connection...');
                const result = await this.authService.testConnection();
                return result;
            } catch (error) {
                console.error('Main: Test connection error:', error);
                return { success: false, message: error.message };
            }
        });

        // Script Master
        ipcMain.handle('get-script-master-files', async (event) => {
            try {
                const axios = require('axios');
                const response = await axios.get('https://gw-napi.kotaksecurities.com/Files/1.0/masterscrip/v2/file-paths', {
                    headers: {
                        'accept': '*/*',
                        'Authorization': `Bearer ${this.authService.getClientToken()}`
                    }
                });
                return { success: true, data: response.data.data };
            } catch (error) {
                console.error('Failed to get script master files:', error);
                return { success: false, message: error.message };
            }
        });

        ipcMain.handle('download-script-master', async (event, fileUrl) => {
            try {
                const axios = require('axios');
                const response = await axios.get(fileUrl, {
                    headers: {
                        'accept': '*/*',
                        'Authorization': `Bearer ${this.authService.getClientToken()}`
                    }
                });
                return { success: true, data: response.data };
            } catch (error) {
                console.error('Failed to download script master:', error);
                return { success: false, message: error.message };
            }
        });

        // Get market quotes
        ipcMain.handle('get-quotes', async (event, neoSymbols, quoteType = 'all') => {
            try {
                const axios = require('axios');
                const symbolsParam = encodeURIComponent(neoSymbols);
                const url = `https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/${symbolsParam}/${quoteType}`;

                console.log('Getting quotes for:', neoSymbols);
                const response = await axios.get(url, {
                    headers: {
                        'accept': 'application/json',
                        'Authorization': `Bearer ${this.authService.getClientToken()}`
                    }
                });

                return { success: true, data: response.data };
            } catch (error) {
                console.error('Failed to get quotes:', error);
                return { success: false, message: error.message };
            }
        });

        // IPC Handler specifically for fetching the index Last Traded Price (LTP)
        ipcMain.handle('get-index-ltp', async (event, { indexName }) => {
            console.log(`[IPC] Backend received request for LTP for: ${indexName}`);

            // 1. Define the correct Neo Symbols
            const indexSymbolMap = {
                'Nifty 50': 'nse_cm|NIFTY 50',
                'Bank Nifty': 'nse_cm|Nifty Bank'
            };
            const neoSymbol = indexSymbolMap[indexName];

            if (!neoSymbol) {
                console.error(`[IPC] Invalid index name received: ${indexName}`);
                return { success: false, error: `Invalid index name: ${indexName}` };
            }

            try {
                // 2. Get the required ACCESS TOKEN (not the session/trading token)
                const accessToken = this.authService.getClientToken();

                if (!accessToken) {
                    throw new Error('Authentication failed: Access Token is missing.');
                }

                // 3. Construct the API URL
                // We use encodeURIComponent to safely handle special characters like '|'
                const quotesApiUrl = `https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/${encodeURIComponent(neoSymbol)}/ltp`;
                console.log(`[API] Calling URL: ${quotesApiUrl}`);

                // 4. Make the API call using axios
                const response = await axios.get(quotesApiUrl, {
                    headers: {
                        'accept': 'application/json',
                        'Authorization': `Bearer ${accessToken}`
                    }
                });

                // 5. Process the response
                console.log('[API] Response Received:', JSON.stringify(response.data, null, 2));

                // The response is an array, we need the first element
                if (response.data && response.data.length > 0) {
                    const ltp = response.data[0].ltp;
                    return { success: true, ltp: parseFloat(ltp) };
                } else {
                    throw new Error('API returned empty data array.');
                }

            } catch (error) {
                // 6. Handle errors robustly
                const errorMessage = error.response ? JSON.stringify(error.response.data) : error.message;
                console.error('[IPC] Error fetching index LTP:', errorMessage);
                return { success: false, error: errorMessage };
            }
        });

        // WebSocket management
        ipcMain.handle('subscribe-market-data', async (event, symbols) => {
            try {
                this.wsService.subscribeQuote(symbols);
                return { success: true };
            } catch (error) {
                return { success: false, message: error.message };
            }
        });

        ipcMain.handle('unsubscribe-market-data', async (event, symbols) => {
            try {
                this.wsService.unsubscribe(symbols);
                return { success: true };
            } catch (error) {
                return { success: false, message: error.message };
            }
        });

        // Utility functions
        ipcMain.handle('show-notification', async (event, data) => {
            // Handle system notifications
            return { success: true };
        });

        ipcMain.handle('show-error-dialog', async (event, data) => {
            const result = await dialog.showMessageBox(this.mainWindow, {
                type: 'error',
                title: data.title,
                message: data.content,
                buttons: ['OK']
            });
            return result;
        });

        ipcMain.handle('show-confirm-dialog', async (event, data) => {
            const result = await dialog.showMessageBox(this.mainWindow, {
                type: 'question',
                title: data.title,
                message: data.content,
                buttons: ['Yes', 'No'],
                defaultId: 0,
                cancelId: 1
            });
            return result;
        });
    }
}

// Create and start the application
new TradingBotApp();
