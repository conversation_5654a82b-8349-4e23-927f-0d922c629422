/**
 * Simple Kotak Neo Trade API Authentication Test
 * Just edit the credentials below and run: node simple_auth_test.js
 */

const axios = require('axios');

// ============================================
// EDIT YOUR CREDENTIALS HERE:
// ============================================
const CREDENTIALS = {
    consumerKey: 'GfS95Qwtx6Pic9Up3P6sqmyRlu8a',
    consumerSecret: 'v6Foh1P4NjdL6StvUTqMtqBBBdEa',
    ucc: 'ZHBTW',
    mobileNumber: '9999977894',
    
    // ENTER YOUR TOTP AND MPIN WHEN READY TO TEST:
    totp: '147498', // Enter 6-digit TOTP code here
    mpin: '002888'  // Enter 4-6 digit MPIN here (optional)
};

class SimpleAuthTest {
    constructor() {
        this.clientAccessToken = null;
        this.viewToken = null;
        this.tradingToken = null;
        this.sessionId = null;
    }

    async step1_getClientToken() {
        console.log('🔑 Step 1: Getting Client Access Token...');
        
        try {
            const auth = Buffer.from(`${CREDENTIALS.consumerKey}:${CREDENTIALS.consumerSecret}`).toString('base64');
            console.log(`Using Authorization: Basic ${auth}`);

            const response = await axios.post(
                'https://napi.kotaksecurities.com/oauth2/token',
                'grant_type=client_credentials',
                {
                    headers: {
                        'Authorization': `Basic ${auth}`,
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                }
            );

            if (response.data && response.data.access_token) {
                this.clientAccessToken = response.data.access_token;
                console.log('✅ SUCCESS: Client token obtained');
                console.log(`Token: ${this.clientAccessToken.substring(0, 50)}...`);
                return true;
            } else {
                console.log('❌ FAILED: No access token in response');
                console.log('Response:', response.data);
                return false;
            }
        } catch (error) {
            console.log('❌ ERROR in Step 1:');
            if (error.response) {
                console.log('Status:', error.response.status);
                console.log('Data:', error.response.data);
            } else {
                console.log('Error:', error.message);
            }
            return false;
        }
    }

    async step2_getViewToken() {
        console.log('\n🔐 Step 2: Getting View Token with TOTP...');
        
        if (!CREDENTIALS.totp) {
            console.log('❌ TOTP not provided. Please edit CREDENTIALS.totp in the script.');
            return false;
        }

        try {
            const requestData = {
                mobileNumber: `+91${CREDENTIALS.mobileNumber}`,
                ucc: CREDENTIALS.ucc,
                totp: CREDENTIALS.totp
            };

            console.log('Request data:', requestData);

            const response = await axios.post(
                'https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/login',
                requestData,
                {
                    headers: {
                        'Authorization': `Bearer ${this.clientAccessToken}`,
                        'neo-fin-key': 'neotradeapi',
                        'Content-Type': 'application/json'
                    }
                }
            );

            console.log('Response status:', response.status);
            console.log('Response data:', JSON.stringify(response.data, null, 2));

            if (response.data && response.data.data && response.data.data.token) {
                this.viewToken = response.data.data.token;
                this.sessionId = response.data.data.sid;
                console.log('✅ SUCCESS: View token obtained');
                console.log(`Session ID: ${this.sessionId}`);
                console.log(`Greeting: ${response.data.data.greetingName}`);
                console.log(`kType: ${response.data.data.kType}`);
                return true;
            } else {
                console.log('❌ FAILED: No view token in response');
                return false;
            }
        } catch (error) {
            console.log('❌ ERROR in Step 2:');
            if (error.response) {
                console.log('Status:', error.response.status);
                console.log('Data:', error.response.data);
            } else {
                console.log('Error:', error.message);
            }
            return false;
        }
    }

    async step3_getTradingToken() {
        console.log('\n🚀 Step 3: Getting Trading Token with MPIN...');
        
        if (!CREDENTIALS.mpin) {
            console.log('⏭️ MPIN not provided. Skipping trading token (View mode only).');
            return true;
        }

        try {
            const requestData = {
                mpin: CREDENTIALS.mpin
            };

            console.log('Request data:', requestData);

            const response = await axios.post(
                'https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/validate',
                requestData,
                {
                    headers: {
                        'accept': 'application/json',
                        'sid': this.sessionId,
                        'Auth': this.viewToken,
                        'neo-fin-key': 'neotradeapi',
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.clientAccessToken}`
                    }
                }
            );

            console.log('Response status:', response.status);
            console.log('Response data:', JSON.stringify(response.data, null, 2));

            if (response.data && response.data.data && response.data.data.token) {
                this.tradingToken = response.data.data.token;
                this.sessionId = response.data.data.sid;
                console.log('✅ SUCCESS: Trading token obtained');
                console.log(`New Session ID: ${this.sessionId}`);
                console.log(`kType: ${response.data.data.kType}`);
                return true;
            } else {
                console.log('❌ FAILED: No trading token in response');
                return false;
            }
        } catch (error) {
            console.log('❌ ERROR in Step 3:');
            if (error.response) {
                console.log('Status:', error.response.status);
                console.log('Data:', error.response.data);
            } else {
                console.log('Error:', error.message);
            }
            return false;
        }
    }

    async runFullTest() {
        console.log('🧪 Kotak Neo Trade API Authentication Test');
        console.log('==========================================\n');
        
        console.log('Testing with credentials:');
        console.log(`Consumer Key: ${CREDENTIALS.consumerKey}`);
        console.log(`UCC: ${CREDENTIALS.ucc}`);
        console.log(`Mobile: ${CREDENTIALS.mobileNumber}`);
        console.log(`TOTP: ${CREDENTIALS.totp || 'NOT PROVIDED'}`);
        console.log(`MPIN: ${CREDENTIALS.mpin || 'NOT PROVIDED'}`);
        console.log('\n');

        // Step 1: Client Token
        const step1Success = await this.step1_getClientToken();
        if (!step1Success) {
            console.log('\n❌ Test FAILED at Step 1');
            return;
        }

        // Step 2: View Token
        const step2Success = await this.step2_getViewToken();
        if (!step2Success) {
            console.log('\n❌ Test FAILED at Step 2');
            return;
        }

        // Step 3: Trading Token (optional)
        const step3Success = await this.step3_getTradingToken();

        // Summary
        console.log('\n📊 TEST SUMMARY:');
        console.log('================');
        console.log(`✅ Client Token: ${this.clientAccessToken ? 'SUCCESS' : 'FAILED'}`);
        console.log(`✅ View Token: ${this.viewToken ? 'SUCCESS' : 'FAILED'}`);
        console.log(`${this.tradingToken ? '✅' : '⏭️'} Trading Token: ${this.tradingToken ? 'SUCCESS' : 'SKIPPED'}`);
        
        if (this.viewToken) {
            console.log('\n🎉 AUTHENTICATION SUCCESSFUL!');
            console.log('You can now use these tokens for API calls.');
            
            console.log('\n🔑 Tokens for integration:');
            console.log(`clientAccessToken: ${this.clientAccessToken}`);
            console.log(`viewToken: ${this.viewToken}`);
            console.log(`tradingToken: ${this.tradingToken || 'N/A'}`);
            console.log(`sessionId: ${this.sessionId}`);
        } else {
            console.log('\n❌ AUTHENTICATION FAILED!');
        }
    }
}

// Validation
function validateCredentials() {
    const errors = [];
    
    if (!CREDENTIALS.consumerKey) errors.push('Consumer Key is required');
    if (!CREDENTIALS.consumerSecret) errors.push('Consumer Secret is required');
    if (!CREDENTIALS.ucc) errors.push('UCC is required');
    if (!CREDENTIALS.mobileNumber) errors.push('Mobile Number is required');
    
    if (CREDENTIALS.mobileNumber && !/^\d{10}$/.test(CREDENTIALS.mobileNumber)) {
        errors.push('Mobile Number must be 10 digits');
    }
    
    if (CREDENTIALS.totp && !/^\d{6}$/.test(CREDENTIALS.totp)) {
        errors.push('TOTP must be 6 digits');
    }
    
    if (CREDENTIALS.mpin && !/^\d{4,6}$/.test(CREDENTIALS.mpin)) {
        errors.push('MPIN must be 4-6 digits');
    }
    
    return errors;
}

// Run the test
async function main() {
    const errors = validateCredentials();
    
    if (errors.length > 0) {
        console.log('❌ Credential Validation Errors:');
        errors.forEach(error => console.log(`  - ${error}`));
        console.log('\nPlease fix the credentials in the script and try again.');
        return;
    }
    
    if (!CREDENTIALS.totp) {
        console.log('⚠️  TOTP not provided. Please:');
        console.log('1. Get a 6-digit TOTP code from your authenticator app');
        console.log('2. Edit CREDENTIALS.totp in this script');
        console.log('3. Run the script again immediately');
        return;
    }
    
    const test = new SimpleAuthTest();
    await test.runFullTest();
}

if (require.main === module) {
    main().catch(error => {
        console.error('Fatal error:', error);
    });
}

module.exports = SimpleAuthTest;
