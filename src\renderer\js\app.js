/**
 * Main Application Class
 * Handles the overall application state and coordination between components
 */
class TradingBotApp {
    constructor() {
        this.isAuthenticated = false;
        this.isConnected = false;
        this.isTradingActive = false;
        this.currentTab = 'dashboard';
        this.marketData = new Map();
        this.portfolio = null;
        this.settings = null;
        
        this.init();
    }

    async init() {
        try {
            // Show loading screen
            this.showLoading();
            
            // Initialize components
            await this.initializeComponents();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Load settings
            await this.loadSettings();
            
            // Initialize services
            await this.initializeServices();
            
            // Hide loading screen and show main app
            this.hideLoading();
            
            console.log('Trading Bot Application initialized successfully');
        } catch (error) {
            console.error('Failed to initialize application:', error);
            this.showError('Failed to initialize application', error.message);
        }
    }

    showLoading() {
        document.getElementById('loading-screen').classList.remove('hidden');
        document.getElementById('main-app').classList.add('hidden');
    }

    hideLoading() {
        document.getElementById('loading-screen').classList.add('hidden');
        document.getElementById('main-app').classList.remove('hidden');
    }

    async initializeComponents() {
        // Initialize authentication component first
        if (typeof AuthComponent !== 'undefined') {
            this.authComponent = new AuthComponent(this);
            window.authComponent = this.authComponent; // Make globally accessible
        }

        // Initialize dashboard component
        if (typeof Dashboard !== 'undefined') {
            this.dashboard = new Dashboard(this);
        }

        // Initialize trading component
        if (typeof Trading !== 'undefined') {
            this.trading = new Trading(this);
            window.tradingComponent = this.trading; // Make globally accessible
        }

        // Initialize settings component
        if (typeof Settings !== 'undefined') {
            this.settings = new Settings(this);
        }

        // Initialize API service
        if (typeof APIService !== 'undefined') {
            this.apiService = new APIService();
        }

        // Initialize WebSocket service
        if (typeof WebSocketService !== 'undefined') {
            this.wsService = new WebSocketService();
        }

        console.log('All components initialized successfully');

        // Check credential status after components are initialized
        if (this.authComponent) {
            this.authComponent.checkAuthStatus();
        }
    }

    /**
     * Update credential configuration status
     */
    updateCredentialStatus(hasCredentials) {
        this.hasCredentials = hasCredentials;

        // Update header status
        const authStatus = document.querySelector('.auth-status');
        if (authStatus) {
            const statusText = authStatus.querySelector('.status-text');
            const statusIndicator = authStatus.querySelector('.status-indicator');

            if (hasCredentials) {
                statusText.textContent = 'Ready to Login';
                statusIndicator.className = 'status-indicator ready';
            } else {
                statusText.textContent = 'Configure Settings';
                statusIndicator.className = 'status-indicator offline';
            }
        }

        // Update login button
        const loginBtn = document.getElementById('login-btn');
        if (loginBtn) {
            if (hasCredentials) {
                loginBtn.textContent = 'Login';
                loginBtn.disabled = false;
                loginBtn.onclick = () => this.showLogin();
            } else {
                loginBtn.textContent = 'Setup Required';
                loginBtn.disabled = true;
                loginBtn.onclick = () => this.openSettings();
            }
        }

        console.log(`Credential status updated: hasCredentials=${hasCredentials}`);
    }

    /**
     * Update authentication status throughout the app
     */
    updateAuthStatus(isAuthenticated, canTrade) {
        this.isAuthenticated = isAuthenticated;
        this.canTrade = canTrade;

        // Update header status
        const authStatus = document.querySelector('.auth-status');
        if (authStatus) {
            const statusText = authStatus.querySelector('.status-text');
            const statusIndicator = authStatus.querySelector('.status-indicator');

            if (isAuthenticated) {
                statusText.textContent = canTrade ? 'Trading' : 'View Only';
                statusIndicator.className = 'status-indicator online';
            } else {
                statusText.textContent = this.hasCredentials ? 'Ready to Login' : 'Configure Settings';
                statusIndicator.className = this.hasCredentials ? 'status-indicator ready' : 'status-indicator offline';
            }
        }

        // Update login button
        const loginBtn = document.getElementById('login-btn');
        if (loginBtn) {
            if (isAuthenticated) {
                loginBtn.textContent = 'Logout';
                loginBtn.disabled = false;
                loginBtn.onclick = () => this.logout();
            } else if (this.hasCredentials) {
                loginBtn.textContent = 'Login';
                loginBtn.disabled = false;
                loginBtn.onclick = () => this.showLogin();
            } else {
                loginBtn.textContent = 'Setup Required';
                loginBtn.disabled = true;
                loginBtn.onclick = () => this.openSettings();
            }
        }

        // Enable/disable trading features
        const tradingElements = document.querySelectorAll('.trading-only');
        tradingElements.forEach(element => {
            if (canTrade) {
                element.classList.remove('disabled');
                element.removeAttribute('disabled');
            } else {
                element.classList.add('disabled');
                element.setAttribute('disabled', 'true');
            }
        });

        // Update dashboard if available
        if (this.dashboard && this.dashboard.updateAuthStatus) {
            this.dashboard.updateAuthStatus(isAuthenticated, canTrade);
        }

        // Update trading component if available
        if (this.trading && this.trading.updateAuthStatus) {
            this.trading.updateAuthStatus(isAuthenticated, canTrade);
        }

        console.log(`Auth status updated: authenticated=${isAuthenticated}, canTrade=${canTrade}`);
    }

    /**
     * Show login modal
     */
    showLogin() {
        if (this.authComponent) {
            this.authComponent.showAuthModal();
        } else {
            console.error('Auth component not available');
        }
    }

    /**
     * Logout user
     */
    async logout() {
        try {
            // Call logout on auth component
            if (this.authComponent) {
                this.authComponent.logout();
            }

            // Update UI
            this.updateAuthStatus(false, false);

            // Show notification
            if (window.notificationService) {
                window.notificationService.info('Logged Out', 'You have been logged out successfully');
            }

        } catch (error) {
            console.error('Logout error:', error);
        }
    }

    setupEventListeners() {
        // Navigation
        this.setupNavigation();
        
        // Header controls
        this.setupHeaderControls();
        
        // Trading controls
        this.setupTradingControls();
        
        // Settings modal
        this.setupSettingsModal();
        
        // Electron API event listeners
        this.setupElectronEventListeners();
        
        // Window events
        this.setupWindowEvents();
    }

    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const tabName = item.dataset.tab;
                this.switchTab(tabName);
            });
        });
    }

    setupHeaderControls() {
        // Settings button
        const settingsBtn = document.getElementById('settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.openSettings();
            });
        }

        // Login button
        const loginBtn = document.getElementById('login-btn');
        if (loginBtn) {
            loginBtn.addEventListener('click', () => {
                if (this.isAuthenticated) {
                    this.logout();
                } else {
                    this.showLogin();
                }
            });
        }
    }

    setupTradingControls() {
        const startBtn = document.getElementById('start-trading-btn');
        const stopBtn = document.getElementById('stop-trading-btn');
        const emergencyBtn = document.getElementById('emergency-stop-btn');

        if (startBtn) {
            startBtn.addEventListener('click', () => {
                this.startTrading();
            });
        }

        if (stopBtn) {
            stopBtn.addEventListener('click', () => {
                this.stopTrading();
            });
        }

        if (emergencyBtn) {
            emergencyBtn.addEventListener('click', () => {
                this.emergencyStop();
            });
        }
    }

    setupSettingsModal() {
        const closeBtn = document.getElementById('close-settings');
        const modal = document.getElementById('settings-modal');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.closeSettings();
            });
        }

        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeSettings();
                }
            });
        }
    }

    setupElectronEventListeners() {
        if (window.electronAPI) {
            // Menu events
            window.electronAPI.onOpenSettings(() => {
                this.openSettings();
            });

            window.electronAPI.onStartTrading(() => {
                this.startTrading();
            });

            window.electronAPI.onStopTrading(() => {
                this.stopTrading();
            });

            window.electronAPI.onViewPortfolio(() => {
                this.switchTab('portfolio');
            });

            // Market data updates
            window.electronAPI.onMarketDataUpdate((event, data) => {
                this.handleMarketDataUpdate(data);
            });

            // Order updates
            window.electronAPI.onOrderUpdate((event, data) => {
                this.handleOrderUpdate(data);
            });

            // Portfolio updates
            window.electronAPI.onPortfolioUpdate((event, data) => {
                this.handlePortfolioUpdate(data);
            });

            // Trading engine updates
            window.electronAPI.onTradingEngineUpdate((event, data) => {
                this.handleTradingEngineUpdate(data);
            });

            // Notifications
            window.electronAPI.onNotification((event, data) => {
                this.showNotification(data.title, data.message, data.type);
            });
        }
    }

    setupWindowEvents() {
        // Update market time
        this.updateMarketTime();
        setInterval(() => {
            this.updateMarketTime();
        }, 1000);

        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Handle keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    async loadSettings() {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.getSettings();
                if (result.success) {
                    this.settings = result.settings;
                }
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }

    async initializeServices() {
        try {
            // Initialize API service with settings
            if (this.apiService && this.settings) {
                await this.apiService.initialize(this.settings);
            }
            
            // Initialize WebSocket service
            if (this.wsService) {
                await this.wsService.initialize();
            }
        } catch (error) {
            console.error('Failed to initialize services:', error);
        }
    }

    switchTab(tabName) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        this.currentTab = tabName;

        // Notify components of tab change
        this.notifyTabChange(tabName);
    }

    notifyTabChange(tabName) {
        // Notify relevant components
        switch (tabName) {
            case 'dashboard':
                if (this.dashboard) {
                    this.dashboard.onTabActivated();
                }
                break;
            case 'trading':
                if (this.trading) {
                    this.trading.onTabActivated();
                }
                break;
        }
    }

    async startTrading() {
        try {
            if (!this.isAuthenticated) {
                this.showError('Authentication Required', 'Please configure your API credentials in settings first.');
                return;
            }

            const result = await window.electronAPI.startTradingEngine();
            if (result.success) {
                this.isTradingActive = true;
                this.updateTradingStatus();
                this.showNotification('Trading Started', 'Automated trading has been started successfully.', 'success');
            } else {
                this.showError('Failed to Start Trading', result.message);
            }
        } catch (error) {
            console.error('Failed to start trading:', error);
            this.showError('Failed to Start Trading', error.message);
        }
    }

    async stopTrading() {
        try {
            const result = await window.electronAPI.stopTradingEngine();
            if (result.success) {
                this.isTradingActive = false;
                this.updateTradingStatus();
                this.showNotification('Trading Stopped', 'Automated trading has been stopped.', 'info');
            } else {
                this.showError('Failed to Stop Trading', result.message);
            }
        } catch (error) {
            console.error('Failed to stop trading:', error);
            this.showError('Failed to Stop Trading', error.message);
        }
    }

    async emergencyStop() {
        try {
            const confirmed = await this.showConfirmDialog(
                'Emergency Stop',
                'This will immediately stop all trading activities and cancel all pending orders. Are you sure?'
            );

            if (confirmed) {
                // Stop trading engine
                await this.stopTrading();
                
                // Cancel all orders
                // This will be implemented when order management is ready
                
                this.showNotification('Emergency Stop Executed', 'All trading activities have been stopped.', 'warning');
            }
        } catch (error) {
            console.error('Emergency stop failed:', error);
            this.showError('Emergency Stop Failed', error.message);
        }
    }

    openSettings() {
        document.getElementById('settings-modal').classList.remove('hidden');
        if (this.settings) {
            this.settings.show();
        }
    }

    closeSettings() {
        document.getElementById('settings-modal').classList.add('hidden');
    }

    updateConnectionStatus(isConnected) {
        this.isConnected = isConnected;
        const statusElement = document.getElementById('connection-status');
        const indicator = statusElement.querySelector('.status-indicator');
        const text = statusElement.querySelector('.status-text');

        if (isConnected) {
            indicator.classList.remove('disconnected', 'connecting');
            indicator.classList.add('connected');
            text.textContent = 'Connected';
        } else {
            indicator.classList.remove('connected', 'connecting');
            indicator.classList.add('disconnected');
            text.textContent = 'Disconnected';
        }
    }

    updateTradingStatus() {
        const statusElement = document.getElementById('trading-status');
        const indicator = statusElement.querySelector('.trading-indicator');
        const text = statusElement.querySelector('.trading-text');
        const startBtn = document.getElementById('start-trading-btn');
        const stopBtn = document.getElementById('stop-trading-btn');

        if (this.isTradingActive) {
            indicator.classList.remove('stopped');
            indicator.classList.add('running');
            text.textContent = 'Trading Active';
            if (startBtn) startBtn.disabled = true;
            if (stopBtn) stopBtn.disabled = false;
        } else {
            indicator.classList.remove('running');
            indicator.classList.add('stopped');
            text.textContent = 'Trading Stopped';
            if (startBtn) startBtn.disabled = false;
            if (stopBtn) stopBtn.disabled = true;
        }
    }

    updateMarketTime() {
        const timeElement = document.getElementById('market-time');
        if (timeElement) {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-IN', {
                timeZone: 'Asia/Kolkata',
                hour12: false
            });
            timeElement.textContent = `IST ${timeString}`;
        }
    }

    handleMarketDataUpdate(data) {
        // Update market data
        if (data.symbol) {
            this.marketData.set(data.symbol, data);
        }

        // Notify components
        if (this.dashboard) {
            this.dashboard.updateMarketData(data);
        }
        if (this.trading) {
            this.trading.updateMarketData(data);
        }
    }

    handleOrderUpdate(data) {
        // Notify components
        if (this.dashboard) {
            this.dashboard.updateOrderData(data);
        }
    }

    handlePortfolioUpdate(data) {
        this.portfolio = data;
        
        // Notify components
        if (this.dashboard) {
            this.dashboard.updatePortfolioData(data);
        }
    }

    handleTradingEngineUpdate(data) {
        this.isTradingActive = data.isActive;
        this.updateTradingStatus();
    }

    handleResize() {
        // Handle responsive layout changes
        // This can be expanded based on specific needs
    }

    handleKeyboardShortcuts(e) {
        // Handle keyboard shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case ',':
                    e.preventDefault();
                    this.openSettings();
                    break;
                case 's':
                    e.preventDefault();
                    if (!this.isTradingActive) {
                        this.startTrading();
                    }
                    break;
                case 't':
                    e.preventDefault();
                    if (this.isTradingActive) {
                        this.stopTrading();
                    }
                    break;
                case 'p':
                    e.preventDefault();
                    this.switchTab('portfolio');
                    break;
            }
        }
    }

    showNotification(title, message, type = 'info') {
        // This will be implemented in a separate notification component
        console.log(`[${type.toUpperCase()}] ${title}: ${message}`);
    }

    showError(title, message) {
        this.showNotification(title, message, 'error');
    }

    async showConfirmDialog(title, message) {
        if (window.electronAPI) {
            const result = await window.electronAPI.showConfirmDialog(title, message);
            return result.response === 0; // 0 is typically "OK" or "Yes"
        }
        return confirm(`${title}\n\n${message}`);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.tradingApp = new TradingBotApp();
});
