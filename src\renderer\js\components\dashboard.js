/**
 * Dashboard Component
 * Handles the main dashboard display with market overview, stats, and controls
 */
class Dashboard {
    constructor(app) {
        this.app = app;
        this.marketData = new Map();
        this.portfolioData = null;
        this.tradingStats = null;
        this.refreshInterval = null;
        this.activityLog = [];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startAutoRefresh();
        this.loadInitialData();
    }

    setupEventListeners() {
        // Market data updates
        document.addEventListener('marketDataUpdate', (e) => {
            this.handleMarketDataUpdate(e.detail);
        });

        // Portfolio updates
        document.addEventListener('portfolioUpdate', (e) => {
            this.handlePortfolioUpdate(e.detail);
        });

        // Trading engine updates
        document.addEventListener('tradingEngineUpdate', (e) => {
            this.handleTradingEngineUpdate(e.detail);
        });

        // Activity logging
        document.addEventListener('orderPlaced', (e) => {
            this.logActivity('Order placed', `${e.detail.symbol} ${e.detail.orderType} ${e.detail.quantity}`);
        });

        document.addEventListener('stopLossExecuted', (e) => {
            this.logActivity('Stop Loss Hit', `${e.detail.symbol} at ₹${e.detail.price}`);
        });

        document.addEventListener('targetExecuted', (e) => {
            this.logActivity('Target Hit', `${e.detail.symbol} at ₹${e.detail.price}`);
        });
    }

    async loadInitialData() {
        try {
            // Load market data for indices
            await this.loadMarketIndices();
            
            // Load portfolio data
            await this.loadPortfolioData();
            
            // Load trading statistics
            await this.loadTradingStats();
            
            // Subscribe to real-time updates
            await this.subscribeToRealTimeData();
        } catch (error) {
            console.error('Failed to load initial dashboard data:', error);
        }
    }

    async loadMarketIndices() {
        try {
            if (window.electronAPI) {
                const symbols = ['NIFTY 50', 'NIFTY BANK'];
                const result = await window.electronAPI.getMarketData(symbols);
                
                if (result.success) {
                    result.data.forEach(quote => {
                        this.marketData.set(quote.symbol, quote);
                        this.updateMarketIndex(quote);
                    });
                }
            }
        } catch (error) {
            console.error('Failed to load market indices:', error);
        }
    }

    async loadPortfolioData() {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.getPortfolio();
                
                if (result.success) {
                    this.portfolioData = result.portfolio;
                    this.updatePortfolioStats();
                }
            }
        } catch (error) {
            console.error('Failed to load portfolio data:', error);
        }
    }

    async loadTradingStats() {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.getTradingStatus();
                
                if (result.success) {
                    this.tradingStats = result.data;
                    this.updateTradingStats();
                }
            }
        } catch (error) {
            console.error('Failed to load trading stats:', error);
        }
    }

    async subscribeToRealTimeData() {
        try {
            if (this.app.wsService) {
                // Subscribe to Nifty and Bank Nifty
                await this.app.wsService.subscribeToNifty((data) => {
                    this.handleMarketDataUpdate(data);
                });
                
                await this.app.wsService.subscribeToBankNifty((data) => {
                    this.handleMarketDataUpdate(data);
                });
            }
        } catch (error) {
            console.error('Failed to subscribe to real-time data:', error);
        }
    }

    updateMarketIndex(quote) {
        const symbol = quote.symbol;
        let elementId;
        
        if (symbol.includes('NIFTY 50') || symbol === 'NIFTY') {
            elementId = 'nifty-50';
        } else if (symbol.includes('BANK') || symbol.includes('BANKNIFTY')) {
            elementId = 'bank-nifty';
        }
        
        if (elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                const priceElement = element.querySelector('.index-price');
                const changeElement = element.querySelector('.index-change');
                
                if (priceElement) {
                    priceElement.textContent = this.formatPrice(quote.ltp);
                }
                
                if (changeElement) {
                    const change = quote.change || 0;
                    const changePercent = quote.changePercent || 0;
                    changeElement.textContent = `${this.formatChange(change)} (${this.formatPercent(changePercent)})`;
                    changeElement.className = `index-change ${change >= 0 ? 'positive' : 'negative'}`;
                }
            }
        }
    }

    updatePortfolioStats() {
        if (!this.portfolioData) return;

        const stats = this.portfolioData.dayTradingStats || {};
        
        // Update P&L
        const pnlElement = document.getElementById('total-pnl');
        if (pnlElement) {
            const pnl = stats.totalPnL || 0;
            pnlElement.textContent = `₹${this.formatPrice(pnl)}`;
            pnlElement.className = `stat-value ${pnl >= 0 ? 'positive' : 'negative'}`;
        }
        
        // Update total trades
        const tradesElement = document.getElementById('total-trades');
        if (tradesElement) {
            tradesElement.textContent = stats.totalTrades || 0;
        }
        
        // Update win rate
        const winRateElement = document.getElementById('win-rate');
        if (winRateElement) {
            winRateElement.textContent = `${this.formatPercent(stats.winRate || 0)}`;
        }
        
        // Update active orders (this would come from order service)
        const activeOrdersElement = document.getElementById('active-orders');
        if (activeOrdersElement) {
            activeOrdersElement.textContent = '0'; // Placeholder
        }
    }

    updateTradingStats() {
        if (!this.tradingStats) return;

        // Update trading status in header
        if (this.app) {
            this.app.updateTradingStatus();
        }
    }

    handleMarketDataUpdate(data) {
        this.marketData.set(data.symbol, data);
        this.updateMarketIndex(data);
        
        // Log significant price movements
        if (Math.abs(data.changePercent) > 1) {
            this.logActivity('Price Alert', `${data.symbol} moved ${this.formatPercent(data.changePercent)}`);
        }
    }

    handlePortfolioUpdate(data) {
        this.portfolioData = data;
        this.updatePortfolioStats();
    }

    handleTradingEngineUpdate(data) {
        this.tradingStats = data;
        this.updateTradingStats();
    }

    updateMarketData(data) {
        this.handleMarketDataUpdate(data);
    }

    updateOrderData(data) {
        // Handle order updates
        this.logActivity('Order Update', `Order ${data.orderId} ${data.status}`);
    }

    updatePortfolioData(data) {
        this.handlePortfolioUpdate(data);
    }

    logActivity(type, message) {
        const now = new Date();
        const timeString = now.toTimeString().slice(0, 8);
        
        const activity = {
            time: timeString,
            type: type,
            message: message,
            timestamp: now
        };
        
        this.activityLog.unshift(activity);
        
        // Keep only last 50 activities
        if (this.activityLog.length > 50) {
            this.activityLog = this.activityLog.slice(0, 50);
        }
        
        this.updateActivityList();
    }

    updateActivityList() {
        const activityList = document.getElementById('activity-list');
        if (!activityList) return;

        // Clear existing activities
        activityList.innerHTML = '';
        
        // Add recent activities
        this.activityLog.slice(0, 10).forEach(activity => {
            const activityItem = document.createElement('div');
            activityItem.className = 'activity-item';
            activityItem.innerHTML = `
                <div class="activity-time">${activity.time}</div>
                <div class="activity-text">
                    <span class="activity-type">${activity.type}:</span>
                    ${activity.message}
                </div>
            `;
            activityList.appendChild(activityItem);
        });
        
        // If no activities, show default message
        if (this.activityLog.length === 0) {
            activityList.innerHTML = `
                <div class="activity-item">
                    <div class="activity-time">--:--:--</div>
                    <div class="activity-text">No recent activity</div>
                </div>
            `;
        }
    }

    startAutoRefresh() {
        // Refresh dashboard data every 30 seconds
        this.refreshInterval = setInterval(async () => {
            await this.refreshDashboardData();
        }, 30000);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    async refreshDashboardData() {
        try {
            // Refresh portfolio data
            await this.loadPortfolioData();
            
            // Refresh trading stats
            await this.loadTradingStats();
        } catch (error) {
            console.error('Failed to refresh dashboard data:', error);
        }
    }

    onTabActivated() {
        // Called when dashboard tab is activated
        this.refreshDashboardData();
    }

    // Utility methods
    formatPrice(price) {
        return parseFloat(price || 0).toFixed(2);
    }

    formatChange(change) {
        const formatted = parseFloat(change || 0).toFixed(2);
        return change >= 0 ? `+${formatted}` : formatted;
    }

    formatPercent(percent) {
        const formatted = parseFloat(percent || 0).toFixed(2);
        return `${formatted}%`;
    }

    formatNumber(num) {
        return parseInt(num || 0).toLocaleString();
    }

    // Dashboard controls
    async startTrading() {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.startTradingEngine();
                if (result.success) {
                    this.logActivity('System', 'Trading engine started');
                } else {
                    this.logActivity('Error', `Failed to start trading: ${result.message}`);
                }
            }
        } catch (error) {
            console.error('Failed to start trading from dashboard:', error);
            this.logActivity('Error', 'Failed to start trading engine');
        }
    }

    async stopTrading() {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.stopTradingEngine();
                if (result.success) {
                    this.logActivity('System', 'Trading engine stopped');
                } else {
                    this.logActivity('Error', `Failed to stop trading: ${result.message}`);
                }
            }
        } catch (error) {
            console.error('Failed to stop trading from dashboard:', error);
            this.logActivity('Error', 'Failed to stop trading engine');
        }
    }

    async emergencyStop() {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.emergencyStop();
                if (result.success) {
                    this.logActivity('Emergency', 'Emergency stop executed');
                } else {
                    this.logActivity('Error', `Emergency stop failed: ${result.message}`);
                }
            }
        } catch (error) {
            console.error('Failed to execute emergency stop:', error);
            this.logActivity('Error', 'Emergency stop failed');
        }
    }

    // Market status
    updateMarketStatus() {
        const now = new Date();
        const day = now.getDay();
        const hour = now.getHours();
        const minute = now.getMinutes();
        const time = hour * 100 + minute;

        let status = 'Closed';
        let statusClass = 'closed';

        if (day >= 1 && day <= 5) { // Monday to Friday
            if (time >= 915 && time <= 1530) {
                status = 'Open';
                statusClass = 'open';
            } else if (time >= 900 && time < 915) {
                status = 'Pre-Market';
                statusClass = 'pre-market';
            } else if (time > 1530 && time <= 1600) {
                status = 'Post-Market';
                statusClass = 'post-market';
            }
        }

        // Update market status display if element exists
        const marketStatusElement = document.getElementById('market-status');
        if (marketStatusElement) {
            marketStatusElement.textContent = status;
            marketStatusElement.className = `market-status ${statusClass}`;
        }
    }

    // Performance metrics
    calculatePerformanceMetrics() {
        if (!this.portfolioData || !this.portfolioData.dayTradingStats) {
            return null;
        }

        const stats = this.portfolioData.dayTradingStats;
        
        return {
            totalReturn: stats.totalPnL,
            totalReturnPercent: stats.totalInvestment > 0 ? (stats.totalPnL / stats.totalInvestment) * 100 : 0,
            winRate: stats.winRate || 0,
            profitFactor: stats.losingTrades > 0 ? Math.abs(stats.totalPnL / (stats.losingTrades * -1)) : 0,
            averageWin: stats.winningTrades > 0 ? stats.totalPnL / stats.winningTrades : 0,
            averageLoss: stats.losingTrades > 0 ? stats.totalPnL / stats.losingTrades : 0
        };
    }

    // Export dashboard data
    exportDashboardData() {
        const data = {
            marketData: Object.fromEntries(this.marketData),
            portfolioData: this.portfolioData,
            tradingStats: this.tradingStats,
            activityLog: this.activityLog,
            performanceMetrics: this.calculatePerformanceMetrics(),
            exportedAt: new Date()
        };

        return data;
    }

    // Cleanup
    destroy() {
        this.stopAutoRefresh();
        
        // Remove event listeners
        document.removeEventListener('marketDataUpdate', this.handleMarketDataUpdate);
        document.removeEventListener('portfolioUpdate', this.handlePortfolioUpdate);
        document.removeEventListener('tradingEngineUpdate', this.handleTradingEngineUpdate);
    }
}

// Export for use in main app
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Dashboard;
}
