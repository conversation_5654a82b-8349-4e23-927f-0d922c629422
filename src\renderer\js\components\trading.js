/**
 * Trading Component
 * Handles option chain display, order placement, and trading interface
 */
class Trading {
    constructor(app) {
        this.app = app;
        this.currentSymbol = 'NIFTY';
        this.currentExpiry = null;
        this.optionChainData = null;
        this.selectedOption = null;
        this.refreshInterval = null;
        this.priceUpdateCallbacks = new Map();
        this.isInitialized = false;

        this.init();
    }

    async init() {
        try {
            // Initialize services
            await this.initializeServices();

            this.createTradingInterface();
            this.setupEventListeners();
            await this.loadDefaultData();

            this.isInitialized = true;
            console.log('Trading: Component initialized successfully');
        } catch (error) {
            console.error('Trading: Failed to initialize:', error);
        }
    }

    async initializeServices() {
        // Initialize script master service
        if (window.scriptMasterService) {
            await window.scriptMasterService.initialize();
        }

        // Initialize market data service
        if (window.marketDataService) {
            await window.marketDataService.initialize();
        }
    }

    createTradingInterface() {
        this.createOptionChainTable();
        this.createOrderForm();
        this.createScheduledOrdersPanel();
    }

    createOptionChainTable() {
        const container = document.getElementById('option-chain-container');
        if (!container) {
            console.error('Trading: option-chain-container not found');
            return;
        }

        console.log('Trading: Creating option chain table');
        container.innerHTML = `
            <div class="option-chain-header">
                <div class="symbol-info">
                    <div class="current-price" id="current-price">
                        <span class="price-label">Current Price:</span>
                        <span class="price-value" id="current-price-value">--</span>
                        <span class="price-change" id="current-price-change">--</span>
                    </div>
                    <div class="expiry-selector">
                        <label for="expiry-select">Expiry:</label>
                        <select id="expiry-select">
                            <option value="">Loading...</option>
                        </select>
                    </div>
                </div>
                <div class="chain-controls">
                    <button class="btn btn-sm" id="refresh-chain">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                    <button class="btn btn-sm" id="auto-refresh-toggle">
                        <i class="fas fa-play"></i>
                        Auto Refresh
                    </button>
                </div>
            </div>

            <div class="option-chain-table-container">
                <table class="option-chain-table" id="option-chain-table">
                    <thead>
                        <tr>
                            <th colspan="8" class="call-header">CALL OPTIONS</th>
                            <th class="strike-header">STRIKE</th>
                            <th colspan="8" class="put-header">PUT OPTIONS</th>
                        </tr>
                        <tr class="sub-header">
                            <th>OI</th>
                            <th>Volume</th>
                            <th>IV</th>
                            <th>LTP</th>
                            <th>Change</th>
                            <th>Bid</th>
                            <th>Ask</th>
                            <th>Action</th>
                            <th class="strike-price">Price</th>
                            <th>Action</th>
                            <th>Bid</th>
                            <th>Ask</th>
                            <th>Change</th>
                            <th>LTP</th>
                            <th>IV</th>
                            <th>Volume</th>
                            <th>OI</th>
                        </tr>
                    </thead>
                    <tbody id="option-chain-body">
                        <tr>
                            <td colspan="17" class="loading-row">
                                <div class="loading-spinner"></div>
                                Loading option chain...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }

    createOrderForm() {
        const container = document.querySelector('.order-form-card .card-body');
        if (!container) return;

        container.innerHTML = `
            <form id="order-form" class="order-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="selected-option">Selected Option</label>
                        <input type="text" id="selected-option" readonly placeholder="Select an option from the chain">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="order-type">Order Type</label>
                        <select id="order-type" name="orderType">
                            <option value="BUY">Buy</option>
                            <option value="SELL">Sell</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="quantity">Quantity (Lots)</label>
                        <input type="number" id="quantity" name="quantity" min="1" max="100" value="1">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="price-type">Price Type</label>
                        <select id="price-type" name="priceType">
                            <option value="MKT">Market</option>
                            <option value="LMT">Limit</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="limit-price">Limit Price</label>
                        <input type="number" id="limit-price" name="limitPrice" step="0.05" disabled>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="stop-loss">Stop Loss (%)</label>
                        <input type="number" id="stop-loss" name="stopLoss" step="0.1" value="2" min="0" max="50">
                    </div>
                    <div class="form-group">
                        <label for="target">Target (%)</label>
                        <input type="number" id="target" name="target" step="0.1" value="5" min="0" max="100">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="schedule-order" name="scheduleOrder">
                            <span class="checkmark"></span>
                            Schedule Order
                        </label>
                    </div>
                </div>
                
                <div class="schedule-section hidden" id="schedule-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="schedule-time">Execution Time</label>
                            <input type="time" id="schedule-time" name="scheduleTime">
                        </div>
                        <div class="form-group">
                            <label for="schedule-date">Execution Date</label>
                            <input type="date" id="schedule-date" name="scheduleDate">
                        </div>
                    </div>
                </div>
                
                <div class="order-summary" id="order-summary">
                    <div class="summary-row">
                        <span>Estimated Premium:</span>
                        <span id="estimated-premium">₹0.00</span>
                    </div>
                    <div class="summary-row">
                        <span>Total Investment:</span>
                        <span id="total-investment">₹0.00</span>
                    </div>
                    <div class="summary-row">
                        <span>Stop Loss at:</span>
                        <span id="stop-loss-price">₹0.00</span>
                    </div>
                    <div class="summary-row">
                        <span>Target at:</span>
                        <span id="target-price">₹0.00</span>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" id="clear-form">
                        <i class="fas fa-times"></i>
                        Clear
                    </button>
                    <button type="submit" class="btn btn-primary" id="place-order">
                        <i class="fas fa-shopping-cart"></i>
                        Place Order
                    </button>
                </div>
            </form>
        `;
    }

    createScheduledOrdersPanel() {
        const tradingLayout = document.querySelector('.trading-layout');
        if (!tradingLayout) return;

        const scheduledOrdersCard = document.createElement('div');
        scheduledOrdersCard.className = 'card scheduled-orders-card';
        scheduledOrdersCard.innerHTML = `
            <div class="card-header">
                <h3>Scheduled Orders</h3>
                <button class="btn btn-sm" id="refresh-scheduled">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
            </div>
            <div class="card-body">
                <div class="scheduled-orders-list" id="scheduled-orders-list">
                    <div class="empty-state">
                        <i class="fas fa-calendar-alt"></i>
                        <p>No scheduled orders</p>
                    </div>
                </div>
            </div>
        `;
        
        tradingLayout.appendChild(scheduledOrdersCard);
    }

    setupEventListeners() {
        // Symbol selector (from the HTML header)
        const symbolSelect = document.getElementById('symbol-select');
        if (symbolSelect) {
            symbolSelect.addEventListener('change', async (e) => {
                this.currentSymbol = e.target.value;
                console.log('Trading: Symbol changed to:', this.currentSymbol);

                // Update current price
                await this.updateCurrentPrice();

                // Reload expiries for new symbol
                await this.loadExpiryDates();

                // Reload option chain
                await this.loadOptionChain();

                // Restart price updates for new symbol
                this.startPriceUpdates();
            });
            console.log('Trading: Symbol selector event listener attached');
        } else {
            console.error('Trading: Symbol selector not found');
        }

        // Expiry selector
        document.getElementById('expiry-select')?.addEventListener('change', async (e) => {
            this.currentExpiry = e.target.value;
            console.log('Trading: Expiry changed to:', this.currentExpiry);
            await this.loadOptionChain();
        });

        // Refresh button
        document.getElementById('refresh-chain')?.addEventListener('click', async () => {
            console.log('Trading: Manual refresh triggered');
            await this.loadOptionChain();
        });

        // Auto refresh toggle
        document.getElementById('auto-refresh-toggle')?.addEventListener('click', (e) => {
            const button = e.target.closest('button');
            const icon = button.querySelector('i');

            if (this.refreshInterval) {
                // Stop auto refresh
                clearInterval(this.refreshInterval);
                this.refreshInterval = null;
                icon.className = 'fas fa-play';
                button.innerHTML = '<i class="fas fa-play"></i> Auto Refresh';
                console.log('Trading: Auto refresh stopped');
            } else {
                // Start auto refresh (every 5 seconds)
                this.refreshInterval = setInterval(() => {
                    this.loadOptionChain();
                }, 5000);
                icon.className = 'fas fa-pause';
                button.innerHTML = '<i class="fas fa-pause"></i> Auto Refresh';
                console.log('Trading: Auto refresh started');
            }
        });

        // Refresh controls
        document.getElementById('refresh-chain')?.addEventListener('click', () => {
            this.loadOptionChain();
        });

        document.getElementById('auto-refresh-toggle')?.addEventListener('click', (e) => {
            this.toggleAutoRefresh(e.target);
        });

        // Order form
        document.getElementById('price-type')?.addEventListener('change', (e) => {
            const limitPriceInput = document.getElementById('limit-price');
            limitPriceInput.disabled = e.target.value === 'MKT';
        });

        document.getElementById('schedule-order')?.addEventListener('change', (e) => {
            const scheduleSection = document.getElementById('schedule-section');
            if (e.target.checked) {
                scheduleSection.classList.remove('hidden');
                this.setDefaultScheduleTime();
            } else {
                scheduleSection.classList.add('hidden');
            }
        });

        // Form inputs for real-time calculation
        ['quantity', 'limit-price', 'stop-loss', 'target'].forEach(id => {
            document.getElementById(id)?.addEventListener('input', () => {
                this.updateOrderSummary();
            });
        });

        document.getElementById('order-form')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.placeOrder();
        });

        document.getElementById('clear-form')?.addEventListener('click', () => {
            this.clearOrderForm();
        });

        // Market data updates
        document.addEventListener('marketDataUpdate', (e) => {
            this.handleMarketDataUpdate(e.detail);
        });
    }

    async loadDefaultData() {
        try {
            console.log('Trading: Loading default data...');

            // Set default schedule date to today
            const today = new Date().toISOString().split('T')[0];
            const scheduleDateInput = document.getElementById('schedule-date');
            if (scheduleDateInput) {
                scheduleDateInput.value = today;
            }

            // Set default symbol
            const symbolSelect = document.getElementById('symbol-select');
            if (symbolSelect) {
                symbolSelect.value = this.currentSymbol;
                console.log('Trading: Default symbol set to:', this.currentSymbol);
            }

            // Load current price and update display
            console.log('Trading: Loading current price...');
            await this.updateCurrentPrice();

            // Load expiry dates
            console.log('Trading: Loading expiry dates...');
            await this.loadExpiryDates();

            // Load initial option chain
            console.log('Trading: Loading option chain...');
            await this.loadOptionChain();

            // Start real-time price updates
            console.log('Trading: Starting price updates...');
            this.startPriceUpdates();

            console.log('Trading: Default data loaded successfully');
        } catch (error) {
            console.error('Trading: Failed to load default data:', error);
            this.showError('Failed to load trading data', error.message);
        }
    }

    showError(title, message) {
        const container = document.getElementById('option-chain-container');
        if (container) {
            container.innerHTML = `
                <div class="error-container">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="error-content">
                        <h4>${title}</h4>
                        <p>${message}</p>
                        <button class="btn btn-primary" onclick="window.trading.loadDefaultData()">
                            <i class="fas fa-retry"></i>
                            Retry
                        </button>
                    </div>
                </div>
            `;
        }

        // Also show notification
        if (window.notificationService) {
            window.notificationService.error(title, message);
        }
    }

    async updateCurrentPrice() {
        try {
            let priceData;
            if (this.currentSymbol === 'NIFTY') {
                priceData = await window.marketDataService.getNiftyPrice();
            } else if (this.currentSymbol === 'BANKNIFTY') {
                priceData = await window.marketDataService.getBankNiftyPrice();
            }

            if (priceData) {
                this.displayCurrentPrice(priceData);
            }
        } catch (error) {
            console.error('Trading: Failed to update current price:', error);
        }
    }

    displayCurrentPrice(priceData) {
        const priceValueEl = document.getElementById('current-price-value');
        const priceChangeEl = document.getElementById('current-price-change');

        if (priceValueEl) {
            priceValueEl.textContent = priceData.ltp.toFixed(2);
        }

        if (priceChangeEl) {
            const change = priceData.change || 0;
            const changePercent = priceData.changePercent || 0;
            priceChangeEl.textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)} (${changePercent.toFixed(2)}%)`;
            priceChangeEl.className = change >= 0 ? 'price-change positive' : 'price-change negative';
        }
    }

    startPriceUpdates() {
        // Subscribe to real-time price updates
        if (window.marketDataService) {
            const callback = (data) => {
                this.displayCurrentPrice(data);
            };

            if (this.currentSymbol === 'NIFTY') {
                window.marketDataService.onPriceUpdate('NIFTY', callback);
            } else if (this.currentSymbol === 'BANKNIFTY') {
                window.marketDataService.onPriceUpdate('BANKNIFTY', callback);
            }
        }
    }

    async loadExpiryDates() {
        try {
            if (window.scriptMasterService) {
                // Get nearest two weekly expiries
                const nearestExpiries = window.scriptMasterService.getNearestTwoExpiries(this.currentSymbol);
                console.log('Trading: Nearest two expiries for', this.currentSymbol, ':', nearestExpiries);
                this.populateExpirySelector(nearestExpiries);
            }
        } catch (error) {
            console.error('Trading: Failed to load expiry dates:', error);
        }
    }

    populateExpirySelector(expiries) {
        const select = document.getElementById('expiry-select');
        if (!select) return;

        select.innerHTML = '';

        if (Array.isArray(expiries) && expiries.length > 0) {
            expiries.forEach(expiry => {
                const option = document.createElement('option');
                option.value = expiry;
                option.textContent = this.formatExpiryDisplay(expiry);
                select.appendChild(option);
            });

            // Select the first expiry by default
            this.currentExpiry = expiries[0];
            select.value = this.currentExpiry;
        } else {
            const option = document.createElement('option');
            option.value = '';
            option.textContent = 'No expiries available';
            select.appendChild(option);
        }
    }

    formatExpiryDisplay(expiry) {
        // Convert expiry format like "25JAN25" to readable format
        try {
            const day = expiry.substring(0, 2);
            const month = expiry.substring(2, 5);
            const year = '20' + expiry.substring(5, 7);
            return `${day} ${month} ${year}`;
        } catch (error) {
            return expiry;
        }
    }

    async loadOptionChain() {
        try {
            const tbody = document.getElementById('option-chain-body');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="17" class="loading-row">
                            <div class="loading-spinner"></div>
                            Loading option chain...
                        </td>
                    </tr>
                `;
            }

            if (window.marketDataService) {
                console.log('Trading: Loading option chain for', this.currentSymbol, 'expiry:', this.currentExpiry);

                const optionChainData = await window.marketDataService.getOptionChainData(
                    this.currentSymbol,
                    this.currentExpiry
                );

                if (optionChainData) {
                    this.optionChainData = optionChainData;
                    this.renderOptionChain();
                    console.log('Trading: Option chain loaded successfully');
                } else {
                    this.showError('Failed to load option chain', 'No data received');
                }
            }
        } catch (error) {
            console.error('Trading: Failed to load option chain:', error);
            this.showError('Failed to load option chain', error.message);
        }
    }

    renderOptionChain() {
        const tbody = document.getElementById('option-chain-body');
        if (!tbody || !this.optionChainData) return;

        tbody.innerHTML = '';

        this.optionChainData.optionChain.forEach(optionData => {
            const row = this.createOptionChainRow(optionData);
            tbody.appendChild(row);
        });

        // Subscribe to real-time updates for all option tokens
        this.subscribeToOptionUpdates();
    }

    subscribeToOptionUpdates() {
        if (!this.optionChainData || !window.marketDataService) {
            return;
        }

        // Collect all option tokens
        const tokens = [];
        this.optionChainData.optionChain.forEach(optionData => {
            if (optionData.call && optionData.call.token) {
                tokens.push(optionData.call.token);
            }
            if (optionData.put && optionData.put.token) {
                tokens.push(optionData.put.token);
            }
        });

        // Subscribe to real-time updates
        if (tokens.length > 0) {
            window.marketDataService.subscribe(tokens).then(() => {
                console.log('Trading: Subscribed to', tokens.length, 'option tokens');

                // Set up price update callbacks for each token
                tokens.forEach(token => {
                    const callback = (priceData) => {
                        this.updateOptionPrice(token, priceData);
                    };

                    window.marketDataService.onPriceUpdate(token, callback);
                    this.priceUpdateCallbacks.set(token, callback);
                });

            }).catch(error => {
                console.error('Trading: Failed to subscribe to option updates:', error);
            });
        }
    }

    updateOptionPrice(token, priceData) {
        // Find and update the corresponding cell in the option chain table
        const ltpCells = document.querySelectorAll(`[data-token="${token}"]`);

        ltpCells.forEach(cell => {
            if (cell.classList.contains('call-ltp') || cell.classList.contains('put-ltp')) {
                // Update LTP
                const oldPrice = parseFloat(cell.textContent);
                const newPrice = priceData.ltp || priceData.lastPrice || 0;

                cell.textContent = newPrice.toFixed(2);

                // Add price change animation
                if (oldPrice !== newPrice) {
                    cell.classList.remove('price-up', 'price-down');
                    if (newPrice > oldPrice) {
                        cell.classList.add('price-up');
                    } else if (newPrice < oldPrice) {
                        cell.classList.add('price-down');
                    }

                    // Remove animation class after animation completes
                    setTimeout(() => {
                        cell.classList.remove('price-up', 'price-down');
                    }, 1000);
                }
            }

            // Update change cell if it's in the same row
            const row = cell.closest('tr');
            if (row) {
                const changeCells = row.querySelectorAll('.call-change, .put-change');
                changeCells.forEach(changeCell => {
                    const change = priceData.change || 0;
                    changeCell.textContent = change.toFixed(2);
                    changeCell.className = `change-cell ${changeCell.classList.contains('call-change') ? 'call-change' : 'put-change'} ${change >= 0 ? 'positive' : 'negative'}`;
                });
            }
        });
    }

    createOptionChainRow(optionData) {
        const row = document.createElement('tr');
        row.className = 'option-chain-row';

        const call = optionData.call;
        const put = optionData.put;
        const strike = optionData.strike;

        row.innerHTML = `
            <!-- Call Options -->
            <td class="oi-cell">${call ? (call.oi || '-') : '-'}</td>
            <td class="volume-cell">${call ? (call.volume || '-') : '-'}</td>
            <td class="iv-cell">-</td>
            <td class="ltp-cell call-ltp" data-token="${call ? call.token : ''}">${call ? call.ltp.toFixed(2) : '-'}</td>
            <td class="change-cell call-change ${call && call.change >= 0 ? 'positive' : 'negative'}">${call ? call.change.toFixed(2) : '-'}</td>
            <td class="bid-cell">${call ? (call.bid || '-') : '-'}</td>
            <td class="ask-cell">${call ? (call.ask || '-') : '-'}</td>
            <td class="action-cell">
                ${call ? `<button class="btn btn-sm btn-buy" onclick="trading.selectOption('${call.symbol}', 'CALL', ${strike})">Buy</button>` : '-'}
            </td>

            <!-- Strike Price -->
            <td class="strike-cell">${strike}</td>

            <!-- Put Options -->
            <td class="action-cell">
                ${put ? `<button class="btn btn-sm btn-sell" onclick="trading.selectOption('${put.symbol}', 'PUT', ${strike})">Buy</button>` : '-'}
            </td>
            <td class="bid-cell">${put ? (put.bid || '-') : '-'}</td>
            <td class="ask-cell">${put ? (put.ask || '-') : '-'}</td>
            <td class="change-cell put-change ${put && put.change >= 0 ? 'positive' : 'negative'}">${put ? put.change.toFixed(2) : '-'}</td>
            <td class="ltp-cell put-ltp" data-token="${put ? put.token : ''}">${put ? put.ltp.toFixed(2) : '-'}</td>
            <td class="iv-cell">-</td>
            <td class="volume-cell">${put ? (put.volume || '-') : '-'}</td>
            <td class="oi-cell">${put ? (put.oi || '-') : '-'}</td>
        `;

        return row;
    }

    selectOption(symbol, type, strike) {
        this.selectedOption = { symbol, type, strike };
        console.log('Trading: Selected option:', this.selectedOption);

        // Update order form with selected option
        this.updateOrderFormWithSelection();

        // Highlight selected row
        this.highlightSelectedOption();
    }

    updateOrderFormWithSelection() {
        if (!this.selectedOption) return;

        const symbolInput = document.getElementById('order-symbol');
        if (symbolInput) {
            symbolInput.value = this.selectedOption.symbol;
        }

        // Auto-fill some order details
        const quantityInput = document.getElementById('quantity');
        if (quantityInput && !quantityInput.value) {
            quantityInput.value = '1';
        }
    }

    highlightSelectedOption() {
        // Remove previous highlights
        document.querySelectorAll('.option-chain-row.selected').forEach(row => {
            row.classList.remove('selected');
        });

        // Add highlight to current selection
        if (this.selectedOption) {
            const rows = document.querySelectorAll('.option-chain-row');
            rows.forEach(row => {
                const strikeCell = row.querySelector('.strike-cell');
                if (strikeCell && parseInt(strikeCell.textContent) === this.selectedOption.strike) {
                    row.classList.add('selected');
                }
            });
        }
    }

    createOptionChainRow(strike) {
        const row = document.createElement('tr');
        const isATM = strike.strike === this.optionChainData.atmStrike;
        
        if (isATM) {
            row.classList.add('atm-strike');
        }

        const call = strike.call || {};
        const put = strike.put || {};

        row.innerHTML = `
            <!-- Call Options -->
            <td class="oi-cell">${this.formatNumber(call.oi || 0)}</td>
            <td class="volume-cell">${this.formatNumber(call.volume || 0)}</td>
            <td class="iv-cell">${this.formatPercent(call.iv || 0)}</td>
            <td class="ltp-cell ${this.getPriceChangeClass(call.change)}">${this.formatPrice(call.ltp || 0)}</td>
            <td class="change-cell ${this.getPriceChangeClass(call.change)}">${this.formatChange(call.change || 0)}</td>
            <td class="bid-cell">${this.formatPrice(call.bid || 0)}</td>
            <td class="ask-cell">${this.formatPrice(call.ask || 0)}</td>
            <td class="action-cell">
                <button class="btn btn-xs btn-success" onclick="tradingComponent.selectOption('${strike.strike}', 'CE', 'BUY')">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="btn btn-xs btn-danger" onclick="tradingComponent.selectOption('${strike.strike}', 'CE', 'SELL')">
                    <i class="fas fa-minus"></i>
                </button>
            </td>
            
            <!-- Strike Price -->
            <td class="strike-price ${isATM ? 'atm' : ''}">${strike.strike}</td>
            
            <!-- Put Options -->
            <td class="action-cell">
                <button class="btn btn-xs btn-success" onclick="tradingComponent.selectOption('${strike.strike}', 'PE', 'BUY')">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="btn btn-xs btn-danger" onclick="tradingComponent.selectOption('${strike.strike}', 'PE', 'SELL')">
                    <i class="fas fa-minus"></i>
                </button>
            </td>
            <td class="bid-cell">${this.formatPrice(put.bid || 0)}</td>
            <td class="ask-cell">${this.formatPrice(put.ask || 0)}</td>
            <td class="change-cell ${this.getPriceChangeClass(put.change)}">${this.formatChange(put.change || 0)}</td>
            <td class="ltp-cell ${this.getPriceChangeClass(put.change)}">${this.formatPrice(put.ltp || 0)}</td>
            <td class="iv-cell">${this.formatPercent(put.iv || 0)}</td>
            <td class="volume-cell">${this.formatNumber(put.volume || 0)}</td>
            <td class="oi-cell">${this.formatNumber(put.oi || 0)}</td>
        `;

        return row;
    }

    selectOption(strike, optionType, orderType) {
        const strikeData = this.optionChainData.strikes.find(s => s.strike === parseInt(strike));
        if (!strikeData) return;

        const option = optionType === 'CE' ? strikeData.call : strikeData.put;
        if (!option) return;

        this.selectedOption = {
            strike: parseInt(strike),
            type: optionType,
            symbol: option.symbol,
            ltp: option.ltp,
            bid: option.bid,
            ask: option.ask
        };

        // Update form
        document.getElementById('selected-option').value = 
            `${this.currentSymbol} ${strike} ${optionType} @ ₹${option.ltp}`;
        document.getElementById('order-type').value = orderType;
        document.getElementById('limit-price').value = orderType === 'BUY' ? option.ask : option.bid;

        this.updateOrderSummary();
    }

    updateCurrentPrice() {
        if (!this.optionChainData) return;

        const priceElement = document.querySelector('#current-price .price-value');
        const changeElement = document.querySelector('#current-price .price-change');

        if (priceElement) {
            priceElement.textContent = `₹${this.formatPrice(this.optionChainData.underlyingPrice)}`;
        }

        // This would be updated with real-time data
        if (changeElement) {
            changeElement.textContent = '+0.00 (0.00%)';
            changeElement.className = 'price-change positive';
        }
    }

    updateOrderSummary() {
        if (!this.selectedOption) return;

        const quantity = parseInt(document.getElementById('quantity').value) || 1;
        const priceType = document.getElementById('price-type').value;
        const limitPrice = parseFloat(document.getElementById('limit-price').value) || 0;
        const stopLoss = parseFloat(document.getElementById('stop-loss').value) || 0;
        const target = parseFloat(document.getElementById('target').value) || 0;

        const price = priceType === 'MKT' ? this.selectedOption.ltp : limitPrice;
        const lotSize = this.getLotSize(this.currentSymbol);
        const totalQuantity = quantity * lotSize;
        const totalInvestment = price * totalQuantity;

        const stopLossPrice = price * (1 - stopLoss / 100);
        const targetPrice = price * (1 + target / 100);

        document.getElementById('estimated-premium').textContent = `₹${this.formatPrice(price)}`;
        document.getElementById('total-investment').textContent = `₹${this.formatPrice(totalInvestment)}`;
        document.getElementById('stop-loss-price').textContent = `₹${this.formatPrice(stopLossPrice)}`;
        document.getElementById('target-price').textContent = `₹${this.formatPrice(targetPrice)}`;
    }

    async placeOrder() {
        try {
            if (!this.selectedOption) {
                this.showError('No option selected', 'Please select an option from the chain first.');
                return;
            }

            const formData = new FormData(document.getElementById('order-form'));
            const orderData = {
                symbol: this.selectedOption.symbol,
                strike: this.selectedOption.strike,
                optionType: this.selectedOption.type,
                orderType: formData.get('orderType'),
                quantity: parseInt(formData.get('quantity')),
                priceType: formData.get('priceType'),
                limitPrice: parseFloat(formData.get('limitPrice')) || 0,
                stopLoss: parseFloat(formData.get('stopLoss')) || 0,
                target: parseFloat(formData.get('target')) || 0,
                scheduleOrder: formData.get('scheduleOrder') === 'on',
                scheduleTime: formData.get('scheduleTime'),
                scheduleDate: formData.get('scheduleDate')
            };

            const placeOrderBtn = document.getElementById('place-order');
            placeOrderBtn.disabled = true;
            placeOrderBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Placing Order...';

            if (window.electronAPI) {
                const result = await window.electronAPI.placeOrder(orderData);
                
                if (result.success) {
                    this.showSuccess('Order placed successfully', `Order ID: ${result.orderId}`);
                    this.clearOrderForm();
                } else {
                    this.showError('Failed to place order', result.message);
                }
            }
        } catch (error) {
            console.error('Failed to place order:', error);
            this.showError('Failed to place order', error.message);
        } finally {
            const placeOrderBtn = document.getElementById('place-order');
            placeOrderBtn.disabled = false;
            placeOrderBtn.innerHTML = '<i class="fas fa-shopping-cart"></i> Place Order';
        }
    }

    clearOrderForm() {
        document.getElementById('order-form').reset();
        document.getElementById('selected-option').value = '';
        document.getElementById('schedule-section').classList.add('hidden');
        this.selectedOption = null;
        this.updateOrderSummary();
    }

    setDefaultScheduleTime() {
        const now = new Date();
        const timeString = now.toTimeString().slice(0, 5);
        document.getElementById('schedule-time').value = timeString;
    }

    toggleAutoRefresh(button) {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
            button.innerHTML = '<i class="fas fa-play"></i> Auto Refresh';
            button.classList.remove('btn-warning');
        } else {
            this.refreshInterval = setInterval(() => {
                this.loadOptionChain();
            }, 30000); // Refresh every 30 seconds
            button.innerHTML = '<i class="fas fa-pause"></i> Stop Auto';
            button.classList.add('btn-warning');
        }
    }

    handleMarketDataUpdate(data) {
        if (data.symbol === this.currentSymbol) {
            this.updateCurrentPrice();
        }
        
        // Update option chain if data matches any option
        if (this.optionChainData) {
            // This would update individual option prices in real-time
            this.updateOptionPrices(data);
        }
    }

    updateOptionPrices(data) {
        // Update option prices in the table
        // This would be implemented based on the specific data format
    }

    onTabActivated() {
        // Called when trading tab is activated
        if (!this.optionChainData) {
            this.loadOptionChain();
        }
    }

    // Utility methods
    formatPrice(price) {
        return parseFloat(price).toFixed(2);
    }

    formatNumber(num) {
        return parseInt(num).toLocaleString();
    }

    formatPercent(percent) {
        return `${parseFloat(percent).toFixed(2)}%`;
    }

    formatChange(change) {
        const formatted = parseFloat(change).toFixed(2);
        return change >= 0 ? `+${formatted}` : formatted;
    }

    getPriceChangeClass(change) {
        if (change > 0) return 'positive';
        if (change < 0) return 'negative';
        return '';
    }

    getLotSize(symbol) {
        const lotSizes = {
            'NIFTY': 50,
            'BANKNIFTY': 25,
            'FINNIFTY': 40,
            'MIDCPNIFTY': 75
        };
        return lotSizes[symbol] || 50;
    }

    showSuccess(title, message) {
        if (this.app) {
            this.app.showNotification(title, message, 'success');
        }
    }

    showError(title, message) {
        if (this.app) {
            this.app.showNotification(title, message, 'error');
        }
    }
}

/**
 * Clean up subscriptions and callbacks
 */
cleanup() {
    // Clear price update callbacks
    this.priceUpdateCallbacks.forEach((callback, token) => {
        if (window.marketDataService) {
            // Remove callback (implementation depends on market data service)
            console.log('Trading: Cleaning up callback for token:', token);
        }
    });
    this.priceUpdateCallbacks.clear();

    // Clear refresh interval
    if (this.refreshInterval) {
        clearInterval(this.refreshInterval);
        this.refreshInterval = null;
    }
}

// Make it globally accessible for button clicks
window.trading = null;

// Export for use in main app
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Trading;
}
