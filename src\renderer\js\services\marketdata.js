/**
 * Market Data Service
 * Handles real-time market data fetching and management
 */
class MarketDataService {
    constructor() {
        this.currentPrices = new Map();
        this.subscriptions = new Set();
        this.updateCallbacks = new Map();
        this.isInitialized = false;
    }

    /**
     * Initialize market data service
     */
    async initialize() {
        if (this.isInitialized) {
            return true;
        }

        try {
            // Set up market data update listener
            if (window.electronAPI && window.electronAPI.onMarketDataUpdate) {
                window.electronAPI.onMarketDataUpdate((event, data) => {
                    this.handleMarketDataUpdate(data);
                });
            }

            this.isInitialized = true;
            console.log('MarketData: Service initialized');
            return true;
        } catch (error) {
            console.error('MarketData: Failed to initialize:', error);
            return false;
        }
    }

    /**
     * Get comprehensive market data for an index (new method from LLM solution)
     */
    async getMarketData(indexName) {
        console.log(`[MarketDataService] Requesting data for ${indexName}`);
        if (!window.electronAPI) {
            console.error('Electron API not exposed on window object!');
            return { success: false, error: 'IPC channel not available.' };
        }
        try {
            const result = await window.electronAPI.getMarketData({ indexName });
            console.log(`[MarketDataService] Received result:`, result);
            return result;
        } catch (error) {
            console.error('Error invoking get-market-data:', error);
            return { success: false, error: 'Failed to communicate with the main process.' };
        }
    }

    /**
     * Get current price for Nifty 50
     */
    async getNiftyPrice() {
        try {
            console.log('MarketData: Getting Nifty 50 price...');

            // Try different possible neo symbols for Nifty 50
            const possibleSymbols = [
                'nse_cm|26000',     // Most likely Nifty 50 token
                'nse_cm|NIFTY 50',  // Alternative format
                'nse_cm|2885'       // Another possible token
            ];

            for (const neoSymbol of possibleSymbols) {
                try {
                    console.log(`MarketData: Trying symbol: ${neoSymbol}`);
                    const result = await window.electronAPI.getQuotes(neoSymbol, 'all');

                    if (result.success && result.data && result.data.length > 0) {
                        const quote = result.data[0];
                        console.log(`MarketData: Found data for ${neoSymbol}:`, quote.display_symbol);

                        const priceData = {
                            symbol: 'NIFTY',
                            token: quote.exchange_token,
                            ltp: parseFloat(quote.ltp),
                            change: parseFloat(quote.change),
                            changePercent: parseFloat(quote.per_change),
                            high: quote.ohlc ? parseFloat(quote.ohlc.high) : 0,
                            low: quote.ohlc ? parseFloat(quote.ohlc.low) : 0,
                            open: quote.ohlc ? parseFloat(quote.ohlc.open) : 0,
                            close: quote.ohlc ? parseFloat(quote.ohlc.close) : 0,
                            volume: parseInt(quote.last_volume) || 0
                        };

                        this.currentPrices.set('NIFTY', priceData);
                        console.log('MarketData: Nifty price updated:', priceData);
                        return priceData;
                    }
                } catch (symbolError) {
                    console.log(`MarketData: Symbol ${neoSymbol} failed:`, symbolError.message);
                    continue;
                }
            }

            throw new Error('No valid Nifty symbol found');
        } catch (error) {
            console.error('MarketData: Failed to get Nifty price:', error);
            throw error;
        }
    }

    /**
     * Get current price for Bank Nifty
     */
    async getBankNiftyPrice() {
        try {
            console.log('MarketData: Getting Bank Nifty price...');

            // Try different possible neo symbols for Bank Nifty
            const possibleSymbols = [
                'nse_cm|26009',      // Most likely Bank Nifty token
                'nse_cm|NIFTY BANK', // Alternative format
                'nse_cm|532174'      // Another possible token
            ];

            for (const neoSymbol of possibleSymbols) {
                try {
                    console.log(`MarketData: Trying symbol: ${neoSymbol}`);
                    const result = await window.electronAPI.getQuotes(neoSymbol, 'all');

                    if (result.success && result.data && result.data.length > 0) {
                        const quote = result.data[0];
                        console.log(`MarketData: Found data for ${neoSymbol}:`, quote.display_symbol);

                        const priceData = {
                            symbol: 'BANKNIFTY',
                            token: quote.exchange_token,
                            ltp: parseFloat(quote.ltp),
                            change: parseFloat(quote.change),
                            changePercent: parseFloat(quote.per_change),
                            high: quote.ohlc ? parseFloat(quote.ohlc.high) : 0,
                            low: quote.ohlc ? parseFloat(quote.ohlc.low) : 0,
                            open: quote.ohlc ? parseFloat(quote.ohlc.open) : 0,
                            close: quote.ohlc ? parseFloat(quote.ohlc.close) : 0,
                            volume: parseInt(quote.last_volume) || 0
                        };

                        this.currentPrices.set('BANKNIFTY', priceData);
                        console.log('MarketData: Bank Nifty price updated:', priceData);
                        return priceData;
                    }
                } catch (symbolError) {
                    console.log(`MarketData: Symbol ${neoSymbol} failed:`, symbolError.message);
                    continue;
                }
            }

            throw new Error('No valid Bank Nifty symbol found');
        } catch (error) {
            console.error('MarketData: Failed to get Bank Nifty price:', error);
            throw error;
        }
    }

    /**
     * Get prices for multiple tokens using neo symbols
     */
    async getPrices(neoSymbols) {
        try {
            if (!Array.isArray(neoSymbols)) {
                neoSymbols = [neoSymbols];
            }

            // Join symbols with comma for the API
            const symbolsParam = neoSymbols.join(',');
            const result = await window.electronAPI.getQuotes(symbolsParam, 'all');

            if (result.success && result.data) {
                // Transform the data to our format
                return result.data.map(quote => ({
                    token: quote.exchange_token,
                    symbol: quote.display_symbol,
                    ltp: parseFloat(quote.ltp),
                    change: parseFloat(quote.change),
                    changePercent: parseFloat(quote.per_change),
                    volume: parseInt(quote.last_volume) || 0,
                    oi: parseInt(quote.open_int) || 0,
                    bid: quote.depth?.buy?.[0]?.price ? parseFloat(quote.depth.buy[0].price) : 0,
                    ask: quote.depth?.sell?.[0]?.price ? parseFloat(quote.depth.sell[0].price) : 0,
                    high: quote.ohlc ? parseFloat(quote.ohlc.high) : 0,
                    low: quote.ohlc ? parseFloat(quote.ohlc.low) : 0,
                    open: quote.ohlc ? parseFloat(quote.ohlc.open) : 0,
                    close: quote.ohlc ? parseFloat(quote.ohlc.close) : 0
                }));
            }
            throw new Error('Failed to get market data');
        } catch (error) {
            console.error('MarketData: Failed to get prices:', error);
            throw error;
        }
    }

    /**
     * Subscribe to real-time updates for symbols
     */
    async subscribe(symbols) {
        try {
            if (!Array.isArray(symbols)) {
                symbols = [symbols];
            }

            // Add to subscriptions
            symbols.forEach(symbol => this.subscriptions.add(symbol));

            // Subscribe via WebSocket
            const result = await window.electronAPI.subscribeToMarketData(symbols);
            if (result.success) {
                console.log('MarketData: Subscribed to symbols:', symbols);
                return true;
            }
            
            throw new Error('Failed to subscribe to market data');
        } catch (error) {
            console.error('MarketData: Failed to subscribe:', error);
            throw error;
        }
    }

    /**
     * Unsubscribe from real-time updates
     */
    async unsubscribe(symbols) {
        try {
            if (!Array.isArray(symbols)) {
                symbols = [symbols];
            }

            // Remove from subscriptions
            symbols.forEach(symbol => this.subscriptions.delete(symbol));

            // Unsubscribe via WebSocket
            const result = await window.electronAPI.unsubscribeFromMarketData(symbols);
            if (result.success) {
                console.log('MarketData: Unsubscribed from symbols:', symbols);
                return true;
            }
            
            throw new Error('Failed to unsubscribe from market data');
        } catch (error) {
            console.error('MarketData: Failed to unsubscribe:', error);
            throw error;
        }
    }

    /**
     * Register callback for price updates
     */
    onPriceUpdate(symbol, callback) {
        if (!this.updateCallbacks.has(symbol)) {
            this.updateCallbacks.set(symbol, new Set());
        }
        this.updateCallbacks.get(symbol).add(callback);

        // Return unsubscribe function
        return () => {
            const callbacks = this.updateCallbacks.get(symbol);
            if (callbacks) {
                callbacks.delete(callback);
                if (callbacks.size === 0) {
                    this.updateCallbacks.delete(symbol);
                }
            }
        };
    }

    /**
     * Handle market data updates from WebSocket
     */
    handleMarketDataUpdate(data) {
        try {
            if (data.symbol) {
                // Update current prices
                this.currentPrices.set(data.symbol, data);

                // Notify callbacks
                const callbacks = this.updateCallbacks.get(data.symbol);
                if (callbacks) {
                    callbacks.forEach(callback => {
                        try {
                            callback(data);
                        } catch (error) {
                            console.error('MarketData: Error in callback:', error);
                        }
                    });
                }
            }
        } catch (error) {
            console.error('MarketData: Error handling update:', error);
        }
    }

    /**
     * Get cached price data
     */
    getCachedPrice(symbol) {
        return this.currentPrices.get(symbol);
    }

    /**
     * Generate strike prices around current price (5 above + 5 below = 10 total)
     */
    generateStrikePrices(currentPrice, count = 5, interval = 50) {
        const strikes = [];
        const baseStrike = Math.round(currentPrice / interval) * interval;

        // Generate 5 strikes below current price
        for (let i = count; i > 0; i--) {
            strikes.push(baseStrike - (i * interval));
        }

        // Generate 5 strikes above current price
        for (let i = 1; i <= count; i++) {
            strikes.push(baseStrike + (i * interval));
        }

        return strikes.sort((a, b) => a - b);
    }

    /**
     * Get option chain data for a symbol - simplified to show strikes only
     */
    async getOptionChainData(symbol, selectedExpiry = null) {
        try {
            console.log('MarketData: Getting option chain for', symbol);

            // Get current price
            let currentPriceData;
            if (symbol === 'NIFTY') {
                currentPriceData = await this.getNiftyPrice();
            } else if (symbol === 'BANKNIFTY') {
                currentPriceData = await this.getBankNiftyPrice();
            } else {
                throw new Error('Unsupported symbol: ' + symbol);
            }

            const currentPrice = currentPriceData.ltp;

            // Generate strike prices (5 above + 5 below = 10 total)
            const interval = symbol === 'BANKNIFTY' ? 100 : 50;
            const strikes = this.generateStrikePrices(currentPrice, 5, interval);

            // Generate simple expiries for display
            const availableExpiries = this.getSimpleExpiries();

            // Use selected expiry or default to first one
            const expiry = selectedExpiry || availableExpiries[0];

            // Create simple option chain structure with strikes only
            const optionChain = strikes.map(strike => ({
                strike,
                call: {
                    symbol: `${symbol}${expiry}${strike}CE`,
                    token: `${symbol}_${strike}_CE_${expiry}`,
                    ltp: 0,
                    change: 0,
                    volume: 0,
                    oi: 0,
                    bid: 0,
                    ask: 0
                },
                put: {
                    symbol: `${symbol}${expiry}${strike}PE`,
                    token: `${symbol}_${strike}_PE_${expiry}`,
                    ltp: 0,
                    change: 0,
                    volume: 0,
                    oi: 0,
                    bid: 0,
                    ask: 0
                }
            }));

            return {
                symbol,
                currentPrice,
                currentPriceData,
                expiry,
                availableExpiries,
                strikes,
                optionChain
            };

        } catch (error) {
            console.error('MarketData: Failed to get option chain:', error);
            throw error;
        }
    }

    /**
     * Generate simple expiry dates for display
     */
    getSimpleExpiries() {
        const expiries = [];
        const today = new Date();

        // Generate next two weekly expiries (Thursdays)
        for (let i = 0; i < 2; i++) {
            const nextThursday = new Date(today);
            const daysUntilThursday = (4 - today.getDay() + 7) % 7 || 7; // Thursday is day 4
            nextThursday.setDate(today.getDate() + daysUntilThursday + (i * 7));

            const day = nextThursday.getDate().toString().padStart(2, '0');
            const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                           'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
            const month = months[nextThursday.getMonth()];
            const year = nextThursday.getFullYear().toString().slice(-2);

            expiries.push(`${day}${month}${year}`);
        }

        return expiries;
    }

            // Combine data
            const optionChain = strikes.map(strike => {
                const callOption = optionTokens.calls.find(opt => opt.strike === strike);
                const putOption = optionTokens.puts.find(opt => opt.strike === strike);

                const callPrice = callOption ? pricesData.find(p => p.token === callOption.token) : null;
                const putPrice = putOption ? pricesData.find(p => p.token === putOption.token) : null;

                return {
                    strike,
                    call: callOption ? {
                        symbol: callOption.symbol,
                        token: callOption.token,
                        ltp: callPrice?.ltp || 0,
                        change: callPrice?.change || 0,
                        volume: callPrice?.volume || 0,
                        oi: callPrice?.oi || 0,
                        bid: callPrice?.bid || 0,
                        ask: callPrice?.ask || 0
                    } : null,
                    put: putOption ? {
                        symbol: putOption.symbol,
                        token: putOption.token,
                        ltp: putPrice?.ltp || 0,
                        change: putPrice?.change || 0,
                        volume: putPrice?.volume || 0,
                        oi: putPrice?.oi || 0,
                        bid: putPrice?.bid || 0,
                        ask: putPrice?.ask || 0
                    } : null
                };
            });

            return {
                symbol,
                currentPrice,
                expiry,
                availableExpiries: nearestExpiries,
                strikes,
                optionChain
            };

        } catch (error) {
            console.error('MarketData: Failed to get option chain:', error);
            throw error;
        }
    }

    /**
     * Clean up subscriptions
     */
    cleanup() {
        this.subscriptions.clear();
        this.updateCallbacks.clear();
        this.currentPrices.clear();
    }
}

// Create global instance
window.marketDataService = new MarketDataService();
