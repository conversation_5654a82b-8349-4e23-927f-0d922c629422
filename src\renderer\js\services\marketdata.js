/**
 * Market Data Service
 * Handles real-time market data fetching and management
 */
class MarketDataService {
    constructor() {
        this.currentPrices = new Map();
        this.subscriptions = new Set();
        this.updateCallbacks = new Map();
        this.isInitialized = false;
    }

    /**
     * Initialize market data service
     */
    async initialize() {
        if (this.isInitialized) {
            return true;
        }

        try {
            // Set up market data update listener
            if (window.electronAPI && window.electronAPI.onMarketDataUpdate) {
                window.electronAPI.onMarketDataUpdate((event, data) => {
                    this.handleMarketDataUpdate(data);
                });
            }

            this.isInitialized = true;
            console.log('MarketData: Service initialized');
            return true;
        } catch (error) {
            console.error('MarketData: Failed to initialize:', error);
            return false;
        }
    }

    /**
     * Get current price for Nifty 50
     */
    async getNiftyPrice() {
        try {
            // Use the exact neo symbol format for Nifty 50 index
            // For indices, we need to find the correct token from script master
            const neoSymbol = 'nse_cm|26000'; // Nifty 50 token
            const result = await window.electronAPI.getQuotes(neoSymbol, 'all');

            if (result.success && result.data && result.data.length > 0) {
                const quote = result.data[0];
                const priceData = {
                    symbol: 'NIFTY',
                    token: quote.exchange_token,
                    ltp: parseFloat(quote.ltp),
                    change: parseFloat(quote.change),
                    changePercent: parseFloat(quote.per_change),
                    high: quote.ohlc ? parseFloat(quote.ohlc.high) : 0,
                    low: quote.ohlc ? parseFloat(quote.ohlc.low) : 0,
                    open: quote.ohlc ? parseFloat(quote.ohlc.open) : 0,
                    close: quote.ohlc ? parseFloat(quote.ohlc.close) : 0,
                    volume: parseInt(quote.last_volume) || 0
                };

                this.currentPrices.set('NIFTY', priceData);
                console.log('MarketData: Nifty price updated:', priceData);
                return priceData;
            }

            throw new Error('No price data received for Nifty');
        } catch (error) {
            console.error('MarketData: Failed to get Nifty price:', error);
            // Return mock data for testing
            const mockData = {
                symbol: 'NIFTY',
                token: '26000',
                ltp: 23500.50,
                change: 125.30,
                changePercent: 0.54,
                high: 23650.00,
                low: 23400.00,
                open: 23450.00,
                close: 23375.20,
                volume: 0
            };
            this.currentPrices.set('NIFTY', mockData);
            console.log('MarketData: Using mock Nifty data:', mockData);
            return mockData;
        }
    }

    /**
     * Get current price for Bank Nifty
     */
    async getBankNiftyPrice() {
        try {
            // Use the exact neo symbol format for Bank Nifty index
            const neoSymbol = 'nse_cm|26009'; // Bank Nifty token
            const result = await window.electronAPI.getQuotes(neoSymbol, 'all');

            if (result.success && result.data && result.data.length > 0) {
                const quote = result.data[0];
                const priceData = {
                    symbol: 'BANKNIFTY',
                    token: quote.exchange_token,
                    ltp: parseFloat(quote.ltp),
                    change: parseFloat(quote.change),
                    changePercent: parseFloat(quote.per_change),
                    high: quote.ohlc ? parseFloat(quote.ohlc.high) : 0,
                    low: quote.ohlc ? parseFloat(quote.ohlc.low) : 0,
                    open: quote.ohlc ? parseFloat(quote.ohlc.open) : 0,
                    close: quote.ohlc ? parseFloat(quote.ohlc.close) : 0,
                    volume: parseInt(quote.last_volume) || 0
                };

                this.currentPrices.set('BANKNIFTY', priceData);
                console.log('MarketData: Bank Nifty price updated:', priceData);
                return priceData;
            }

            throw new Error('No price data received for Bank Nifty');
        } catch (error) {
            console.error('MarketData: Failed to get Bank Nifty price:', error);
            // Return mock data for testing
            const mockData = {
                symbol: 'BANKNIFTY',
                token: '26009',
                ltp: 51200.75,
                change: 285.50,
                changePercent: 0.56,
                high: 51450.00,
                low: 50950.00,
                open: 51000.00,
                close: 50915.25,
                volume: 0
            };
            this.currentPrices.set('BANKNIFTY', mockData);
            console.log('MarketData: Using mock Bank Nifty data:', mockData);
            return mockData;
        }
    }

    /**
     * Get prices for multiple tokens using neo symbols
     */
    async getPrices(neoSymbols) {
        try {
            if (!Array.isArray(neoSymbols)) {
                neoSymbols = [neoSymbols];
            }

            // Join symbols with comma for the API
            const symbolsParam = neoSymbols.join(',');
            const result = await window.electronAPI.getQuotes(symbolsParam, 'all');

            if (result.success && result.data) {
                // Transform the data to our format
                return result.data.map(quote => ({
                    token: quote.exchange_token,
                    symbol: quote.display_symbol,
                    ltp: parseFloat(quote.ltp),
                    change: parseFloat(quote.change),
                    changePercent: parseFloat(quote.per_change),
                    volume: parseInt(quote.last_volume) || 0,
                    oi: parseInt(quote.open_int) || 0,
                    bid: quote.depth?.buy?.[0]?.price ? parseFloat(quote.depth.buy[0].price) : 0,
                    ask: quote.depth?.sell?.[0]?.price ? parseFloat(quote.depth.sell[0].price) : 0,
                    high: quote.ohlc ? parseFloat(quote.ohlc.high) : 0,
                    low: quote.ohlc ? parseFloat(quote.ohlc.low) : 0,
                    open: quote.ohlc ? parseFloat(quote.ohlc.open) : 0,
                    close: quote.ohlc ? parseFloat(quote.ohlc.close) : 0
                }));
            }
            throw new Error('Failed to get market data');
        } catch (error) {
            console.error('MarketData: Failed to get prices:', error);
            throw error;
        }
    }

    /**
     * Subscribe to real-time updates for symbols
     */
    async subscribe(symbols) {
        try {
            if (!Array.isArray(symbols)) {
                symbols = [symbols];
            }

            // Add to subscriptions
            symbols.forEach(symbol => this.subscriptions.add(symbol));

            // Subscribe via WebSocket
            const result = await window.electronAPI.subscribeToMarketData(symbols);
            if (result.success) {
                console.log('MarketData: Subscribed to symbols:', symbols);
                return true;
            }
            
            throw new Error('Failed to subscribe to market data');
        } catch (error) {
            console.error('MarketData: Failed to subscribe:', error);
            throw error;
        }
    }

    /**
     * Unsubscribe from real-time updates
     */
    async unsubscribe(symbols) {
        try {
            if (!Array.isArray(symbols)) {
                symbols = [symbols];
            }

            // Remove from subscriptions
            symbols.forEach(symbol => this.subscriptions.delete(symbol));

            // Unsubscribe via WebSocket
            const result = await window.electronAPI.unsubscribeFromMarketData(symbols);
            if (result.success) {
                console.log('MarketData: Unsubscribed from symbols:', symbols);
                return true;
            }
            
            throw new Error('Failed to unsubscribe from market data');
        } catch (error) {
            console.error('MarketData: Failed to unsubscribe:', error);
            throw error;
        }
    }

    /**
     * Register callback for price updates
     */
    onPriceUpdate(symbol, callback) {
        if (!this.updateCallbacks.has(symbol)) {
            this.updateCallbacks.set(symbol, new Set());
        }
        this.updateCallbacks.get(symbol).add(callback);

        // Return unsubscribe function
        return () => {
            const callbacks = this.updateCallbacks.get(symbol);
            if (callbacks) {
                callbacks.delete(callback);
                if (callbacks.size === 0) {
                    this.updateCallbacks.delete(symbol);
                }
            }
        };
    }

    /**
     * Handle market data updates from WebSocket
     */
    handleMarketDataUpdate(data) {
        try {
            if (data.symbol) {
                // Update current prices
                this.currentPrices.set(data.symbol, data);

                // Notify callbacks
                const callbacks = this.updateCallbacks.get(data.symbol);
                if (callbacks) {
                    callbacks.forEach(callback => {
                        try {
                            callback(data);
                        } catch (error) {
                            console.error('MarketData: Error in callback:', error);
                        }
                    });
                }
            }
        } catch (error) {
            console.error('MarketData: Error handling update:', error);
        }
    }

    /**
     * Get cached price data
     */
    getCachedPrice(symbol) {
        return this.currentPrices.get(symbol);
    }

    /**
     * Generate strike prices around current price (5 above + 5 below = 10 total)
     */
    generateStrikePrices(currentPrice, count = 5, interval = 50) {
        const strikes = [];
        const baseStrike = Math.round(currentPrice / interval) * interval;

        // Generate 5 strikes below current price
        for (let i = count; i > 0; i--) {
            strikes.push(baseStrike - (i * interval));
        }

        // Generate 5 strikes above current price
        for (let i = 1; i <= count; i++) {
            strikes.push(baseStrike + (i * interval));
        }

        return strikes.sort((a, b) => a - b);
    }

    /**
     * Get option chain data for a symbol - simplified to show strikes only
     */
    async getOptionChainData(symbol, selectedExpiry = null) {
        try {
            console.log('MarketData: Getting option chain for', symbol);

            // Get current price
            let currentPriceData;
            if (symbol === 'NIFTY') {
                currentPriceData = await this.getNiftyPrice();
            } else if (symbol === 'BANKNIFTY') {
                currentPriceData = await this.getBankNiftyPrice();
            } else {
                throw new Error('Unsupported symbol: ' + symbol);
            }

            const currentPrice = currentPriceData.ltp;

            // Generate strike prices (5 above + 5 below = 10 total)
            const interval = symbol === 'BANKNIFTY' ? 100 : 50;
            const strikes = this.generateStrikePrices(currentPrice, 5, interval);

            // Generate simple expiries for display
            const availableExpiries = this.getSimpleExpiries();

            // Use selected expiry or default to first one
            const expiry = selectedExpiry || availableExpiries[0];

            // Create simple option chain structure with strikes only
            const optionChain = strikes.map(strike => ({
                strike,
                call: {
                    symbol: `${symbol}${expiry}${strike}CE`,
                    token: `${symbol}_${strike}_CE_${expiry}`,
                    ltp: 0,
                    change: 0,
                    volume: 0,
                    oi: 0,
                    bid: 0,
                    ask: 0
                },
                put: {
                    symbol: `${symbol}${expiry}${strike}PE`,
                    token: `${symbol}_${strike}_PE_${expiry}`,
                    ltp: 0,
                    change: 0,
                    volume: 0,
                    oi: 0,
                    bid: 0,
                    ask: 0
                }
            }));

            return {
                symbol,
                currentPrice,
                currentPriceData,
                expiry,
                availableExpiries,
                strikes,
                optionChain
            };

        } catch (error) {
            console.error('MarketData: Failed to get option chain:', error);
            throw error;
        }
    }

    /**
     * Generate simple expiry dates for display
     */
    getSimpleExpiries() {
        const expiries = [];
        const today = new Date();

        // Generate next two weekly expiries (Thursdays)
        for (let i = 0; i < 2; i++) {
            const nextThursday = new Date(today);
            const daysUntilThursday = (4 - today.getDay() + 7) % 7 || 7; // Thursday is day 4
            nextThursday.setDate(today.getDate() + daysUntilThursday + (i * 7));

            const day = nextThursday.getDate().toString().padStart(2, '0');
            const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                           'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
            const month = months[nextThursday.getMonth()];
            const year = nextThursday.getFullYear().toString().slice(-2);

            expiries.push(`${day}${month}${year}`);
        }

        return expiries;
    }

            // Combine data
            const optionChain = strikes.map(strike => {
                const callOption = optionTokens.calls.find(opt => opt.strike === strike);
                const putOption = optionTokens.puts.find(opt => opt.strike === strike);

                const callPrice = callOption ? pricesData.find(p => p.token === callOption.token) : null;
                const putPrice = putOption ? pricesData.find(p => p.token === putOption.token) : null;

                return {
                    strike,
                    call: callOption ? {
                        symbol: callOption.symbol,
                        token: callOption.token,
                        ltp: callPrice?.ltp || 0,
                        change: callPrice?.change || 0,
                        volume: callPrice?.volume || 0,
                        oi: callPrice?.oi || 0,
                        bid: callPrice?.bid || 0,
                        ask: callPrice?.ask || 0
                    } : null,
                    put: putOption ? {
                        symbol: putOption.symbol,
                        token: putOption.token,
                        ltp: putPrice?.ltp || 0,
                        change: putPrice?.change || 0,
                        volume: putPrice?.volume || 0,
                        oi: putPrice?.oi || 0,
                        bid: putPrice?.bid || 0,
                        ask: putPrice?.ask || 0
                    } : null
                };
            });

            return {
                symbol,
                currentPrice,
                expiry,
                availableExpiries: nearestExpiries,
                strikes,
                optionChain
            };

        } catch (error) {
            console.error('MarketData: Failed to get option chain:', error);
            throw error;
        }
    }

    /**
     * Clean up subscriptions
     */
    cleanup() {
        this.subscriptions.clear();
        this.updateCallbacks.clear();
        this.currentPrices.clear();
    }
}

// Create global instance
window.marketDataService = new MarketDataService();
