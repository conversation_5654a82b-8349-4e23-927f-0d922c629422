// src/main/scripMasterService.js
const fs = require('fs');
const path = require('path');
const axios = require('axios');

const SCRIP_MASTER_URL = 'https://gw-napi.kotaksecurities.com/Files/1.0/masterscrip/v2/file-paths';
const LOCAL_STORE_PATH = path.join(__dirname, '..', '..', 'data');
const LOCAL_NSE_FO_PATH = path.join(LOCAL_STORE_PATH, 'nse_fo_data.json');

let nseFoData = [];

// Helper to find the next Thursday (standard weekly expiry day)
function getNextExpiry(date = new Date()) {
    const d = new Date(date);
    d.setUTCHours(0, 0, 0, 0);
    const day = d.getUTCDay();
    const diff = (day < 4) ? 4 - day : 11 - day; // Find next Thursday
    d.setUTCDate(d.getUTCDate() + diff);
    // Format to YYYYMMDD
    return d.toISOString().slice(0, 10).replace(/-/g, '');
}

// Simple CSV parser (since papaparse might not be available)
function parseCSV(csvText) {
    const lines = csvText.split('\n');
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const data = [];
    
    for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;
        
        const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
        if (values.length >= headers.length) {
            const row = {};
            headers.forEach((header, index) => {
                row[header] = values[index] || '';
            });
            data.push(row);
        }
    }
    
    return { data };
}

async function initializeScripMaster(accessToken) {
    console.log('[ScripMaster] Initializing...');
    try {
        if (!fs.existsSync(LOCAL_STORE_PATH)) {
            fs.mkdirSync(LOCAL_STORE_PATH, { recursive: true });
        }

        console.log('[ScripMaster] Fetching fresh scrip master file paths.');

        const response = await axios.get(SCRIP_MASTER_URL, {
            headers: {
                'accept': '*/*',
                'Authorization': `Bearer ${accessToken}`
            }
        });

        console.log('[ScripMaster] Response:', response.data);

        const nseFoUrl = response.data.data.filesPaths.find(p => p.includes('nse_fo.csv'));
        if (!nseFoUrl) {
            throw new Error('NSE FO scrip master URL not found.');
        }

        console.log(`[ScripMaster] Downloading from: ${nseFoUrl}`);
        const csvResponse = await axios.get(nseFoUrl, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });
        
        const parsedData = parseCSV(csvResponse.data);

        // Filter and simplify the data to only what we need
        nseFoData = parsedData.data
            .filter(row => (row.pSymbol === 'NIFTY' || row.pSymbol === 'BANKNIFTY') && row.pInst === 'OPTIDX')
            .map(row => ({
                token: row.pToken,
                tradingSymbol: row.pTrdSymbol,
                symbol: row.pSymbol,
                expiry: row.lExpiryDate ? row.lExpiryDate.split(' ')[0] : '', // Format: YYYYMMDD
                strike: parseFloat(row.dStrikePrice) / 100,
                optionType: row.cOptType,
            }))
            .filter(row => row.expiry && row.strike && row.optionType); // Remove invalid entries

        fs.writeFileSync(LOCAL_NSE_FO_PATH, JSON.stringify(nseFoData, null, 2));
        console.log(`[ScripMaster] Successfully processed and saved ${nseFoData.length} Nifty/BankNifty options.`);
        
        return true;
    } catch (error) {
        console.error('[ScripMaster] Failed to initialize:', error.message);
        // Fallback to local copy if it exists
        if (fs.existsSync(LOCAL_NSE_FO_PATH)) {
            console.log('[ScripMaster] Falling back to local scrip master file.');
            const fileContent = fs.readFileSync(LOCAL_NSE_FO_PATH, 'utf-8');
            nseFoData = JSON.parse(fileContent);
            return true;
        } else {
            console.error('[ScripMaster] No local copy available.');
            return false;
        }
    }
}

function findOption(symbol, strike, expiryDate, optionType) {
    return nseFoData.find(opt =>
        opt.symbol === symbol &&
        opt.strike === strike &&
        opt.expiry === expiryDate &&
        opt.optionType === optionType
    );
}

function getOptionsData() {
    return nseFoData;
}

module.exports = {
    initializeScripMaster,
    findOption,
    getNextExpiry,
    getOptionsData
};
