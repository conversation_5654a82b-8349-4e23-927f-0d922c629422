# Nifty Options Trading Bot

A comprehensive desktop application for automated trading of Nifty 50 and Bank Nifty options using the Kotak Neo Trade API. Built with Electron, this application provides real-time market data, automated trading strategies, and comprehensive portfolio management.

## Features

### 🚀 Core Features
- **Real-time Market Data**: Live price feeds for Nifty 50, Bank Nifty, and options
- **Automated Trading**: Schedule trades with automatic execution at specified times
- **Option Chain Display**: Interactive option chain with 5 strikes above and below current price
- **Stop Loss & Target**: Automatic exit orders based on percentage or price levels
- **Portfolio Management**: Real-time P&L tracking and position monitoring
- **Risk Management**: Daily loss/profit limits and position size controls

### 📊 Trading Features
- **Multiple Order Types**: Market, Limit, Stop Loss orders
- **Scheduled Trading**: Set exact time for order execution
- **Auto Square-off**: Automatic position closure before market close
- **Real-time Monitoring**: Live price updates and position tracking
- **Emergency Stop**: Instant halt of all trading activities

### 🔐 Security & Authentication
- **TOTP Authentication**: Secure login with Time-based OTP
- **Encrypted Storage**: Secure credential storage on local machine
- **Session Management**: Automatic token refresh and session handling

### 📈 Analytics & Reporting
- **Day Trading Summary**: Daily P&L, win rate, and trade statistics
- **Performance Metrics**: Sharpe ratio, maximum drawdown, volatility
- **Activity Logging**: Comprehensive audit trail of all activities
- **Data Export**: Export trading data and reports

## Prerequisites

Before running the application, ensure you have:

1. **Node.js** (v16 or higher)
2. **npm** or **yarn** package manager
3. **Kotak Neo Trade Account** with API access
4. **API Credentials** from Kotak Securities

## Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/nifty-options-trading-bot.git
   cd nifty-options-trading-bot
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the application**
   ```bash
   npm start
   ```

## Configuration

### API Credentials Setup

1. Open the application
2. Click on the **Settings** button (gear icon)
3. Navigate to the **API Credentials** tab
4. Enter your Kotak Neo Trade credentials:
   - User ID
   - Password
   - Mobile Number
   - Consumer Key
   - Consumer Secret
   - Environment (Production/UAT)

5. Click **Save Credentials**
6. Test the connection using the **Test Connection** button

### Trading Settings

Configure your trading preferences in the **Trading Settings** tab:
- Default symbol (Nifty 50 or Bank Nifty)
- Default lot size
- Auto square-off time
- Default order type
- Product type (Intraday/Normal)

### Risk Management

Set up risk controls in the **Risk Management** tab:
- Maximum loss per day
- Maximum profit per day
- Maximum trades per day
- Default stop loss percentage
- Default target percentage
- Position sizing method

## Usage

### Authentication

1. After configuring credentials, enter your 6-digit TOTP code
2. Click **Authenticate** to login
3. The connection status will show "Connected" when successful

### Viewing Option Chain

1. Navigate to the **Trading** tab
2. Select your preferred symbol (Nifty 50 or Bank Nifty)
3. Choose the expiry date
4. The option chain will display with current prices and Greeks

### Placing Orders

1. Click on the **Buy** or **Sell** button for any option in the chain
2. Configure order details:
   - Quantity (in lots)
   - Price type (Market/Limit)
   - Stop loss percentage
   - Target percentage
3. Optionally schedule the order for future execution
4. Click **Place Order**

### Monitoring Positions

1. Navigate to the **Portfolio** tab to view current positions
2. The **Dashboard** shows real-time P&L and statistics
3. All positions are automatically monitored for stop loss and target

### Starting Automated Trading

1. Ensure you're authenticated and have configured risk settings
2. Click **Start Trading** on the dashboard
3. The system will automatically monitor positions and execute stop loss/target orders
4. Use **Emergency Stop** to immediately halt all activities

## API Documentation

The application uses the Kotak Neo Trade API. Key endpoints include:

- **Authentication**: Login with TOTP
- **Market Data**: Real-time quotes and option chain
- **Order Management**: Place, modify, cancel orders
- **Portfolio**: Positions, holdings, and P&L
- **WebSocket**: Real-time data streaming

## File Structure

```
src/
├── main/                 # Electron main process
│   ├── main.js          # Main application entry
│   └── preload.js       # Preload script for security
├── renderer/            # Electron renderer process
│   ├── index.html       # Main HTML file
│   ├── js/              # JavaScript files
│   │   ├── app.js       # Main application logic
│   │   ├── components/  # UI components
│   │   ├── services/    # API and WebSocket services
│   │   └── utils/       # Utility functions
│   └── styles/          # CSS stylesheets
└── services/            # Backend services
    ├── authService.js   # Authentication handling
    ├── marketDataService.js # Market data API
    ├── orderService.js  # Order management
    ├── portfolioService.js # Portfolio tracking
    ├── tradingEngine.js # Automated trading logic
    └── websocketService.js # Real-time data
```

## Development

### Running in Development Mode

```bash
npm run dev
```

### Building for Production

```bash
# Build for current platform
npm run build

# Build for Windows
npm run build:win

# Build for macOS
npm run build:mac

# Build for Linux
npm run build:linux
```

### Testing

```bash
npm test
```

## Risk Disclaimer

**IMPORTANT**: This software is for educational and informational purposes only. Trading in options involves substantial risk and may not be suitable for all investors. Past performance is not indicative of future results.

- **Use at your own risk**: The developers are not responsible for any financial losses
- **Test thoroughly**: Always test with small amounts before using larger positions
- **Market risks**: Options trading can result in significant losses
- **Technical risks**: Software bugs or connectivity issues may affect trading
- **Regulatory compliance**: Ensure compliance with local trading regulations

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the API documentation from Kotak Securities

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Changelog

### Version 1.0.0
- Initial release
- Basic trading functionality
- Real-time market data
- Option chain display
- Automated stop loss and target
- Portfolio management
- Risk management controls

## Acknowledgments

- Kotak Securities for providing the Neo Trade API
- Electron framework for cross-platform desktop development
- All contributors and testers

---

**Remember**: Always trade responsibly and never risk more than you can afford to lose.
