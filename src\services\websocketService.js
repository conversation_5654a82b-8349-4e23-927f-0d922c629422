const WebSocket = require('ws');
const EventEmitter = require('events');

/**
 * WebSocket Service for Kotak Neo Trade API
 * Handles real-time market data streaming
 */
class WebSocketService extends EventEmitter {
    constructor() {
        super();
        this.ws = null;
        this.isConnected = false;
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 5000;
        this.heartbeatInterval = null;
        this.subscriptions = new Set();
        this.messageQueue = [];
        
        // WebSocket URLs
        this.wsUrls = {
            prod: 'wss://mlhsi.kotaksecurities.com',
            uat: 'wss://uatws.kotaksecurities.com'
        };
        
        this.environment = 'prod';
        this.accessToken = null;
        this.userId = null;
    }

    /**
     * Initialize WebSocket connection
     */
    async initialize(config = {}) {
        this.environment = config.environment || 'prod';
        this.accessToken = config.accessToken;
        this.userId = config.userId;
        
        if (!this.accessToken || !this.userId) {
            throw new Error('Access token and user ID are required for WebSocket connection');
        }
        
        await this.connect();
    }

    /**
     * Connect to WebSocket
     */
    async connect() {
        if (this.isConnected || this.isConnecting) {
            return;
        }

        this.isConnecting = true;
        this.emit('connecting');

        try {
            const wsUrl = this.wsUrls[this.environment];
            console.log(`Connecting to WebSocket: ${wsUrl}`);

            this.ws = new WebSocket(wsUrl);
            this.setupEventHandlers();

            // Wait for connection to be established
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('WebSocket connection timeout'));
                }, 10000);

                this.ws.once('open', () => {
                    clearTimeout(timeout);
                    resolve();
                });

                this.ws.once('error', (error) => {
                    clearTimeout(timeout);
                    reject(error);
                });
            });

        } catch (error) {
            this.isConnecting = false;
            console.error('WebSocket connection failed:', error);
            this.emit('error', error);
            
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
                this.scheduleReconnect();
            }
            throw error;
        }
    }

    /**
     * Setup WebSocket event handlers
     */
    setupEventHandlers() {
        this.ws.on('open', () => {
            console.log('WebSocket connected');
            this.isConnected = true;
            this.isConnecting = false;
            this.reconnectAttempts = 0;
            
            this.emit('connected');
            this.authenticate();
            this.startHeartbeat();
            this.processMessageQueue();
        });

        this.ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                this.handleMessage(message);
            } catch (error) {
                console.error('Failed to parse WebSocket message:', error);
            }
        });

        this.ws.on('close', (code, reason) => {
            console.log(`WebSocket disconnected: ${code} - ${reason}`);
            this.isConnected = false;
            this.isConnecting = false;
            
            this.emit('disconnected', { code, reason });
            this.stopHeartbeat();
            
            if (code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
                this.scheduleReconnect();
            }
        });

        this.ws.on('error', (error) => {
            console.error('WebSocket error:', error);
            this.emit('error', error);
        });
    }

    /**
     * Authenticate WebSocket connection
     */
    authenticate() {
        const authMessage = {
            a: 'login',
            user: this.userId,
            token: this.accessToken
        };
        
        this.sendMessage(authMessage);
    }

    /**
     * Start heartbeat to keep connection alive
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.sendMessage({ a: 'heartbeat' });
            }
        }, 30000); // Send heartbeat every 30 seconds
    }

    /**
     * Stop heartbeat
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    /**
     * Schedule reconnection attempt
     */
    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
        
        setTimeout(() => {
            if (!this.isConnected && !this.isConnecting) {
                this.connect().catch(error => {
                    console.error('Reconnection failed:', error);
                });
            }
        }, delay);
    }

    /**
     * Send message to WebSocket
     */
    sendMessage(message) {
        if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        } else {
            // Queue message for later sending
            this.messageQueue.push(message);
        }
    }

    /**
     * Process queued messages
     */
    processMessageQueue() {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.sendMessage(message);
        }
    }

    /**
     * Handle incoming WebSocket messages
     */
    handleMessage(message) {
        switch (message.a) {
            case 'login':
                this.handleLoginResponse(message);
                break;
            case 'quote':
                this.handleQuoteUpdate(message);
                break;
            case 'depth':
                this.handleDepthUpdate(message);
                break;
            case 'ack':
                this.handleAcknowledgment(message);
                break;
            case 'error':
                this.handleError(message);
                break;
            default:
                console.log('Unknown message type:', message);
        }
    }

    /**
     * Handle login response
     */
    handleLoginResponse(message) {
        if (message.stat === 'Ok') {
            console.log('WebSocket authentication successful');
            this.emit('authenticated');
            
            // Re-subscribe to previously subscribed symbols
            this.resubscribeAll();
        } else {
            console.error('WebSocket authentication failed:', message.emsg);
            this.emit('authError', message.emsg);
        }
    }

    /**
     * Handle quote updates
     */
    handleQuoteUpdate(message) {
        const quoteData = {
            symbol: message.tk,
            ltp: parseFloat(message.lp || 0),
            change: parseFloat(message.c || 0),
            changePercent: parseFloat(message.pc || 0),
            volume: parseInt(message.v || 0),
            open: parseFloat(message.o || 0),
            high: parseFloat(message.h || 0),
            low: parseFloat(message.l || 0),
            close: parseFloat(message.lc || 0),
            timestamp: new Date()
        };
        
        this.emit('quote', quoteData);
    }

    /**
     * Handle market depth updates
     */
    handleDepthUpdate(message) {
        const depthData = {
            symbol: message.tk,
            bids: this.parseDepthData(message.bp, message.bq),
            asks: this.parseDepthData(message.sp, message.sq),
            timestamp: new Date()
        };
        
        this.emit('depth', depthData);
    }

    /**
     * Parse depth data
     */
    parseDepthData(prices, quantities) {
        const depth = [];
        if (prices && quantities) {
            for (let i = 0; i < Math.min(prices.length, quantities.length); i++) {
                depth.push({
                    price: parseFloat(prices[i]),
                    quantity: parseInt(quantities[i])
                });
            }
        }
        return depth;
    }

    /**
     * Handle acknowledgment messages
     */
    handleAcknowledgment(message) {
        console.log('WebSocket acknowledgment:', message);
    }

    /**
     * Handle error messages
     */
    handleError(message) {
        console.error('WebSocket error message:', message);
        this.emit('wsError', message);
    }

    /**
     * Subscribe to symbol quotes
     */
    subscribeQuote(symbols) {
        if (!Array.isArray(symbols)) {
            symbols = [symbols];
        }
        
        symbols.forEach(symbol => {
            this.subscriptions.add(symbol);
        });
        
        const subscribeMessage = {
            a: 'subscribe',
            v: symbols,
            m: 'quote'
        };
        
        this.sendMessage(subscribeMessage);
        console.log('Subscribed to quotes:', symbols);
    }

    /**
     * Subscribe to market depth
     */
    subscribeDepth(symbols) {
        if (!Array.isArray(symbols)) {
            symbols = [symbols];
        }
        
        symbols.forEach(symbol => {
            this.subscriptions.add(symbol);
        });
        
        const subscribeMessage = {
            a: 'subscribe',
            v: symbols,
            m: 'depth'
        };
        
        this.sendMessage(subscribeMessage);
        console.log('Subscribed to depth:', symbols);
    }

    /**
     * Unsubscribe from symbols
     */
    unsubscribe(symbols) {
        if (!Array.isArray(symbols)) {
            symbols = [symbols];
        }
        
        symbols.forEach(symbol => {
            this.subscriptions.delete(symbol);
        });
        
        const unsubscribeMessage = {
            a: 'unsubscribe',
            v: symbols
        };
        
        this.sendMessage(unsubscribeMessage);
        console.log('Unsubscribed from:', symbols);
    }

    /**
     * Re-subscribe to all previously subscribed symbols
     */
    resubscribeAll() {
        if (this.subscriptions.size > 0) {
            const symbols = Array.from(this.subscriptions);
            this.subscribeQuote(symbols);
            console.log('Re-subscribed to all symbols:', symbols);
        }
    }

    /**
     * Disconnect WebSocket
     */
    disconnect() {
        if (this.ws) {
            this.ws.close(1000, 'Client disconnect');
        }
        this.stopHeartbeat();
        this.subscriptions.clear();
        this.messageQueue = [];
    }

    /**
     * Get connection status
     */
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            isConnecting: this.isConnecting,
            reconnectAttempts: this.reconnectAttempts,
            subscriptions: Array.from(this.subscriptions)
        };
    }
}

module.exports = WebSocketService;
