/**
 * Script Master Service
 * Handles downloading and parsing of Kotak Neo Trade script master files
 * to get token codes for instruments
 */
class ScriptMasterService {
    constructor() {
        this.scriptMasterData = new Map();
        this.isLoaded = false;
        this.loadingPromise = null;
        this.niftyToken = null;
        this.bankNiftyToken = null;
        this.optionTokens = new Map();
    }

    /**
     * Initialize script master service
     */
    async initialize() {
        if (this.loadingPromise) {
            return this.loadingPromise;
        }

        this.loadingPromise = this._loadScriptMaster();
        return this.loadingPromise;
    }

    /**
     * Load script master data
     */
    async _loadScriptMaster() {
        try {
            console.log('ScriptMaster: Starting to load script master data...');
            
            // Get script master file paths
            const filesResponse = await window.electronAPI.getScriptMasterFiles();
            if (!filesResponse.success) {
                throw new Error('Failed to get script master files: ' + filesResponse.message);
            }

            const filePaths = filesResponse.data.filesPaths;
            console.log('ScriptMaster: Found file paths:', filePaths);

            // Find NSE FO (Futures & Options) file
            const nsefoFile = filePaths.find(path => path.includes('nse_fo.csv'));
            if (!nsefoFile) {
                throw new Error('NSE FO script master file not found');
            }

            console.log('ScriptMaster: Downloading NSE FO file:', nsefoFile);
            
            // Download and parse NSE FO file
            const csvResponse = await window.electronAPI.downloadScriptMaster(nsefoFile);
            if (!csvResponse.success) {
                throw new Error('Failed to download script master: ' + csvResponse.message);
            }

            console.log('ScriptMaster: Parsing CSV data...');
            await this._parseCSVData(csvResponse.data);

            // Find Nifty and Bank Nifty tokens
            await this._findIndexTokens();

            this.isLoaded = true;
            console.log('ScriptMaster: Successfully loaded script master data');
            
            return true;
        } catch (error) {
            console.error('ScriptMaster: Failed to load script master:', error);
            throw error;
        }
    }

    /**
     * Parse CSV data and store in memory
     */
    async _parseCSVData(csvData) {
        const lines = csvData.split('\n');
        const headers = lines[0].split(',').map(h => h.trim());
        
        console.log('ScriptMaster: CSV headers:', headers);
        
        for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;

            const values = line.split(',');
            if (values.length < headers.length) continue;

            const instrument = {};
            headers.forEach((header, index) => {
                instrument[header] = values[index] ? values[index].trim() : '';
            });

            // Store by symbol for quick lookup
            const symbol = instrument.pSymbol || instrument.symbol;
            if (symbol) {
                this.scriptMasterData.set(symbol, instrument);
            }
        }

        console.log(`ScriptMaster: Parsed ${this.scriptMasterData.size} instruments`);
    }

    /**
     * Find token codes for Nifty and Bank Nifty indices
     */
    async _findIndexTokens() {
        // Look for Nifty 50 index
        for (const [symbol, instrument] of this.scriptMasterData) {
            if (symbol.includes('NIFTY') && instrument.pInstType === 'IDX') {
                this.niftyToken = instrument.pToken;
                console.log('ScriptMaster: Found Nifty token:', this.niftyToken, 'for symbol:', symbol);
                break;
            }
        }

        // Look for Bank Nifty index
        for (const [symbol, instrument] of this.scriptMasterData) {
            if (symbol.includes('BANKNIFTY') && instrument.pInstType === 'IDX') {
                this.bankNiftyToken = instrument.pToken;
                console.log('ScriptMaster: Found Bank Nifty token:', this.bankNiftyToken, 'for symbol:', symbol);
                break;
            }
        }

        if (!this.niftyToken || !this.bankNiftyToken) {
            console.warn('ScriptMaster: Could not find all index tokens');
        }
    }

    /**
     * Get token for a specific symbol
     */
    getTokenBySymbol(symbol) {
        if (!this.isLoaded) {
            throw new Error('Script master not loaded. Call initialize() first.');
        }

        const instrument = this.scriptMasterData.get(symbol);
        return instrument ? instrument.pToken : null;
    }

    /**
     * Get Nifty 50 token
     */
    getNiftyToken() {
        return this.niftyToken;
    }

    /**
     * Get Bank Nifty token
     */
    getBankNiftyToken() {
        return this.bankNiftyToken;
    }

    /**
     * Generate option symbols for given strike prices
     */
    generateOptionSymbols(baseSymbol, expiry, strikes, optionType) {
        const symbols = [];
        const expiryStr = this._formatExpiryForSymbol(expiry);
        
        strikes.forEach(strike => {
            const symbol = `${baseSymbol}${expiryStr}${strike}${optionType}`;
            symbols.push(symbol);
        });

        return symbols;
    }

    /**
     * Get option tokens for given parameters
     */
    getOptionTokens(baseSymbol, expiry, strikes) {
        const tokens = {
            calls: [],
            puts: []
        };

        // Generate call option symbols and get tokens
        const callSymbols = this.generateOptionSymbols(baseSymbol, expiry, strikes, 'CE');
        callSymbols.forEach(symbol => {
            const token = this.getTokenBySymbol(symbol);
            if (token) {
                tokens.calls.push({ symbol, token, strike: this._extractStrike(symbol) });
            }
        });

        // Generate put option symbols and get tokens
        const putSymbols = this.generateOptionSymbols(baseSymbol, expiry, strikes, 'PE');
        putSymbols.forEach(symbol => {
            const token = this.getTokenBySymbol(symbol);
            if (token) {
                tokens.puts.push({ symbol, token, strike: this._extractStrike(symbol) });
            }
        });

        return tokens;
    }

    /**
     * Format expiry date for symbol generation
     */
    _formatExpiryForSymbol(expiry) {
        // Convert expiry to format used in symbols (e.g., "25JAN" for January 2025)
        const date = new Date(expiry);
        const year = date.getFullYear().toString().slice(-2);
        const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                       'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
        const month = months[date.getMonth()];
        const day = date.getDate().toString().padStart(2, '0');
        
        return `${day}${month}${year}`;
    }

    /**
     * Extract strike price from option symbol
     */
    _extractStrike(symbol) {
        const match = symbol.match(/(\d+)(CE|PE)$/);
        return match ? parseInt(match[1]) : null;
    }

    /**
     * Search instruments by pattern
     */
    searchInstruments(pattern, limit = 50) {
        if (!this.isLoaded) {
            return [];
        }

        const results = [];
        const searchPattern = pattern.toLowerCase();

        for (const [symbol, instrument] of this.scriptMasterData) {
            if (symbol.toLowerCase().includes(searchPattern) && results.length < limit) {
                results.push({
                    symbol,
                    token: instrument.pToken,
                    name: instrument.pDesc || symbol,
                    exchange: instrument.pExchange,
                    instrumentType: instrument.pInstType
                });
            }
        }

        return results;
    }

    /**
     * Get all available expiries for a base symbol
     */
    getAvailableExpiries(baseSymbol) {
        if (!this.isLoaded) {
            return [];
        }

        const expiries = new Set();
        const pattern = new RegExp(`^${baseSymbol}(\\d{2}[A-Z]{3}\\d{2})`);

        for (const symbol of this.scriptMasterData.keys()) {
            const match = symbol.match(pattern);
            if (match) {
                expiries.add(match[1]);
            }
        }

        return Array.from(expiries).sort();
    }
}

// Create global instance
window.scriptMasterService = new ScriptMasterService();
