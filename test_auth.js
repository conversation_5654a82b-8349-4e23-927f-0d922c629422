/**
 * Kotak Neo Trade API Authentication Test
 * This script tests the exact authentication flow from the examples
 */

const axios = require('axios');

class KotakAuthTest {
    constructor() {
        // These will be provided by user
        this.consumerKey = GfS95Qwtx6Pic9Up3P6sqmyRlu8a;
        this.consumerSecret = v6Foh1P4NjdL6StvUTqMtqBBBdEa;
        this.ucc = ZHBTW;
        this.mobileNumber = 9999977894;
        
        // Tokens
        this.clientAccessToken = null;
        this.viewToken = null;
        this.tradingToken = null;
        this.sessionId = null;
        this.requestId = null;
    }

    /**
     * Step 1: Get Client Access Token
     */
    async getClientAccessToken() {
        try {
            console.log('🔑 Step 1: Getting Client Access Token...');
            
            if (!this.consumerKey || !this.consumerSecret) {
                throw new Error('Consumer Key and Secret are required');
            }

            // Create Base64 encoded authorization header exactly like the example
            const auth = Buffer.from(`${this.consumerKey}:${this.consumerSecret}`).toString('base64');
            console.log(`Authorization: Basic ${auth}`);

            const response = await axios.post(
                'https://napi.kotaksecurities.com/oauth2/token',
                'grant_type=client_credentials',
                {
                    headers: {
                        'Authorization': `Basic ${auth}`,
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    timeout: 15000
                }
            );

            if (response.data && response.data.access_token) {
                this.clientAccessToken = response.data.access_token;
                console.log('✅ Client Access Token obtained successfully');
                console.log('Token Type:', response.data.token_type);
                console.log('Expires In:', response.data.expires_in);
                console.log('Scope:', response.data.scope);
                return this.clientAccessToken;
            } else {
                throw new Error('Failed to get client access token');
            }
        } catch (error) {
            console.error('❌ Client Access Token Error:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Step 2: Get View Token with TOTP
     */
    async getViewToken(totp) {
        try {
            console.log('\n🔐 Step 2: Getting View Token with TOTP...');
            
            if (!this.clientAccessToken) {
                throw new Error('Client access token required. Run getClientAccessToken() first.');
            }

            if (!this.ucc || !this.mobileNumber) {
                throw new Error('UCC and Mobile Number are required');
            }

            if (!/^\d{6}$/.test(totp)) {
                throw new Error('TOTP must be a 6-digit number');
            }

            // Format mobile number exactly like the example
            const formattedMobile = this.mobileNumber.startsWith('+91') 
                ? this.mobileNumber 
                : `+91${this.mobileNumber}`;

            const requestData = {
                mobileNumber: formattedMobile,
                ucc: this.ucc,
                totp: totp
            };

            console.log('Request Data:', requestData);

            const response = await axios.post(
                'https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/login',
                requestData,
                {
                    headers: {
                        'Authorization': `Bearer ${this.clientAccessToken}`,
                        'neo-fin-key': 'neotradeapi',
                        'Content-Type': 'application/json'
                    },
                    timeout: 15000
                }
            );

            console.log('Response Status:', response.status);
            console.log('Response Data:', JSON.stringify(response.data, null, 2));

            if (response.data && response.data.data && response.data.data.token) {
                const data = response.data.data;
                this.viewToken = data.token;
                this.sessionId = data.sid;
                this.requestId = data.rid;
                this.ucc = data.ucc;
                
                console.log('✅ View Token obtained successfully');
                console.log('Session ID:', this.sessionId);
                console.log('UCC:', this.ucc);
                console.log('Greeting Name:', data.greetingName);
                console.log('kType:', data.kType);
                
                return {
                    success: true,
                    data: data
                };
            } else {
                throw new Error(response.data?.message || 'Failed to get view token');
            }
        } catch (error) {
            console.error('❌ View Token Error:', error.response?.data || error.message);
            return {
                success: false,
                error: error.response?.data || error.message
            };
        }
    }

    /**
     * Step 3: Get Trading Token with MPIN
     */
    async getTradingToken(mpin) {
        try {
            console.log('\n🚀 Step 3: Getting Trading Token with MPIN...');
            
            if (!this.viewToken || !this.sessionId) {
                throw new Error('View token and session ID required. Run getViewToken() first.');
            }

            if (!/^\d{4,6}$/.test(mpin)) {
                throw new Error('MPIN must be 4-6 digits');
            }

            const requestData = {
                mpin: mpin
            };

            console.log('Request Data:', requestData);

            const response = await axios.post(
                'https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/validate',
                requestData,
                {
                    headers: {
                        'accept': 'application/json',
                        'sid': this.sessionId,
                        'Auth': this.viewToken,
                        'neo-fin-key': 'neotradeapi',
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.clientAccessToken}`
                    },
                    timeout: 15000
                }
            );

            console.log('Response Status:', response.status);
            console.log('Response Data:', JSON.stringify(response.data, null, 2));

            if (response.data && response.data.data && response.data.data.token) {
                const data = response.data.data;
                this.tradingToken = data.token;
                this.sessionId = data.sid; // Update session ID
                
                console.log('✅ Trading Token obtained successfully');
                console.log('New Session ID:', this.sessionId);
                console.log('kType:', data.kType);
                
                return {
                    success: true,
                    data: data
                };
            } else {
                throw new Error(response.data?.message || 'Failed to get trading token');
            }
        } catch (error) {
            console.error('❌ Trading Token Error:', error.response?.data || error.message);
            return {
                success: false,
                error: error.response?.data || error.message
            };
        }
    }

    /**
     * Test API call with trading token
     */
    async testAPICall() {
        try {
            console.log('\n📊 Testing API Call with Trading Token...');
            
            if (!this.tradingToken || !this.sessionId) {
                throw new Error('Trading token required. Run getTradingToken() first.');
            }

            // Test with script master API (doesn't require trading, just authentication)
            const response = await axios.get(
                'https://gw-napi.kotaksecurities.com/Files/1.0/masterscrip/v2/file-paths',
                {
                    headers: {
                        'accept': '*/*',
                        'Authorization': `Bearer ${this.clientAccessToken}`
                    },
                    timeout: 15000
                }
            );

            console.log('✅ API Test successful');
            console.log('Script Master Response:', JSON.stringify(response.data, null, 2));
            
            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('❌ API Test Error:', error.response?.data || error.message);
            return {
                success: false,
                error: error.response?.data || error.message
            };
        }
    }

    /**
     * Complete authentication flow
     */
    async authenticate(credentials, totp, mpin = null) {
        try {
            console.log('🚀 Starting Complete Authentication Flow...');
            console.log('='.repeat(50));
            
            // Set credentials
            this.consumerKey = credentials.consumerKey;
            this.consumerSecret = credentials.consumerSecret;
            this.ucc = credentials.ucc;
            this.mobileNumber = credentials.mobileNumber;
            
            // Step 1: Get Client Access Token
            await this.getClientAccessToken();
            
            // Step 2: Get View Token
            const viewResult = await this.getViewToken(totp);
            if (!viewResult.success) {
                return viewResult;
            }
            
            // Step 3: Get Trading Token (optional)
            if (mpin) {
                const tradingResult = await this.getTradingToken(mpin);
                if (!tradingResult.success) {
                    return tradingResult;
                }
            }
            
            // Test API call
            await this.testAPICall();
            
            console.log('\n🎉 Authentication Flow Completed Successfully!');
            console.log('='.repeat(50));
            
            return {
                success: true,
                tokens: {
                    clientAccessToken: this.clientAccessToken,
                    viewToken: this.viewToken,
                    tradingToken: this.tradingToken,
                    sessionId: this.sessionId,
                    ucc: this.ucc
                }
            };
            
        } catch (error) {
            console.error('❌ Authentication Flow Failed:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get current authentication status
     */
    getStatus() {
        return {
            hasClientToken: !!this.clientAccessToken,
            hasViewToken: !!this.viewToken,
            hasTradingToken: !!this.tradingToken,
            sessionId: this.sessionId,
            ucc: this.ucc
        };
    }
}

// Export for testing
module.exports = KotakAuthTest;

// If running directly, provide interactive test
if (require.main === module) {
    console.log('🧪 Kotak Neo Trade API Authentication Test');
    console.log('==========================================');
    console.log('');
    console.log('This script will test the authentication flow.');
    console.log('Please provide your credentials when prompted.');
    console.log('');
    console.log('Required credentials:');
    console.log('1. Consumer Key (from Kotak API portal)');
    console.log('2. Consumer Secret (from Kotak API portal)');
    console.log('3. UCC (Unique Client Code)');
    console.log('4. Mobile Number (10 digits)');
    console.log('5. TOTP (6-digit code from authenticator app)');
    console.log('6. MPIN (4-6 digit trading PIN) - Optional');
    console.log('');
    console.log('Ready to test? Provide credentials in the authenticate() call below:');
    console.log('');
    
    // Example usage (user will need to fill in their credentials)
    const authTest = new KotakAuthTest();
    
    // Uncomment and fill in your credentials to test:
    /*
    authTest.authenticate({
        consumerKey: 'YOUR_CONSUMER_KEY',
        consumerSecret: 'YOUR_CONSUMER_SECRET',
        ucc: 'YOUR_UCC',
        mobileNumber: 'YOUR_MOBILE_NUMBER'
    }, 'TOTP_CODE', 'MPIN_CODE').then(result => {
        console.log('Final Result:', result);
    });
    */
    
    console.log('Uncomment the code above and fill in your credentials to test.');
}
