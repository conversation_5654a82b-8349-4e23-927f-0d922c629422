/**
 * Interactive Kotak Neo Trade API Authentication Test
 * Run this script and provide your credentials to test authentication
 */

const readline = require('readline');
const KotakAuthTest = require('./test_auth');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

function hiddenQuestion(prompt) {
    return new Promise((resolve) => {
        process.stdout.write(prompt);
        process.stdin.setRawMode(true);
        process.stdin.resume();
        process.stdin.setEncoding('utf8');
        
        let input = '';
        process.stdin.on('data', function(char) {
            char = char + '';
            
            switch (char) {
                case '\n':
                case '\r':
                case '\u0004':
                    process.stdin.setRawMode(false);
                    process.stdin.pause();
                    process.stdout.write('\n');
                    resolve(input);
                    break;
                case '\u0003':
                    process.exit();
                    break;
                case '\u007f': // Backspace
                    if (input.length > 0) {
                        input = input.slice(0, -1);
                        process.stdout.write('\b \b');
                    }
                    break;
                default:
                    input += char;
                    process.stdout.write('*');
                    break;
            }
        });
    });
}

async function main() {
    console.log('🧪 Kotak Neo Trade API Authentication Test');
    console.log('==========================================\n');
    
    console.log('This will test the complete authentication flow.');
    console.log('Please have your credentials ready:\n');
    
    console.log('📋 Required Information:');
    console.log('1. Consumer Key (from Kotak API portal)');
    console.log('2. Consumer Secret (from Kotak API portal)');
    console.log('3. UCC (Unique Client Code, e.g., YOJE0)');
    console.log('4. Mobile Number (10 digits, no +91)');
    console.log('5. TOTP (6-digit code from authenticator app)');
    console.log('6. MPIN (4-6 digit trading PIN) - Optional\n');
    
    try {
        // Get credentials
        console.log('📝 Please enter your credentials:\n');
        
        const consumerKey = await question('Consumer Key: ');
        const consumerSecret = await hiddenQuestion('Consumer Secret: ');
        const ucc = await question('UCC (Unique Client Code): ');
        const mobileNumber = await question('Mobile Number (10 digits): ');
        
        console.log('\n🔐 Authentication Process:\n');
        
        const totp = await question('TOTP (6-digit code from authenticator): ');
        
        const wantTrading = await question('Do you want to enable trading? (y/n): ');
        let mpin = null;
        
        if (wantTrading.toLowerCase() === 'y' || wantTrading.toLowerCase() === 'yes') {
            mpin = await hiddenQuestion('MPIN (4-6 digits): ');
        }
        
        console.log('\n🚀 Starting Authentication...\n');
        
        // Create auth test instance
        const authTest = new KotakAuthTest();
        
        // Run authentication
        const result = await authTest.authenticate({
            consumerKey: consumerKey.trim(),
            consumerSecret: consumerSecret.trim(),
            ucc: ucc.trim().toUpperCase(),
            mobileNumber: mobileNumber.trim()
        }, totp.trim(), mpin ? mpin.trim() : null);
        
        console.log('\n📊 Final Result:');
        console.log('================');
        
        if (result.success) {
            console.log('✅ Authentication Successful!');
            console.log('\n🎯 Summary:');
            console.log(`- Client Access Token: ${result.tokens.clientAccessToken ? '✅ Obtained' : '❌ Failed'}`);
            console.log(`- View Token: ${result.tokens.viewToken ? '✅ Obtained' : '❌ Failed'}`);
            console.log(`- Trading Token: ${result.tokens.tradingToken ? '✅ Obtained' : '⏭️ Skipped'}`);
            console.log(`- Session ID: ${result.tokens.sessionId || 'N/A'}`);
            console.log(`- UCC: ${result.tokens.ucc || 'N/A'}`);
            
            console.log('\n🎉 You can now use these tokens for API calls!');
            
            // Show current status
            const status = authTest.getStatus();
            console.log('\n📈 Current Status:');
            console.log(JSON.stringify(status, null, 2));
            
        } else {
            console.log('❌ Authentication Failed!');
            console.log('\n🔍 Error Details:');
            console.log(result.error);
            
            console.log('\n💡 Common Issues:');
            console.log('- Check Consumer Key and Secret are correct');
            console.log('- Verify UCC format (usually 5-10 alphanumeric characters)');
            console.log('- Ensure mobile number is 10 digits without +91');
            console.log('- Use fresh TOTP code (not expired)');
            console.log('- Check MPIN is correct (4-6 digits)');
            console.log('- Ensure API access is approved by Kotak');
        }
        
    } catch (error) {
        console.error('\n❌ Test Failed:', error.message);
    } finally {
        rl.close();
    }
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n\n👋 Test cancelled by user');
    rl.close();
    process.exit(0);
});

// Run the interactive test
if (require.main === module) {
    main().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

module.exports = { main };
