const moment = require('moment');

/**
 * Option Chain Service
 * Handles option chain data, strike price discovery, and option calculations
 */
class OptionChainService {
    constructor(marketDataService) {
        this.marketDataService = marketDataService;
        this.optionChainCache = new Map();
        this.cacheTimeout = 30000; // 30 seconds cache for option chain
        
        // Strike intervals for different indices
        this.strikeIntervals = {
            'NIFTY': 50,
            'BANKNIFTY': 100,
            'FINNIFTY': 50,
            'MIDCPNIFTY': 25
        };
        
        // Default expiry patterns
        this.expiryPatterns = {
            'NIFTY': 'weekly', // Weekly expiry on Thursday
            'BANKNIFTY': 'weekly', // Weekly expiry on Wednesday
            'FINNIFTY': 'weekly', // Weekly expiry on Tuesday
            'MIDCPNIFTY': 'monthly' // Monthly expiry
        };
    }

    /**
     * Get option chain for a symbol with automatic strike discovery
     */
    async getOptionChain(symbol, expiry = null, strikeCount = 5) {
        try {
            const cacheKey = `${symbol}_${expiry}_${strikeCount}`;
            const cached = this.optionChainCache.get(cacheKey);
            
            if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
                return { success: true, data: cached.data };
            }

            // Get current price of the underlying
            const currentQuote = await this.marketDataService.getQuote(symbol);
            if (!currentQuote.success) {
                throw new Error(`Failed to get current price for ${symbol}`);
            }

            const currentPrice = currentQuote.data.ltp;
            const atmStrike = this.getATMStrike(currentPrice, symbol);
            
            // Generate strike prices
            const strikes = this.generateStrikes(atmStrike, symbol, strikeCount);
            
            // Get expiry date if not provided
            if (!expiry) {
                expiry = this.getNextExpiry(symbol);
            }

            // Fetch option chain data
            const optionChainData = await this.fetchOptionChainData(symbol, expiry, strikes);
            
            const result = {
                symbol: symbol,
                underlyingPrice: currentPrice,
                atmStrike: atmStrike,
                expiry: expiry,
                strikes: optionChainData,
                timestamp: new Date(),
                metadata: {
                    strikeInterval: this.strikeIntervals[symbol] || 50,
                    totalStrikes: strikes.length,
                    priceRange: {
                        min: Math.min(...strikes),
                        max: Math.max(...strikes)
                    }
                }
            };

            // Cache the result
            this.optionChainCache.set(cacheKey, {
                data: result,
                timestamp: Date.now()
            });

            return { success: true, data: result };
        } catch (error) {
            console.error('Failed to get option chain:', error);
            return {
                success: false,
                message: error.message || 'Failed to get option chain'
            };
        }
    }

    /**
     * Get ATM (At The Money) strike price
     */
    getATMStrike(currentPrice, symbol) {
        const interval = this.strikeIntervals[symbol] || 50;
        return Math.round(currentPrice / interval) * interval;
    }

    /**
     * Generate strike prices around ATM
     */
    generateStrikes(atmStrike, symbol, strikeCount = 5) {
        const interval = this.strikeIntervals[symbol] || 50;
        const strikes = [];
        
        // Generate strikes below ATM
        for (let i = strikeCount; i >= 1; i--) {
            strikes.push(atmStrike - (i * interval));
        }
        
        // Add ATM strike
        strikes.push(atmStrike);
        
        // Generate strikes above ATM
        for (let i = 1; i <= strikeCount; i++) {
            strikes.push(atmStrike + (i * interval));
        }
        
        return strikes.sort((a, b) => a - b);
    }

    /**
     * Get next expiry date for a symbol
     */
    getNextExpiry(symbol) {
        const now = moment();
        const pattern = this.expiryPatterns[symbol] || 'weekly';
        
        if (pattern === 'weekly') {
            return this.getNextWeeklyExpiry(symbol, now);
        } else {
            return this.getNextMonthlyExpiry(now);
        }
    }

    /**
     * Get next weekly expiry
     */
    getNextWeeklyExpiry(symbol, currentDate) {
        let expiryDay;
        
        switch (symbol) {
            case 'NIFTY':
                expiryDay = 4; // Thursday
                break;
            case 'BANKNIFTY':
                expiryDay = 3; // Wednesday
                break;
            case 'FINNIFTY':
                expiryDay = 2; // Tuesday
                break;
            default:
                expiryDay = 4; // Default to Thursday
        }
        
        let nextExpiry = currentDate.clone().day(expiryDay);
        
        // If expiry day has passed this week, move to next week
        if (nextExpiry.isSameOrBefore(currentDate)) {
            nextExpiry.add(1, 'week');
        }
        
        return nextExpiry.format('YYYY-MM-DD');
    }

    /**
     * Get next monthly expiry (last Thursday of the month)
     */
    getNextMonthlyExpiry(currentDate) {
        let nextExpiry = currentDate.clone().endOf('month');
        
        // Find last Thursday of the month
        while (nextExpiry.day() !== 4) {
            nextExpiry.subtract(1, 'day');
        }
        
        // If monthly expiry has passed, move to next month
        if (nextExpiry.isSameOrBefore(currentDate)) {
            nextExpiry = currentDate.clone().add(1, 'month').endOf('month');
            while (nextExpiry.day() !== 4) {
                nextExpiry.subtract(1, 'day');
            }
        }
        
        return nextExpiry.format('YYYY-MM-DD');
    }

    /**
     * Fetch option chain data from API
     */
    async fetchOptionChainData(symbol, expiry, strikes) {
        try {
            // This would typically call the market data service
            // For now, we'll simulate the structure
            const optionData = [];
            
            for (const strike of strikes) {
                const callSymbol = this.generateOptionSymbol(symbol, expiry, strike, 'CE');
                const putSymbol = this.generateOptionSymbol(symbol, expiry, strike, 'PE');
                
                // Get quotes for both call and put
                const [callQuote, putQuote] = await Promise.all([
                    this.marketDataService.getQuote(callSymbol).catch(() => ({ success: false })),
                    this.marketDataService.getQuote(putSymbol).catch(() => ({ success: false }))
                ]);
                
                const strikeData = {
                    strike: strike,
                    call: callQuote.success ? this.formatOptionData(callQuote.data, 'CE') : null,
                    put: putQuote.success ? this.formatOptionData(putQuote.data, 'PE') : null
                };
                
                optionData.push(strikeData);
            }
            
            return optionData;
        } catch (error) {
            console.error('Failed to fetch option chain data:', error);
            throw error;
        }
    }

    /**
     * Generate option symbol
     */
    generateOptionSymbol(underlying, expiry, strike, optionType) {
        // Format: NIFTY21OCT17000CE or BANKNIFTY21OCT35000PE
        const expiryDate = moment(expiry);
        const year = expiryDate.format('YY');
        const month = expiryDate.format('MMM').toUpperCase();
        
        return `${underlying}${year}${month}${strike}${optionType}`;
    }

    /**
     * Format option data
     */
    formatOptionData(quoteData, optionType) {
        return {
            symbol: quoteData.tradingSymbol || quoteData.symbol,
            type: optionType,
            ltp: quoteData.ltp || 0,
            change: quoteData.change || 0,
            changePercent: quoteData.changePercent || 0,
            volume: quoteData.volume || 0,
            oi: quoteData.oi || 0,
            bid: quoteData.bid || 0,
            ask: quoteData.ask || 0,
            bidQty: quoteData.bidQty || 0,
            askQty: quoteData.askQty || 0,
            iv: quoteData.iv || 0, // Implied Volatility
            delta: quoteData.delta || 0,
            gamma: quoteData.gamma || 0,
            theta: quoteData.theta || 0,
            vega: quoteData.vega || 0
        };
    }

    /**
     * Calculate option Greeks (simplified)
     */
    calculateGreeks(spot, strike, timeToExpiry, riskFreeRate, volatility, optionType) {
        // Simplified Black-Scholes calculations
        // In a real implementation, you would use a proper options pricing library
        
        const d1 = (Math.log(spot / strike) + (riskFreeRate + 0.5 * volatility * volatility) * timeToExpiry) / 
                   (volatility * Math.sqrt(timeToExpiry));
        const d2 = d1 - volatility * Math.sqrt(timeToExpiry);
        
        // Standard normal cumulative distribution function (approximation)
        const normCDF = (x) => {
            return 0.5 * (1 + this.erf(x / Math.sqrt(2)));
        };
        
        // Standard normal probability density function
        const normPDF = (x) => {
            return Math.exp(-0.5 * x * x) / Math.sqrt(2 * Math.PI);
        };
        
        let delta, gamma, theta, vega;
        
        if (optionType === 'CE') {
            delta = normCDF(d1);
        } else {
            delta = normCDF(d1) - 1;
        }
        
        gamma = normPDF(d1) / (spot * volatility * Math.sqrt(timeToExpiry));
        
        if (optionType === 'CE') {
            theta = -(spot * normPDF(d1) * volatility) / (2 * Math.sqrt(timeToExpiry)) - 
                    riskFreeRate * strike * Math.exp(-riskFreeRate * timeToExpiry) * normCDF(d2);
        } else {
            theta = -(spot * normPDF(d1) * volatility) / (2 * Math.sqrt(timeToExpiry)) + 
                    riskFreeRate * strike * Math.exp(-riskFreeRate * timeToExpiry) * normCDF(-d2);
        }
        
        vega = spot * normPDF(d1) * Math.sqrt(timeToExpiry);
        
        return { delta, gamma, theta: theta / 365, vega: vega / 100 };
    }

    /**
     * Error function approximation
     */
    erf(x) {
        const a1 =  0.254829592;
        const a2 = -0.284496736;
        const a3 =  1.421413741;
        const a4 = -1.453152027;
        const a5 =  1.061405429;
        const p  =  0.3275911;
        
        const sign = x >= 0 ? 1 : -1;
        x = Math.abs(x);
        
        const t = 1.0 / (1.0 + p * x);
        const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);
        
        return sign * y;
    }

    /**
     * Get option chain for trading interface
     */
    async getOptionChainForTrading(symbol, strikeCount = 5) {
        const result = await this.getOptionChain(symbol, null, strikeCount);
        
        if (result.success) {
            // Add additional data useful for trading
            result.data.tradingData = {
                recommendations: this.generateTradingRecommendations(result.data),
                riskMetrics: this.calculateRiskMetrics(result.data),
                liquidityAnalysis: this.analyzeLiquidity(result.data)
            };
        }
        
        return result;
    }

    /**
     * Generate trading recommendations
     */
    generateTradingRecommendations(optionChain) {
        const recommendations = [];
        
        // Find high volume options
        optionChain.strikes.forEach(strike => {
            if (strike.call && strike.call.volume > 1000) {
                recommendations.push({
                    type: 'high_volume',
                    option: 'call',
                    strike: strike.strike,
                    reason: 'High trading volume indicates interest'
                });
            }
            
            if (strike.put && strike.put.volume > 1000) {
                recommendations.push({
                    type: 'high_volume',
                    option: 'put',
                    strike: strike.strike,
                    reason: 'High trading volume indicates interest'
                });
            }
        });
        
        return recommendations;
    }

    /**
     * Calculate risk metrics
     */
    calculateRiskMetrics(optionChain) {
        let totalCallOI = 0;
        let totalPutOI = 0;
        let maxCallOI = { strike: 0, oi: 0 };
        let maxPutOI = { strike: 0, oi: 0 };
        
        optionChain.strikes.forEach(strike => {
            if (strike.call) {
                totalCallOI += strike.call.oi;
                if (strike.call.oi > maxCallOI.oi) {
                    maxCallOI = { strike: strike.strike, oi: strike.call.oi };
                }
            }
            
            if (strike.put) {
                totalPutOI += strike.put.oi;
                if (strike.put.oi > maxPutOI.oi) {
                    maxPutOI = { strike: strike.strike, oi: strike.put.oi };
                }
            }
        });
        
        return {
            putCallRatio: totalPutOI / totalCallOI,
            maxCallOI,
            maxPutOI,
            totalOI: totalCallOI + totalPutOI
        };
    }

    /**
     * Analyze liquidity
     */
    analyzeLiquidity(optionChain) {
        const liquidityScores = [];
        
        optionChain.strikes.forEach(strike => {
            const callLiquidity = strike.call ? 
                (strike.call.volume + strike.call.bidQty + strike.call.askQty) : 0;
            const putLiquidity = strike.put ? 
                (strike.put.volume + strike.put.bidQty + strike.put.askQty) : 0;
            
            liquidityScores.push({
                strike: strike.strike,
                callLiquidity,
                putLiquidity,
                totalLiquidity: callLiquidity + putLiquidity
            });
        });
        
        return liquidityScores.sort((a, b) => b.totalLiquidity - a.totalLiquidity);
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.optionChainCache.clear();
    }

    /**
     * Get available expiry dates
     */
    getAvailableExpiries(symbol) {
        const expiries = [];
        const now = moment();
        
        // Add next 4 weekly expiries
        for (let i = 0; i < 4; i++) {
            const expiry = this.getNextWeeklyExpiry(symbol, now.clone().add(i, 'weeks'));
            expiries.push({
                date: expiry,
                label: moment(expiry).format('DD MMM YYYY'),
                type: 'weekly'
            });
        }
        
        // Add next 2 monthly expiries
        for (let i = 0; i < 2; i++) {
            const expiry = this.getNextMonthlyExpiry(now.clone().add(i, 'months'));
            if (!expiries.find(e => e.date === expiry)) {
                expiries.push({
                    date: expiry,
                    label: moment(expiry).format('DD MMM YYYY'),
                    type: 'monthly'
                });
            }
        }
        
        return expiries.sort((a, b) => moment(a.date).diff(moment(b.date)));
    }
}

module.exports = OptionChainService;
