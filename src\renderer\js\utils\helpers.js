/**
 * Helper Utilities
 * Common utility functions used throughout the application
 */

/**
 * Format price with currency symbol
 */
function formatPrice(price, currency = '₹') {
    if (price === null || price === undefined || isNaN(price)) {
        return `${currency}0.00`;
    }
    return `${currency}${parseFloat(price).toFixed(2)}`;
}

/**
 * Format number with commas
 */
function formatNumber(num) {
    if (num === null || num === undefined || isNaN(num)) {
        return '0';
    }
    return parseInt(num).toLocaleString('en-IN');
}

/**
 * Format percentage
 */
function formatPercent(percent, decimals = 2) {
    if (percent === null || percent === undefined || isNaN(percent)) {
        return '0.00%';
    }
    return `${parseFloat(percent).toFixed(decimals)}%`;
}

/**
 * Format change with sign
 */
function formatChange(change, decimals = 2) {
    if (change === null || change === undefined || isNaN(change)) {
        return '0.00';
    }
    const formatted = parseFloat(change).toFixed(decimals);
    return change >= 0 ? `+${formatted}` : formatted;
}

/**
 * Get price change class for styling
 */
function getPriceChangeClass(change) {
    if (change > 0) return 'positive';
    if (change < 0) return 'negative';
    return 'neutral';
}

/**
 * Validate email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Validate mobile number (Indian format)
 */
function isValidMobile(mobile) {
    const mobileRegex = /^[6-9]\d{9}$/;
    return mobileRegex.test(mobile);
}

/**
 * Validate TOTP code
 */
function isValidTOTP(totp) {
    const totpRegex = /^\d{6}$/;
    return totpRegex.test(totp);
}

/**
 * Validate price
 */
function isValidPrice(price) {
    const num = parseFloat(price);
    return !isNaN(num) && num > 0 && num <= 999999.99;
}

/**
 * Validate quantity
 */
function isValidQuantity(quantity) {
    const num = parseInt(quantity);
    return !isNaN(num) && num > 0 && num <= 10000;
}

/**
 * Check if market is open
 */
function isMarketOpen() {
    const now = new Date();
    const day = now.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    const hour = now.getHours();
    const minute = now.getMinutes();
    const time = hour * 100 + minute;

    // Market is closed on weekends
    if (day === 0 || day === 6) {
        return false;
    }

    // Market hours: 9:15 AM to 3:30 PM (IST)
    return time >= 915 && time <= 1530;
}

/**
 * Get market status
 */
function getMarketStatus() {
    const now = new Date();
    const day = now.getDay();
    const hour = now.getHours();
    const minute = now.getMinutes();
    const time = hour * 100 + minute;

    if (day === 0 || day === 6) {
        return { status: 'Closed', class: 'closed' };
    }

    if (time >= 915 && time <= 1530) {
        return { status: 'Open', class: 'open' };
    } else if (time >= 900 && time < 915) {
        return { status: 'Pre-Market', class: 'pre-market' };
    } else if (time > 1530 && time <= 1600) {
        return { status: 'Post-Market', class: 'post-market' };
    } else {
        return { status: 'Closed', class: 'closed' };
    }
}

/**
 * Format date for display
 */
function formatDate(date, format = 'DD MMM YYYY') {
    if (!date) return '';
    
    const d = new Date(date);
    const day = d.getDate().toString().padStart(2, '0');
    const month = d.toLocaleString('en', { month: 'short' });
    const year = d.getFullYear();
    const hour = d.getHours().toString().padStart(2, '0');
    const minute = d.getMinutes().toString().padStart(2, '0');
    const second = d.getSeconds().toString().padStart(2, '0');

    switch (format) {
        case 'DD MMM YYYY':
            return `${day} ${month} ${year}`;
        case 'HH:mm:ss':
            return `${hour}:${minute}:${second}`;
        case 'HH:mm':
            return `${hour}:${minute}`;
        case 'DD MMM YYYY HH:mm':
            return `${day} ${month} ${year} ${hour}:${minute}`;
        case 'YYYY-MM-DD':
            return `${year}-${(d.getMonth() + 1).toString().padStart(2, '0')}-${day}`;
        default:
            return d.toLocaleDateString();
    }
}

/**
 * Get time ago string
 */
function getTimeAgo(date) {
    const now = new Date();
    const diffMs = now - new Date(date);
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSecs < 60) {
        return 'Just now';
    } else if (diffMins < 60) {
        return `${diffMins} min ago`;
    } else if (diffHours < 24) {
        return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else {
        return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    }
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Throttle function
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Deep clone object
 */
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    
    if (obj instanceof Array) {
        return obj.map(item => deepClone(item));
    }
    
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

/**
 * Generate unique ID
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * Calculate option premium
 */
function calculateOptionPremium(spot, strike, timeToExpiry, volatility, riskFreeRate, optionType) {
    // Simplified Black-Scholes calculation
    // In a real implementation, you would use a proper options pricing library
    
    const d1 = (Math.log(spot / strike) + (riskFreeRate + 0.5 * volatility * volatility) * timeToExpiry) / 
               (volatility * Math.sqrt(timeToExpiry));
    const d2 = d1 - volatility * Math.sqrt(timeToExpiry);
    
    // Standard normal cumulative distribution function (approximation)
    const normCDF = (x) => {
        return 0.5 * (1 + erf(x / Math.sqrt(2)));
    };
    
    let premium;
    if (optionType === 'CE') {
        premium = spot * normCDF(d1) - strike * Math.exp(-riskFreeRate * timeToExpiry) * normCDF(d2);
    } else {
        premium = strike * Math.exp(-riskFreeRate * timeToExpiry) * normCDF(-d2) - spot * normCDF(-d1);
    }
    
    return Math.max(0, premium);
}

/**
 * Error function approximation
 */
function erf(x) {
    const a1 =  0.*********;
    const a2 = -0.*********;
    const a3 =  1.*********;
    const a4 = -1.*********;
    const a5 =  1.*********;
    const p  =  0.3275911;
    
    const sign = x >= 0 ? 1 : -1;
    x = Math.abs(x);
    
    const t = 1.0 / (1.0 + p * x);
    const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);
    
    return sign * y;
}

/**
 * Calculate position size based on risk
 */
function calculatePositionSize(accountSize, riskPercent, entryPrice, stopLossPrice) {
    const riskAmount = accountSize * (riskPercent / 100);
    const riskPerShare = Math.abs(entryPrice - stopLossPrice);
    
    if (riskPerShare === 0) {
        return 0;
    }
    
    return Math.floor(riskAmount / riskPerShare);
}

/**
 * Calculate profit/loss
 */
function calculatePnL(entryPrice, currentPrice, quantity, side) {
    const priceDiff = currentPrice - entryPrice;
    const multiplier = side === 'LONG' ? 1 : -1;
    return priceDiff * quantity * multiplier;
}

/**
 * Export data to CSV
 */
function exportToCSV(data, filename) {
    if (!data || data.length === 0) {
        throw new Error('No data to export');
    }
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => {
            const value = row[header];
            // Escape commas and quotes in values
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
        }).join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

/**
 * Show toast notification
 */
function showToast(message, type = 'info', duration = 3000) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    const container = document.getElementById('notification-container') || document.body;
    container.appendChild(toast);
    
    // Animate in
    setTimeout(() => toast.classList.add('show'), 100);
    
    // Remove after duration
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => container.removeChild(toast), 300);
    }, duration);
}

// Export all helper functions
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        formatPrice,
        formatNumber,
        formatPercent,
        formatChange,
        getPriceChangeClass,
        isValidEmail,
        isValidMobile,
        isValidTOTP,
        isValidPrice,
        isValidQuantity,
        isMarketOpen,
        getMarketStatus,
        formatDate,
        getTimeAgo,
        debounce,
        throttle,
        deepClone,
        generateId,
        calculateOptionPremium,
        calculatePositionSize,
        calculatePnL,
        exportToCSV,
        showToast
    };
} else {
    // Browser environment
    window.Helpers = {
        formatPrice,
        formatNumber,
        formatPercent,
        formatChange,
        getPriceChangeClass,
        isValidEmail,
        isValidMobile,
        isValidTOTP,
        isValidPrice,
        isValidQuantity,
        isMarketOpen,
        getMarketStatus,
        formatDate,
        getTimeAgo,
        debounce,
        throttle,
        deepClone,
        generateId,
        calculateOptionPremium,
        calculatePositionSize,
        calculatePnL,
        exportToCSV,
        showToast
    };
}
