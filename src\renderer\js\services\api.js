/**
 * API Service for Renderer Process
 * Handles communication with the main process for API calls
 */
class APIService {
    constructor() {
        this.isInitialized = false;
        this.requestQueue = [];
        this.isProcessingQueue = false;
    }

    /**
     * Initialize the API service
     */
    async initialize() {
        try {
            this.isInitialized = true;
            console.log('API Service initialized');
            return true;
        } catch (error) {
            console.error('Failed to initialize API service:', error);
            return false;
        }
    }

    /**
     * Make API call through electron IPC
     */
    async makeRequest(method, ...args) {
        if (!window.electronAPI) {
            throw new Error('Electron API not available');
        }

        try {
            const result = await window.electronAPI[method](...args);
            return result;
        } catch (error) {
            console.error(`API request failed: ${method}`, error);
            throw error;
        }
    }

    /**
     * Authentication methods
     */
    async authenticate(credentials) {
        return await this.makeRequest('authenticate', credentials);
    }

    async logout() {
        return await this.makeRequest('logout');
    }

    async getUserProfile() {
        return await this.makeRequest('getUserProfile');
    }

    /**
     * Market data methods
     */
    async getMarketData(symbols) {
        return await this.makeRequest('getMarketData', symbols);
    }

    async getOptionChain(params) {
        return await this.makeRequest('getOptionChain', params);
    }

    async getAvailableExpiries(symbol) {
        return await this.makeRequest('getAvailableExpiries', symbol);
    }

    async getMarketDepth(symbol) {
        return await this.makeRequest('getMarketDepth', symbol);
    }

    async getHistoricalData(symbol, interval, fromDate, toDate) {
        return await this.makeRequest('getHistoricalData', symbol, interval, fromDate, toDate);
    }

    async searchInstruments(query) {
        return await this.makeRequest('searchInstruments', query);
    }

    /**
     * Script Master methods
     */
    async getScriptMasterFiles() {
        return await this.makeRequest('getScriptMasterFiles');
    }

    async downloadScriptMaster(fileUrl) {
        return await this.makeRequest('downloadScriptMaster', fileUrl);
    }

    async parseScriptMaster(csvData) {
        return await this.makeRequest('parseScriptMaster', csvData);
    }

    async getTokenBySymbol(symbol, exchange = 'NSE_FO') {
        return await this.makeRequest('getTokenBySymbol', symbol, exchange);
    }

    /**
     * Order management methods
     */
    async placeOrder(orderData) {
        return await this.makeRequest('placeOrder', orderData);
    }

    async modifyOrder(orderId, modifications) {
        return await this.makeRequest('modifyOrder', orderId, modifications);
    }

    async cancelOrder(orderId) {
        return await this.makeRequest('cancelOrder', orderId);
    }

    async getOrderBook() {
        return await this.makeRequest('getOrderBook');
    }

    async getOrderHistory(fromDate, toDate) {
        return await this.makeRequest('getOrderHistory', fromDate, toDate);
    }

    async getScheduledOrders() {
        return await this.makeRequest('getScheduledOrders');
    }

    async cancelScheduledOrder(scheduledOrderId) {
        return await this.makeRequest('cancelScheduledOrder', scheduledOrderId);
    }

    /**
     * Portfolio methods
     */
    async getPortfolio() {
        return await this.makeRequest('getPortfolio');
    }

    async getPositions() {
        return await this.makeRequest('getPositions');
    }

    async getHoldings() {
        return await this.makeRequest('getHoldings');
    }

    async getPortfolioSummary() {
        return await this.makeRequest('getPortfolioSummary');
    }

    /**
     * Trading engine methods
     */
    async startTradingEngine() {
        return await this.makeRequest('startTradingEngine');
    }

    async stopTradingEngine() {
        return await this.makeRequest('stopTradingEngine');
    }

    async getTradingStatus() {
        return await this.makeRequest('getTradingStatus');
    }

    async updateRiskLimits(riskLimits) {
        return await this.makeRequest('updateRiskLimits', riskLimits);
    }

    async emergencyStop() {
        return await this.makeRequest('emergencyStop');
    }

    /**
     * WebSocket methods
     */
    async subscribeToMarketData(symbols) {
        return await this.makeRequest('subscribeMarketData', symbols);
    }

    async unsubscribeFromMarketData(symbols) {
        return await this.makeRequest('unsubscribeMarketData', symbols);
    }

    async initializeWebSocket() {
        return await this.makeRequest('initializeWebSocket');
    }

    async reconnectWebSocket() {
        return await this.makeRequest('reconnectWebSocket');
    }

    async disconnectWebSocket() {
        return await this.makeRequest('disconnectWebSocket');
    }

    /**
     * Settings methods
     */
    async saveSettings(settings) {
        return await this.makeRequest('saveSettings', settings);
    }

    async getSettings() {
        return await this.makeRequest('getSettings');
    }

    async testConnection() {
        return await this.makeRequest('testConnection');
    }

    /**
     * Utility methods
     */
    async showNotification(title, message, type = 'info') {
        return await this.makeRequest('showNotification', { title, message, type });
    }

    async showErrorDialog(title, content) {
        return await this.makeRequest('showErrorDialog', { title, content });
    }

    async showConfirmDialog(title, content) {
        return await this.makeRequest('showConfirmDialog', { title, content });
    }

    async exportData(data, filename) {
        return await this.makeRequest('exportData', { data, filename });
    }

    async importData(fileType) {
        return await this.makeRequest('importData', fileType);
    }

    /**
     * Logging methods
     */
    async logInfo(message) {
        return await this.makeRequest('logInfo', message);
    }

    async logError(message) {
        return await this.makeRequest('logError', message);
    }

    async logWarning(message) {
        return await this.makeRequest('logWarning', message);
    }

    /**
     * Batch operations
     */
    async batchRequest(requests) {
        const results = [];
        
        for (const request of requests) {
            try {
                const result = await this.makeRequest(request.method, ...request.args);
                results.push({ success: true, data: result });
            } catch (error) {
                results.push({ success: false, error: error.message });
            }
        }
        
        return results;
    }

    /**
     * Queue management for rate limiting
     */
    async queueRequest(method, ...args) {
        return new Promise((resolve, reject) => {
            this.requestQueue.push({
                method,
                args,
                resolve,
                reject,
                timestamp: Date.now()
            });
            
            this.processQueue();
        });
    }

    async processQueue() {
        if (this.isProcessingQueue || this.requestQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        while (this.requestQueue.length > 0) {
            const request = this.requestQueue.shift();
            
            try {
                const result = await this.makeRequest(request.method, ...request.args);
                request.resolve(result);
            } catch (error) {
                request.reject(error);
            }

            // Add small delay to prevent overwhelming the API
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        this.isProcessingQueue = false;
    }

    /**
     * Error handling
     */
    handleError(error, context = '') {
        console.error(`API Error ${context}:`, error);
        
        // Show user-friendly error message
        if (error.message.includes('Network')) {
            this.showNotification('Network Error', 'Please check your internet connection', 'error');
        } else if (error.message.includes('Authentication')) {
            this.showNotification('Authentication Error', 'Please login again', 'error');
        } else {
            this.showNotification('Error', error.message || 'An unexpected error occurred', 'error');
        }
    }

    /**
     * Retry mechanism
     */
    async retryRequest(method, args, maxRetries = 3, delay = 1000) {
        let lastError;
        
        for (let i = 0; i < maxRetries; i++) {
            try {
                return await this.makeRequest(method, ...args);
            } catch (error) {
                lastError = error;
                
                if (i < maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
                }
            }
        }
        
        throw lastError;
    }

    /**
     * Health check
     */
    async healthCheck() {
        try {
            const result = await this.makeRequest('healthCheck');
            return result.success;
        } catch (error) {
            return false;
        }
    }

    /**
     * Get API status
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            queueLength: this.requestQueue.length,
            isProcessingQueue: this.isProcessingQueue
        };
    }

    /**
     * Clear request queue
     */
    clearQueue() {
        this.requestQueue.forEach(request => {
            request.reject(new Error('Request cancelled'));
        });
        this.requestQueue = [];
        this.isProcessingQueue = false;
    }

    /**
     * Cleanup
     */
    destroy() {
        this.clearQueue();
        this.isInitialized = false;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = APIService;
}
