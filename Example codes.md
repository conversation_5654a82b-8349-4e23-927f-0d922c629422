for get access code request 
var settings = {
  "url": "https://napi.kotaksecurities.com/oauth2/token",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Authorization": "Basic MW1La1VYcWxIdTZlNEw0NDR6RmluRzdjNnRzYTpENGV1bEZZOXc3X0ZBRWRTWW5vRWY4blI2emth"
  },
  "data": {
    "grant_type": "client_credentials"
  }
};

$.ajax(settings).done(function (response) {
  console.log(response);
});


response 
{
  "access_token": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "scope": "default",
  "token_type": "Bearer",
  "expires_in": *********
}


Example Request
Step 1 - Get View Token

var settings = {
  "url": "https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/login",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/json"
  },
  "data": JSON.stringify({
    "mobileNumber": "+9170******93",
    "ucc": "******",
    "totp": "******"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});

Example Response
{
  "data": {
    "token": "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "sid": "b979ea59-9e86-4c50-83b0-099ea7c11b07",
    "rid": "9e13da1c-5186-4bd0-b0dc-6ab819f71e18",
    "hsServerId": "",
    "isUserPwdExpired": false,
    "ucc": "YOJE0",
    "greetingName": "PRIYANKA",
    "isTrialAccount": false,
    "dataCenter": "E21",
    "searchAPIKey": "",
    "derivativesRiskDisclosure": "Risk Disclosure on Derivatives\n\nAs per a SEBI study dated 25 Jan 2023- \n• 9 out of 10 individual traders in equity Futures and Options Segment, incurred net losses.\n• On an average, loss makers registered net trading loss close to Rs.50,000.\n• Over and above the net trading losses incurred, loss makers expended an additional 28% of net trading losses as transaction costs.\n• Those making net trading profits, incurred between 15% to 50% of such profits as transaction cost.\n\nFor more information please check out : https://www.sebi.gov.in/reports-and-statistics/research/jan-2023/study-analysis-of-profit-and-loss-of-individual-traders-dealing-in-equity-fando-segment_67525.html",
    "mfAccess": 1,
    "dataCenterMap": null,
    "dormancyStatus": "A",
    "asbaStatus": "",
    "clientType": "RI",
    "isNRI": false,
    "kId": "AVRPC7535J",
    "kType": "View",
    "status": "success",
    "incRange": 0,
    "incUpdFlag": "",
    "clientGroup": ""
  }
}
Headers
Date
Wed, 19 Mar 2025 05:46:07 GMT

Content-Type
application/json

Transfer-Encoding
chunked

Connection
keep-alive

kpid
login-service-5f774746f8-vsr9z

access-control-allow-origin
*

access-control-allow-methods
POST

x-content-type-options
nosniff

x-correlation-id
17716df5-2d6a-49eb-a92b-35109a6c2bdd

server-timing
intid;desc=2ffafc854c3258bf

x-instana-s
2ffafc854c3258bf

x-instana-l
1

access-control-allow-headers
authorization,Access-Control-Allow-Origin,Content-Type,SOAPAction,apikey,Internal-Key,Auth,Sid,sid,custom_header,Authorization

traceparent
00-00000000000000002ffafc854c3258bf-2ffafc854c3258bf-01

x-frame-options
deny

activityid
29ce01aa-603a-44fb-93c1-e9b0fcb8c6cc

access-control-expose-headers
strict-transport-security
max-age=15552000; includeSubDomains

Cache-Control
no-store, no-cache, private, max-age=0

x-instana-t
2ffafc854c3258bf

content-security-policy
default-src 'self';

x-xss-protection
1; mode=block

tracestate
in=2ffafc854c3258bf;2ffafc854c3258bf

cf-cache-status
DYNAMIC

Server
cloudflare

CF-RAY
922aaae70836ff6f-BOM

Content-Encoding
gzip



Example Request
Step 2 - Get Final Session Token
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/validate",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "sid": "b979ea59-9e86-4c50-83b0-099ea7c11b07",
    "Auth": "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/json",
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": JSON.stringify({
    "mpin": "******"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});

Example Response
{
  "data": {
    "token": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "sid": "8a43c2a9-004f-4186-9d9e-824c07014f60",
    "rid": "9e13da1c-5186-4bd0-b0dc-6ab819f71e18",
    "hsServerId": "",
    "isUserPwdExpired": false,
    "ucc": "YOJE0",
    "greetingName": "PRIYANKA",
    "isTrialAccount": false,
    "dataCenter": "E21",
    "searchAPIKey": "",
    "derivativesRiskDisclosure": "Risk Disclosure on Derivatives\n\nAs per a SEBI study dated 25 Jan 2023- \n• 9 out of 10 individual traders in equity Futures and Options Segment, incurred net losses.\n• On an average, loss makers registered net trading loss close to Rs.50,000.\n• Over and above the net trading losses incurred, loss makers expended an additional 28% of net trading losses as transaction costs.\n• Those making net trading profits, incurred between 15% to 50% of such profits as transaction cost.\n\nFor more information please check out : https://www.sebi.gov.in/reports-and-statistics/research/jan-2023/study-analysis-of-profit-and-loss-of-individual-traders-dealing-in-equity-fando-segment_67525.html",
    "mfAccess": 1,
    "dataCenterMap": null,
    "dormancyStatus": "A",
    "asbaStatus": "",
    "clientType": "RI",
    "isNRI": false,
    "kId": "AVRPC7535J",
    "kType": "Trade",
    "status": "success",
    "incRange": 0,
    "incUpdFlag": "",
    "clientGroup": ""
  }
}

Header 
Date
Wed, 19 Mar 2025 05:50:45 GMT

Content-Type
application/json

Transfer-Encoding
chunked

Connection
keep-alive

kpid
login-service-5f774746f8-thtpq

access-control-allow-origin
*

access-control-allow-methods
POST

x-content-type-options
nosniff

x-correlation-id
f7529a9f-4e59-417e-a06a-b706cdbfab48

server-timing
intid;desc=33d112475727d29e

x-instana-s
33d112475727d29e

x-instana-l
1

access-control-allow-headers
authorization,Access-Control-Allow-Origin,Content-Type,SOAPAction,apikey,Internal-Key,Auth,Sid,sid,custom_header,Authorization

traceparent
00-000000000000000033d112475727d29e-33d112475727d29e-01

x-frame-options
deny

activityid
b3e1dffa-f116-4b72-b53d-378cf5cacc9a

access-control-expose-headers
strict-transport-security
max-age=15552000; includeSubDomains

Cache-Control
no-store, no-cache, private, max-age=0

x-instana-t
33d112475727d29e

content-security-policy
default-src 'self';

x-xss-protection
1; mode=block

tracestate
in=33d112475727d29e;33d112475727d29e

cf-cache-status
DYNAMIC

Server
cloudflare

CF-RAY
922ab1a48ed73e57-BOM

Content-Encoding
gzip


Script master Example Request
Filename

var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Files/1.0/masterscrip/v2/file-paths",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "accept": "*/*",
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});

Example response
{
  "data": {
    "filesPaths": [
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed/cde_fo.csv",
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed/mcx_fo.csv",
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed/nse_fo.csv",
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed/bse_fo.csv",
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed-v1/nse_cm-v1.csv",
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed-v1/bse_cm-v1.csv"
    ],
    "baseFolder": "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod"
  }
}


Example Request
Place Order

var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/rule/ms/place",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Sid": "8a43c2a9-004f-4186-9d9e-824c07014f60",
    "Auth": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": {
    "jData": "{\"am\":\"NO\", \"dq\":\"0\",\"es\":\"nse_cm\", \"mp\":\"0\", \"pc\":\"CNC\", \"pf\":\"N\", \"pr\":\"6.50\", \"pt\":\"L\", \"qt\":\"1\", \"rt\":\"DAY\", \"tp\":\"0\", \"ts\":\"IDEA-EQ\", \"tt\":\"B\"}"
  }
};

$.ajax(settings).done(function (response) {
  console.log(response);
});

Example response 
{
  "nOrdNo": "250319000343209",
  "stat": "Ok",
  "stCode": 200
}

Example Request
Modify Order
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/vr/modify",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Auth": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "Sid": "1f47bf35-625b-4d17-a9de-35d827ec2aa2",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": "Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": {
    "jData": "{\"tk\":\"14366\", \"mp\":\"0\", \"pc\":\"CNC\", \"dd\":\"NA\", \"dq\":\"0\", \"vd\":\"DAY\", \"ts\":\"IDEA-EQ\", \"tt\":\"B\", \"pr\":\"3001\", \"tp\":\"0\", \"qt\":\"2\", \"no\":\"240702002199220\", \"es\":\"nse_cm\", \"pt\":\"L\"}"
  }
};

$.ajax(settings).done(function (response) {
  console.log(response);
});

Example response 
{
  "stat": "Ok",
  "nOrdNo": "220621000000097",
  "stCode": 200
}


Example Request
Cancel Order

var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/cancel",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Sid": "b3ebb6af-7205-43e9-8513-189472393cab",
    "Auth": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": "Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": {
    "jData": "{\"on\":\"220621000000097\"}"
  }
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
 example response 
 {
  "stat": "Ok",
  "result": "220621000000097",
  "stCode": 200
}
